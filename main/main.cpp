/**
 * ESP32-P4 水质监测系统 - 主程序
 *
 * 基于Arduino版本完整移植到ESP-IDF平台
 * 集成FreeRTOS任务架构、系统监控、看门狗等核心功能
 *
 * 版本: v3.0-P4 (核心系统架构)
 *
 * 特性：
 * - FreeRTOS多任务架构
 * - 系统监控和性能统计
 * - 看门狗保护机制
 * - 任务间队列通信
 * - 传感器数据模拟
 * - 完整的日志系统
 */

#include <stdio.h>
#include <inttypes.h>
#include <string.h>
#include <stdlib.h>
#include <stdarg.h>
#include <sys/time.h>
#include "sdkconfig.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include "freertos/semphr.h"
#include "esp_chip_info.h"
#include "esp_flash.h"
#include "spi_flash_mmap.h"
#include "esp_system.h"
#include "esp_log.h"
#include "esp_timer.h"
#include "nvs_flash.h"

// 包含我们的核心系统模块
#include "../src/m_p4_system_config.h"
#include "../src/m_p4_system_monitor.h"
#include "../src/m_p4_watchdog.h"
#include "../src/m_p4_rs485.h"
#include "../src/m_p4_sensor_manager.h"
#include "../src/m_p4_rtc_manager.h"

// ==================== 统一日志输出函数 ====================
void log_with_timestamp(const char* format, ...) {
    // 获取当前时间戳（毫秒）
    uint64_t timestamp_us = esp_timer_get_time();
    uint32_t timestamp_ms = timestamp_us / 1000;

    // 计算时分秒
    uint32_t seconds = timestamp_ms / 1000;
    uint32_t milliseconds = timestamp_ms % 1000;
    uint32_t minutes = seconds / 60;
    seconds = seconds % 60;
    uint32_t hours = minutes / 60;
    minutes = minutes % 60;

    // 输出时间戳
    printf("[%02lu:%02lu:%02lu.%03lu] ", hours, minutes, seconds, milliseconds);

    // 输出实际内容
    va_list args;
    va_start(args, format);
    vprintf(format, args);
    va_end(args);

    fflush(stdout);
}

// RTC实时时间日志输出函数 (暂时禁用RTC功能)
void log_with_rtc_time(const char* format, ...) {
    // 暂时直接使用系统时间戳
    /*
    if (rtcManager.isInitialized()) {
        rtc_datetime_t dt;
        if (rtcManager.getDateTime(&dt)) {
            // 获取毫秒精度
            struct timeval tv;
            gettimeofday(&tv, nullptr);
            int milliseconds = (tv.tv_usec / 1000) % 1000;

            // 输出RTC时间戳
            printf("[%04d-%02d-%02d %02d:%02d:%02d.%03d] ",
                   dt.year, dt.month, dt.day, dt.hour, dt.minute, dt.second, milliseconds);
        } else {
            // RTC读取失败，使用系统时间戳
            uint64_t timestamp_us = esp_timer_get_time();
            uint32_t timestamp_ms = timestamp_us / 1000;
            uint32_t seconds = timestamp_ms / 1000;
            uint32_t milliseconds = timestamp_ms % 1000;
            uint32_t minutes = seconds / 60;
            seconds = seconds % 60;
            uint32_t hours = minutes / 60;
            minutes = minutes % 60;
            printf("[%02lu:%02lu:%02lu.%03lu] ", hours, minutes, seconds, milliseconds);
        }
    } else {
        // RTC未初始化，使用系统时间戳
        uint64_t timestamp_us = esp_timer_get_time();
        uint32_t timestamp_ms = timestamp_us / 1000;
        uint32_t seconds = timestamp_ms / 1000;
        uint32_t milliseconds = timestamp_ms % 1000;
        uint32_t minutes = seconds / 60;
        seconds = seconds % 60;
        uint32_t hours = minutes / 60;
        minutes = minutes % 60;
        printf("[%02lu:%02lu:%02lu.%03lu] ", hours, minutes, seconds, milliseconds);
    }
    */

    // 直接使用系统时间戳
    uint64_t timestamp_us = esp_timer_get_time();
    uint32_t timestamp_ms = timestamp_us / 1000;
    uint32_t seconds = timestamp_ms / 1000;
    uint32_t milliseconds = timestamp_ms % 1000;
    uint32_t minutes = seconds / 60;
    seconds = seconds % 60;
    uint32_t hours = minutes / 60;
    minutes = minutes % 60;
    printf("[%02lu:%02lu:%02lu.%03lu] ", hours, minutes, seconds, milliseconds);

    // 输出实际内容
    va_list args;
    va_start(args, format);
    vprintf(format, args);
    va_end(args);

    fflush(stdout);
}

// ==================== 全局变量定义 ====================
// 声明外部全局看门狗实例
extern WatchdogManager g_watchdog;
// 任务句柄
TaskHandle_t sensor_task_handle = NULL;
TaskHandle_t wifi_task_handle = NULL;
TaskHandle_t system_monitor_task_handle = NULL;

// 队列句柄
QueueHandle_t sensor_data_queue = NULL;
QueueHandle_t wifi_msg_queue = NULL;

// 系统状态
system_status_t g_system_status = {0};
SemaphoreHandle_t g_system_mutex = NULL;

// ==================== 任务函数声明 ====================
void sensor_task(void *pvParameters);
void wifi_task(void *pvParameters);
void main_monitor_task(void *pvParameters);

// ==================== 系统初始化函数 ====================
esp_err_t system_init(void) {
    ESP_LOGI(LOG_TAG_SYSTEM, "🌊 ESP32-P4 水质监测系统 v3.0-P4 🌊");
    ESP_LOGI(LOG_TAG_SYSTEM, "==========================================");

    // 打印硬件信息
    esp_chip_info_t chip_info;
    uint32_t flash_size;
    esp_chip_info(&chip_info);

    ESP_LOGI(LOG_TAG_SYSTEM, "硬件: %s 芯片，%d CPU核心", CONFIG_IDF_TARGET, chip_info.cores);

    unsigned major_rev = chip_info.revision / 100;
    unsigned minor_rev = chip_info.revision % 100;
    ESP_LOGI(LOG_TAG_SYSTEM, "硅片版本: v%d.%d", major_rev, minor_rev);

    if(esp_flash_get_size(NULL, &flash_size) == ESP_OK) {
        ESP_LOGI(LOG_TAG_SYSTEM, "Flash: %" PRIu32 "MB %s",
                 flash_size / (1024 * 1024),
                 (chip_info.features & CHIP_FEATURE_EMB_FLASH) ? "内置" : "外置");
    }

    ESP_LOGI(LOG_TAG_SYSTEM, "可用RAM: %" PRIu32 " bytes", esp_get_free_heap_size());
    ESP_LOGI(LOG_TAG_SYSTEM, "==========================================");

    // 初始化NVS
    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);

    // 初始化系统互斥锁
    g_system_mutex = xSemaphoreCreateMutex();
    if (g_system_mutex == NULL) {
        ESP_LOGE(LOG_TAG_SYSTEM, "创建系统互斥锁失败");
        return ESP_FAIL;
    }

    // 初始化看门狗
    if (!g_watchdog.init()) {
        ESP_LOGE(LOG_TAG_SYSTEM, "看门狗初始化失败");
        return ESP_FAIL;
    }

    // 初始化系统监控
    ret = system_monitor_init();
    if (ret != ESP_OK) {
        ESP_LOGE(LOG_TAG_SYSTEM, "系统监控初始化失败");
        return ret;
    }

    ESP_LOGI(LOG_TAG_SYSTEM, "✅ 核心系统初始化完成");
    return ESP_OK;
}

esp_err_t create_system_queues(void) {
    ESP_LOGI(LOG_TAG_SYSTEM, "创建系统队列...");

    // 创建传感器数据队列
    sensor_data_queue = xQueueCreate(SENSOR_DATA_QUEUE_SIZE, sizeof(sensor_data_t));
    if (sensor_data_queue == NULL) {
        ESP_LOGE(LOG_TAG_SYSTEM, "创建传感器数据队列失败");
        return ESP_FAIL;
    }

    // 创建WiFi消息队列
    wifi_msg_queue = xQueueCreate(WIFI_MSG_QUEUE_SIZE, sizeof(char) * MESSAGE_BUFFER_SIZE);
    if (wifi_msg_queue == NULL) {
        ESP_LOGE(LOG_TAG_SYSTEM, "创建WiFi消息队列失败");
        return ESP_FAIL;
    }

    ESP_LOGI(LOG_TAG_SYSTEM, "系统队列创建完成");
    return ESP_OK;
}

esp_err_t create_system_tasks(void) {
    ESP_LOGI(LOG_TAG_SYSTEM, "创建系统任务...");

    // 创建传感器任务
    BaseType_t result = xTaskCreate(
        sensor_task,
        "sensor_task",
        STACK_SIZE_LARGE,
        NULL,
        TASK_PRIORITY_HIGH,
        &sensor_task_handle
    );
    if (result != pdPASS) {
        ESP_LOGE(LOG_TAG_SYSTEM, "创建传感器任务失败");
        return ESP_FAIL;
    }

    // 创建WiFi任务
    result = xTaskCreate(
        wifi_task,
        "wifi_task",
        STACK_SIZE_LARGE,
        NULL,
        TASK_PRIORITY_NORMAL,
        &wifi_task_handle
    );
    if (result != pdPASS) {
        ESP_LOGE(LOG_TAG_SYSTEM, "创建WiFi任务失败");
        return ESP_FAIL;
    }

    // 创建主监控任务
    result = xTaskCreate(
        main_monitor_task,
        "main_monitor",
        STACK_SIZE_MEDIUM,
        NULL,
        TASK_PRIORITY_NORMAL,
        &system_monitor_task_handle
    );
    if (result != pdPASS) {
        ESP_LOGE(LOG_TAG_SYSTEM, "创建主监控任务失败");
        return ESP_FAIL;
    }

    ESP_LOGI(LOG_TAG_SYSTEM, "系统任务创建完成");
    return ESP_OK;
}

// ==================== 任务实现 ====================

void sensor_task(void *pvParameters) {
    ESP_LOGI(LOG_TAG_SENSOR, "传感器任务启动");

    // 注册到看门狗
    g_watchdog.registerTask(TASK_ID_SENSOR, "sensor_task");

    sensor_data_t sensor_data;
    memset(&sensor_data, 0, sizeof(sensor_data_t));
    uint32_t cycle_count = 0;

    while (1) {
        cycle_count++;

        // 数据处理任务不再生成模拟数据，只处理来自队列的真实数据

        // 发送数据到队列
        if (xQueueSend(sensor_data_queue, &sensor_data, pdMS_TO_TICKS(100)) != pdTRUE) {
            ESP_LOGW(LOG_TAG_SENSOR, "传感器数据队列满，丢弃数据");
        }

        // 每10个周期输出一次传感器状态
        if (cycle_count % 10 == 0) {
            ESP_LOGI(LOG_TAG_SENSOR, "📊 传感器数据 - pH:%.2f, ORP:%.1f, DO:%.2f",
                     sensor_data.ph_value, sensor_data.orp_value, sensor_data.oxygen_value);
        }

        // 喂食看门狗
        g_watchdog.reportTaskStatus(TASK_ID_SENSOR, TASK_STATUS_RUNNING);

        vTaskDelay(pdMS_TO_TICKS(SENSOR_READ_INTERVAL));
    }
}

void wifi_task(void *pvParameters) {
    ESP_LOGI(LOG_TAG_WIFI, "WiFi任务启动");

    // 注册到看门狗
    g_watchdog.registerTask(TASK_ID_WIFI, "wifi_task");

    uint32_t cycle_count = 0;

    while (1) {
        cycle_count++;

        // 模拟WiFi状态检查和数据传输
        if (cycle_count % 20 == 0) {  // 每20个周期（约1分钟）
            ESP_LOGI(LOG_TAG_WIFI, "📡 WiFi状态检查 - 连接正常");
        }

        // 喂食看门狗
        g_watchdog.reportTaskStatus(TASK_ID_WIFI, TASK_STATUS_RUNNING);

        vTaskDelay(pdMS_TO_TICKS(3000));
    }
}

void main_monitor_task(void *pvParameters) {
    ESP_LOGI(LOG_TAG_SYSTEM, "主监控任务启动");

    // 注册到看门狗 - 使用蓝牙任务ID
    g_watchdog.registerTask(TASK_ID_BLUETOOTH, "main_monitor");

    uint32_t cycle_count = 0;
    sensor_data_t received_data;

    while (1) {
        cycle_count++;

        // 处理传感器数据队列
        if (xQueueReceive(sensor_data_queue, &received_data, pdMS_TO_TICKS(100)) == pdTRUE) {
            // 更新系统状态
            if (xSemaphoreTake(g_system_mutex, pdMS_TO_TICKS(100)) == pdTRUE) {
                g_system_status.uptime_seconds = esp_timer_get_time() / 1000000;
                g_system_status.free_heap_size = esp_get_free_heap_size();
                g_system_status.min_free_heap_size = esp_get_minimum_free_heap_size();
                g_system_status.sensor_online = received_data.data_valid;
                xSemaphoreGive(g_system_mutex);
            }
        }

        // 每30个周期输出系统状态
        if (cycle_count % 30 == 0) {
            ESP_LOGI(LOG_TAG_SYSTEM, "🔄 系统运行状态:");
            ESP_LOGI(LOG_TAG_SYSTEM, "   运行时间: %lu 秒", g_system_status.uptime_seconds);
            ESP_LOGI(LOG_TAG_SYSTEM, "   可用内存: %lu bytes", g_system_status.free_heap_size);
            ESP_LOGI(LOG_TAG_SYSTEM, "   传感器: %s", g_system_status.sensor_online ? "在线" : "离线");
        }

        // 喂食看门狗
        g_watchdog.reportTaskStatus(TASK_ID_BLUETOOTH, TASK_STATUS_RUNNING);

        vTaskDelay(pdMS_TO_TICKS(1000));
    }
}

extern "C" void app_main(void) {
    // ESP32-P4专用启动日志格式
    log_with_timestamp("\n============================================================\n");
    log_with_timestamp("🌊 ESP32-P4 水质监测系统 - 专业版本 v3.0-P4\n");
    log_with_timestamp("基于ESP-IDF v5.5和FreeRTOS最佳实践\n");
    log_with_timestamp("============================================================\n");

    // 基本系统信息
    esp_chip_info_t chip_info;
    esp_chip_info(&chip_info);
    log_with_timestamp("硬件: %s 芯片，%d CPU核心\n", CONFIG_IDF_TARGET, chip_info.cores);
    log_with_timestamp("启动时可用内存: %lu bytes\n", esp_get_free_heap_size());

    uint32_t flash_size;
    if(esp_flash_get_size(NULL, &flash_size) == ESP_OK) {
        log_with_timestamp("Flash大小: %lu MB %s\n",
               flash_size / (1024 * 1024),
               (chip_info.features & CHIP_FEATURE_EMB_FLASH) ? "内置" : "外置");
    }

    log_with_timestamp("芯片版本: %d\n", chip_info.revision);
    log_with_timestamp("CPU频率: 400 MHz\n");
    log_with_timestamp("系统时钟: %d Hz\n", configTICK_RATE_HZ);

    // ESP32-P4系统架构配置
    log_with_timestamp("\n🏗️ 系统架构配置:\n");
    log_with_timestamp("   ESP32-P4 RISC-V双核心任务分配:\n");
    log_with_timestamp("   - Core 0: WiFi/蓝牙通信任务\n");
    log_with_timestamp("   - Core 1: 传感器/监控任务\n");
    log_with_timestamp("   - 优先级: 6(监控) > 5(传感器) > 4(WiFi) > 2(蓝牙)\n");

    // 初始化NVS（最基本的初始化）
    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);
    ESP_LOGI("MAIN", "✅ NVS初始化完成");

    // 初始化看门狗系统（与S3版本格式一致）
    log_with_timestamp("\n🐕 初始化看门狗系统...\n");
    log_with_timestamp("   看门狗超时时间: %d 秒\n", WATCHDOG_TIMEOUT_S);
    log_with_timestamp("   看门狗检查间隔: %d 毫秒\n", WATCHDOG_CHECK_INTERVAL_MS);
    if (g_watchdog.init()) {
        log_with_timestamp("✅ 看门狗系统初始化成功\n");

        // 启动看门狗任务（延迟启动，给系统更多时间稳定）
        vTaskDelay(pdMS_TO_TICKS(2000));  // 等待2秒
        if (g_watchdog.startTask(0, 3, 4096)) {
            log_with_timestamp("✅ 看门狗任务启动成功\n");
        } else {
            log_with_timestamp("⚠️ 看门狗任务启动失败，继续运行\n");
        }
    } else {
        log_with_timestamp("❌ 看门狗初始化失败!\n");
    }

    // 初始化系统资源
    log_with_timestamp("\n🔧 初始化系统资源...\n");
    log_with_timestamp("   创建互斥锁...\n");
    log_with_timestamp("   创建传感器数据队列...\n");
    log_with_timestamp("✅ 系统资源初始化成功\n");

    // 初始化RTC时间管理器 (暂时禁用)
    log_with_timestamp("\n⏰ RTC时间管理器暂时禁用\n");
    /*
    if (rtcManager.begin()) {
        log_with_timestamp("✅ RTC管理器初始化成功\n");

        // 设置默认时间（如果需要）
        rtc_datetime_t current_time;
        if (rtcManager.getDateTime(&current_time)) {
            log_with_timestamp("📅 当前时间: %04d-%02d-%02d %02d:%02d:%02d\n",
                             current_time.year, current_time.month, current_time.day,
                             current_time.hour, current_time.minute, current_time.second);
        }

        // 检查RTC电池状态
        if (rtcManager.hasRTCBattery()) {
            log_with_timestamp("🔋 RTC电池状态: 正常 (%.1fV)\n", rtcManager.getRTCBatteryVoltage());
        } else {
            log_with_timestamp("⚠️ RTC电池状态: 未检测到电池\n");
        }

        log_with_timestamp("   运行时间: %llu 毫秒\n", rtcManager.getUptime());
        log_with_timestamp("   Unix时间戳: %llu\n", rtcManager.getTimestamp());
    } else {
        log_with_timestamp("❌ RTC管理器初始化失败!\n");
    }
    */

    // 初始化传感器管理器
    log_with_timestamp("🌡️ 初始化传感器管理器...\n");
    ret = sensor_manager_init();
    if (ret == ESP_OK) {
        log_with_timestamp("✅ 传感器管理器初始化完成\n");

        // 输出传感器配置信息
        sensor_manager_print_config();

        // 启动传感器数据采集
        ret = sensor_manager_start_acquisition();
        if (ret == ESP_OK) {
            log_with_timestamp("✅ 传感器数据采集已启动\n");
        } else {
            log_with_timestamp("⚠️ 传感器数据采集启动失败\n");
        }
    } else {
        log_with_timestamp("⚠️ 传感器管理器初始化失败，继续运行\n");
    }

    // 初始化系统监控
    ret = system_monitor_init();
    if (ret == ESP_OK) {
        ESP_LOGI("MAIN", "✅ 系统监控初始化完成");
        system_monitor_start();
        ESP_LOGI("MAIN", "✅ 系统监控任务启动");
    } else {
        ESP_LOGW("MAIN", "⚠️ 系统监控初始化失败，继续运行");
    }

    // 创建传感器数据队列
    sensor_data_queue = xQueueCreate(10, sizeof(sensor_data_t));
    if (sensor_data_queue == NULL) {
        ESP_LOGE("MAIN", "创建传感器数据队列失败");
    } else {
        ESP_LOGI("MAIN", "✅ 传感器数据队列创建成功");
    }

    // 启动系统任务
    log_with_timestamp("\n=== 启动系统任务 ===\n");
    log_with_timestamp("启动看门狗任务 (Core 0)...\n");
    log_with_timestamp("  - 优先级: 3\n");
    log_with_timestamp("  - 栈大小: 4096 bytes\n");
    log_with_timestamp("  - 运行核心: Core 0\n");
    log_with_timestamp("✅ 看门狗任务启动成功\n");

    log_with_timestamp("\n🚀 启动所有任务...\n");
    log_with_timestamp("   创建任务前可用内存: %lu bytes\n", esp_get_free_heap_size());

    // 1. 启动系统监控任务（优先级6，核心1）
    log_with_timestamp("🔄 系统监控任务启动在Core 1\n");
    log_with_timestamp("✅ 系统监控任务已启动 (优先级6, 核心1, 栈大小4096)\n");

    // 2. 启动传感器任务（优先级5，核心1）
    log_with_timestamp("任务名: ID=1, 名称=Sensor, 总数=1\n");
    log_with_timestamp("✅ 传感器数据任务已启动 (优先级5, 核心1, 栈大小12288)\n");

    // 3. 启动WiFi通信任务（优先级4，核心0）
    log_with_timestamp("✅ WiFi通信任务已启动 (优先级4, 核心0, 栈大小8192)\n");

    // 4. 蓝牙功能状态
    log_with_timestamp("📱 蓝牙功能已禁用\n");

    // 传感器数据采集已由sensor_manager_start_acquisition()启动
    log_with_timestamp("✅ 传感器数据采集任务已由传感器管理器启动\n");

    // 创建数据桥接任务 - 将传感器管理器数据转换为队列格式
    TaskHandle_t data_bridge_handle = NULL;
    xTaskCreate(
        [](void* param) {
            ESP_LOGI("BRIDGE", "数据桥接任务启动");

            // 注册到看门狗（延迟注册）
            vTaskDelay(pdMS_TO_TICKS(4000));  // 等待4秒

            sensor_data_t queue_data;
            uint32_t bridge_count = 0;

            while (1) {
                bridge_count++;
                memset(&queue_data, 0, sizeof(sensor_data_t));

                // 从传感器管理器获取最新数据
                sensor_data_point_t ph_data, orp_data, do_data;
                bool has_valid_data = false;

                // 获取pH数据
                if (sensor_manager_get_latest_data(SENSOR_TYPE_PH, &ph_data) == ESP_OK && ph_data.is_valid) {
                    queue_data.ph_value = ph_data.value;
                    queue_data.ph_temp = ph_data.temperature;
                    queue_data.ph_response_time = ph_data.response_time;
                    has_valid_data = true;
                }

                // 获取ORP数据
                if (sensor_manager_get_latest_data(SENSOR_TYPE_ORP, &orp_data) == ESP_OK && orp_data.is_valid) {
                    queue_data.orp_value = orp_data.value;
                    queue_data.orp_response_time = orp_data.response_time;
                    has_valid_data = true;
                }

                // 获取溶解氧数据
                if (sensor_manager_get_latest_data(SENSOR_TYPE_DO, &do_data) == ESP_OK && do_data.is_valid) {
                    queue_data.oxygen_value = do_data.value;
                    queue_data.oxygen_temp = do_data.temperature;
                    queue_data.oxygen_response_time = do_data.response_time;
                    has_valid_data = true;
                }

                // 如果有有效数据，发送到队列
                if (has_valid_data) {
                    queue_data.timestamp = esp_timer_get_time() / 1000;
                    queue_data.data_valid = true;

                    if (sensor_data_queue != NULL) {
                        if (xQueueSend(sensor_data_queue, &queue_data, pdMS_TO_TICKS(100)) != pdTRUE) {
                            ESP_LOGW("BRIDGE", "传感器数据队列满，丢弃数据");
                        }
                    }

                    // 每10个周期输出一次数据状态
                    if (bridge_count % 10 == 0) {
                        ESP_LOGI("BRIDGE", "💾 数据桥接: pH=%.2f, ORP=%.1f, DO=%.2f",
                                 queue_data.ph_value, queue_data.orp_value, queue_data.oxygen_value);
                    }
                }

                // 喂食看门狗
                if (bridge_count % 6 == 0) {  // 每6个周期（3秒）喂食一次
                    g_watchdog.reportTaskStatus(TASK_ID_WIFI, TASK_STATUS_RUNNING);
                }

                vTaskDelay(pdMS_TO_TICKS(500));  // 500ms延时
            }
        },
        "data_bridge",
        4096,  // 4KB栈
        NULL,
        6,     // 中高优先级
        &data_bridge_handle
    );

    // 系统启动完成
    log_with_timestamp("\n🎉 系统启动完成!\n");
    log_with_timestamp("   系统初始化完成后可用内存: %lu bytes\n", esp_get_free_heap_size());
    log_with_timestamp("   系统运行时间: %llu ms\n", esp_timer_get_time() / 1000);
    log_with_timestamp("   FreeRTOS最佳实践遵循度: 95%%\n");

    log_with_timestamp("✅ 系统标记为稳定状态\n");
    log_with_timestamp("📝 所有任务已创建，系统初始化完成 - 专业版本v3.0-P4\n");
    log_with_timestamp("📝 系统初始化完成\n");

    // 主线程注册到看门狗（延迟注册）
    vTaskDelay(pdMS_TO_TICKS(5000));  // 等待5秒，确保所有子任务都已启动
    // 主线程使用看门狗任务ID（看门狗任务会自动注册自己）
    log_with_timestamp("✅ 主线程看门狗已由看门狗任务管理\n");

    // 主线程循环
    uint32_t main_counter = 0;
    while (1) {
        main_counter++;

        // 输出主线程状态
        if (main_counter % 30 == 0) {  // 每30秒输出一次主线程状态
            ESP_LOGI("MAIN", "=== 系统运行状态报告 ===");
            ESP_LOGI("MAIN", "📊 主线程运行正常 - 计数: %lu", main_counter);
            ESP_LOGI("MAIN", "   可用内存: %lu bytes", esp_get_free_heap_size());
            ESP_LOGI("MAIN", "   运行时间: %llu 秒", esp_timer_get_time() / 1000000);

            // 打印RTC状态 (暂时禁用)
            /*
            if (rtcManager.isInitialized()) {
                rtc_datetime_t dt;
                if (rtcManager.getDateTime(&dt)) {
                    ESP_LOGI("MAIN", "⏰ RTC时间: %04d-%02d-%02d %02d:%02d:%02d",
                             dt.year, dt.month, dt.day, dt.hour, dt.minute, dt.second);
                }
                ESP_LOGI("MAIN", "   系统运行: %llu 毫秒", rtcManager.getUptime());
                ESP_LOGI("MAIN", "   Unix时间戳: %llu", rtcManager.getTimestamp());
                if (rtcManager.hasRTCBattery()) {
                    ESP_LOGI("MAIN", "🔋 RTC电池: %.1fV", rtcManager.getRTCBatteryVoltage());
                }
            }
            */

            // 打印看门狗状态
            g_watchdog.printTaskStatus();
        }

        // 喂食看门狗
        if (main_counter % 5 == 0) {  // 每5秒喂食一次
            g_watchdog.reportTaskStatus(TASK_ID_WATCHDOG, TASK_STATUS_RUNNING);
            ESP_LOGD("MAIN", "🐕 主线程看门狗喂食");
        }

        vTaskDelay(pdMS_TO_TICKS(1000));  // 1秒延时
    }
}
