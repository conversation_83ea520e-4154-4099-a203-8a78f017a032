Command: /Users/<USER>/.espressif/python_env/idf5.5_py3.9_env/bin/python /Users/<USER>/esp/v5.5/esp-idf/tools/idf_monitor.py -p /dev/cu.wchusbserial5A7A0449951 -b 115200 --toolchain-prefix riscv32-esp-elf- --target esp32p4 --revision 1 --decode-panic backtrace /Users/<USER>/Documents/Arduino/libraries/ESP32-S3-Touch-LCD-4.3-Demo/Arduino/examples/esp32P4/build/HelloWorld.elf /Users/<USER>/Documents/Arduino/libraries/ESP32-S3-Touch-LCD-4.3-Demo/Arduino/examples/esp32P4/build/bootloader/bootloader.elf -m '/Users/<USER>/.espressif/python_env/idf5.5_py3.9_env/bin/python' '/Users/<USER>/esp/v5.5/esp-idf/tools/idf.py'
ESP-ROM:esp32p4-eco2-20240710
Build:Jul 10 2024
rst:0x1 (POWERON),boot:0x30f (SPI_FAST_FLASH_BOOT)
SPI mode:DIO, clock div:1
load:0x4ff33ce0,len:0x164c
load:0x4ff29ed0,len:0xd64
load:0x4ff2cbd0,len:0x3364
entry 0x4ff29eda
[0;32mI (25) boot: ESP-IDF v5.5-dirty 2nd stage bootloader[0m
[0;32mI (26) boot: compile time Jul 29 2025 12:48:23[0m
[0;32mI (26) boot: Multicore bootloader[0m
[0;32mI (27) boot: chip revision: v1.0[0m
[0;32mI (29) boot: efuse block revision: v0.3[0m
[0;32mI (33) boot.esp32p4: SPI Speed      : 80MHz[0m
[0;32mI (36) boot.esp32p4: SPI Mode       : DIO[0m
[0;32mI (40) boot.esp32p4: SPI Flash Size : 16MB[0m
[0;32mI (44) boot: Enabling RNG early entropy source...[0m
[0;32mI (49) boot: Partition Table:[0m
[0;32mI (51) boot: ## Label            Usage          Type ST Offset   Length[0m
[0;32mI (57) boot:  0 nvs              WiFi data        01 02 00009000 00006000[0m
[0;32mI (64) boot:  1 phy_init         RF data          01 01 0000f000 00001000[0m
[0;32mI (70) boot:  2 factory          factory app      00 00 00010000 00800000[0m
[0;32mI (77) boot:  3 storage          Unknown data     01 82 00810000 00600000[0m
[0;32mI (83) boot:  4 coredump         Unknown data     01 03 00e10000 00010000[0m
[0;32mI (90) boot:  5 nvs_key          NVS keys         01 04 00e20000 00001000[0m
[0;32mI (96) boot:  6 data             Unknown data     01 81 00e30000 001d0000[0m
[0;32mI (104) boot: End of partition table[0m
[0;32mI (107) esp_image: segment 0: paddr=00010020 vaddr=40020020 size=0be58h ( 48728) map[0m
[0;32mI (123) esp_image: segment 1: paddr=0001be80 vaddr=30100000 size=00044h (    68) load[0m
[0;32mI (125) esp_image: segment 2: paddr=0001becc vaddr=4ff00000 size=0414ch ( 16716) load[0m
[0;32mI (133) esp_image: segment 3: paddr=00020020 vaddr=40000020 size=1dd40h (122176) map[0m
[0;32mI (158) esp_image: segment 4: paddr=0003dd68 vaddr=4ff0414c size=0b824h ( 47140) load[0m
[0;32mI (169) esp_image: segment 5: paddr=00049594 vaddr=4ff0f980 size=01da8h (  7592) load[0m
[0;32mI (175) boot: Loaded app from partition at offset 0x10000[0m
[0;32mI (175) boot: Disabling RNG early entropy source...[0m
[0;32mI (186) cpu_start: Multicore app[0m
[0;32mI (195) cpu_start: Pro cpu start user code[0m
[0;32mI (195) cpu_start: cpu freq: 360000000 Hz[0m
[0;32mI (196) app_init: Application information:[0m
[0;32mI (196) app_init: Project name:     HelloWorld[0m
[0;32mI (200) app_init: App version:      1[0m
[0;32mI (203) app_init: Compile time:     Jul 29 2025 12:48:17[0m
[0;32mI (208) app_init: ELF file SHA256:  3eeb394c4...[0m
[0;32mI (212) app_init: ESP-IDF:          v5.5-dirty[0m
[0;32mI (216) efuse_init: Min chip rev:     v0.1[0m
[0;32mI (220) efuse_init: Max chip rev:     v1.99 [0m
[0;32mI (224) efuse_init: Chip rev:         v1.0[0m
[0;32mI (228) heap_init: Initializing. RAM available for dynamic allocation:[0m
[0;32mI (234) heap_init: At 4FF12E90 len 00028130 (160 KiB): RAM[0m
[0;32mI (240) heap_init: At 4FF3AFC0 len 00004BF0 (18 KiB): RAM[0m
[0;32mI (245) heap_init: At 4FF40000 len 00060000 (384 KiB): RAM[0m
[0;32mI (250) heap_init: At 50108080 len 00007F80 (31 KiB): RTCRAM[0m
[0;32mI (255) heap_init: At 30100044 len 00001FBC (7 KiB): TCM[0m
[0;33mW (261) spi_flash: GigaDevice detected but related driver is not linked, please check option `SPI_FLASH_SUPPORT_GD_CHIP`[0m
[0;32mI (271) spi_flash: detected chip: generic[0m
[0;32mI (275) spi_flash: flash io: dio[0m
[0;32mI (278) main_task: Started on CPU0[0m
[0;32mI (318) main_task: Calling app_main()[0m
[0;32mI (318) RS485_LOOPBACK: === ESP32-P4 RS485回环测试程序 ===[0m
[0;32mI (318) RS485_LOOPBACK: 🎯 测试目标: 验证HVD75扩展板回环功能[0m
[0;32mI (318) RS485_LOOPBACK: 📋 测试条件:[0m
[0;32mI (328) RS485_LOOPBACK:    - HVD75扩展板A+和B-已短接[0m
[0;32mI (328) RS485_LOOPBACK:    - ESP32-P4 GPIO21 → 扩展板RX (TX_PIN)[0m
[0;32mI (338) RS485_LOOPBACK:    - ESP32-P4 GPIO20 ← 扩展板TX (RX_PIN)[0m
[0;32mI (348) RS485_LOOPBACK:    - 3.3V供电正常[0m
[0;32mI (348) RS485_LOOPBACK: 🚀 开始测试...[0m

[0;32mI (348) RS485_LOOPBACK: 🔧 RS485回环测试开始[0m
[0;32mI (358) RS485_LOOPBACK:    UART: 2, TX: GPIO21, RX: GPIO20, 波特率: 9600[0m
[0;32mI (368) RS485_LOOPBACK:    前提条件: HVD75扩展板A+和B-已短接[0m
[0;32mI (368) RS485_LOOPBACK: ✅ UART配置完成[0m
[0;32mI (378) RS485_LOOPBACK: === 第1次回环测试 ===[0m
[0;32mI (378) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (388) main_task: Returned from app_main()[0m
[0;32mI (448) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (448) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (448) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (1468) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (1468) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (1468) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (1468) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (1468) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (1478) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (1478) RS485_LOOPBACK: 📊 统计: 0/1 成功 (0.0%)[0m
[0;32mI (1488) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (4488) RS485_LOOPBACK: === 第2次回环测试 ===[0m
[0;32mI (4488) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (4548) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (4548) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (4558) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (5578) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (5578) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (5578) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (5578) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (5578) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (5588) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (5588) RS485_LOOPBACK: 📊 统计: 0/2 成功 (0.0%)[0m
[0;32mI (5598) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (8598) RS485_LOOPBACK: === 第3次回环测试 ===[0m
[0;32mI (8598) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (8658) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (8658) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (8668) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (9688) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (9688) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (9688) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (9688) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (9688) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (9698) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (9698) RS485_LOOPBACK: 📊 统计: 0/3 成功 (0.0%)[0m
[0;32mI (9708) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (12708) RS485_LOOPBACK: === 第4次回环测试 ===[0m
[0;32mI (12708) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (12768) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (12768) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (12778) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (13798) RS485_LOOPBACK: