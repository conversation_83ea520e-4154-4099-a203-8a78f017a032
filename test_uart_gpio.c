/*
 * 测试ESP32-P4的GPIO20/21是否可以正常用作UART引脚
 * 基于ESP-IDF官方UART示例
 */

#include <stdio.h>
#include <string.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "driver/uart.h"
#include "driver/gpio.h"
#include "esp_log.h"

static const char *TAG = "UART_TEST";

#define UART_TEST_NUM       UART_NUM_2
#define UART_TEST_TX_PIN    21
#define UART_TEST_RX_PIN    20
#define UART_TEST_BAUDRATE  9600
#define BUF_SIZE            1024

void uart_test_task(void *arg)
{
    // 配置UART参数
    uart_config_t uart_config = {
        .baud_rate = UART_TEST_BAUDRATE,
        .data_bits = UART_DATA_8_BITS,
        .parity    = UART_PARITY_DISABLE,
        .stop_bits = UART_STOP_BITS_1,
        .flow_ctrl = UART_HW_FLOWCTRL_DISABLE,
        .source_clk = UART_SCLK_DEFAULT,
    };
    
    ESP_LOGI(TAG, "🔧 开始UART GPIO20/21测试...");
    ESP_LOGI(TAG, "   UART端口: %d", UART_TEST_NUM);
    ESP_LOGI(TAG, "   TX引脚: GPIO%d", UART_TEST_TX_PIN);
    ESP_LOGI(TAG, "   RX引脚: GPIO%d", UART_TEST_RX_PIN);
    ESP_LOGI(TAG, "   波特率: %d", UART_TEST_BAUDRATE);
    
    // 安装UART驱动
    ESP_ERROR_CHECK(uart_driver_install(UART_TEST_NUM, BUF_SIZE * 2, 0, 0, NULL, 0));
    ESP_LOGI(TAG, "✅ UART驱动安装成功");
    
    // 配置UART参数
    ESP_ERROR_CHECK(uart_param_config(UART_TEST_NUM, &uart_config));
    ESP_LOGI(TAG, "✅ UART参数配置成功");
    
    // 设置UART引脚
    esp_err_t ret = uart_set_pin(UART_TEST_NUM, UART_TEST_TX_PIN, UART_TEST_RX_PIN, 
                                UART_PIN_NO_CHANGE, UART_PIN_NO_CHANGE);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "❌ UART引脚设置失败: %s", esp_err_to_name(ret));
        ESP_LOGE(TAG, "   GPIO%d和GPIO%d可能不支持UART功能", UART_TEST_TX_PIN, UART_TEST_RX_PIN);
        return;
    }
    ESP_LOGI(TAG, "✅ UART引脚设置成功");
    
    // 测试数据
    const char* test_str = "ESP32-P4 UART GPIO20/21 Test\n";
    uint8_t data[BUF_SIZE];
    
    ESP_LOGI(TAG, "🚀 开始循环测试...");
    
    while (1) {
        // 发送测试数据
        int txBytes = uart_write_bytes(UART_TEST_NUM, test_str, strlen(test_str));
        ESP_LOGI(TAG, "📤 发送 %d 字节: %s", txBytes, test_str);
        
        // 等待发送完成
        ESP_ERROR_CHECK(uart_wait_tx_done(UART_TEST_NUM, 1000 / portTICK_PERIOD_MS));
        
        // 尝试接收数据（如果有回环连接）
        int rxBytes = uart_read_bytes(UART_TEST_NUM, data, BUF_SIZE, 1000 / portTICK_PERIOD_MS);
        if (rxBytes > 0) {
            data[rxBytes] = 0;
            ESP_LOGI(TAG, "📥 接收 %d 字节: %s", rxBytes, data);
        } else {
            ESP_LOGI(TAG, "📥 未接收到数据（正常，因为没有回环连接）");
        }
        
        vTaskDelay(2000 / portTICK_PERIOD_MS);
    }
}

void app_main(void)
{
    ESP_LOGI(TAG, "=== ESP32-P4 UART GPIO20/21 功能测试 ===");
    ESP_LOGI(TAG, "🔧 修复内容:");
    ESP_LOGI(TAG, "   1. 统一GPIO20/21引脚配置");
    ESP_LOGI(TAG, "   2. 改进接收响应逻辑");
    ESP_LOGI(TAG, "   3. 添加发送后延时");
    ESP_LOGI(TAG, "   4. 增强错误诊断信息");

    xTaskCreate(uart_test_task, "uart_test_task", 4096, NULL, 10, NULL);
}
