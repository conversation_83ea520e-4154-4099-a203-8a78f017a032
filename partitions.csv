# ESP32-P4 分区表配置 - 16MB Flash优化版本
# Name,   Type, SubType, Offset,  Size,    Flags
# Note: if you have increased the bootloader size, make sure to update the offsets to avoid overlap
nvs,      data, nvs,     0x9000,  0x6000,
phy_init, data, phy,     0xf000,  0x1000,
factory,  app,  factory, 0x10000, 0x800000,
storage,  data, spiffs,  0x810000,0x600000,
coredump, data, coredump,0xe10000,0x10000,
nvs_key,  data, nvs_keys,0xe20000,0x1000,
data,     data, fat,     0xe30000,0x1d0000,
