/*
 * Automatically generated file. DO NOT EDIT.
 * Espressif IoT Development Framework (ESP-IDF) 5.5.0 Configuration Header
 */
#pragma once
#define CONFIG_SOC_ADC_SUPPORTED 1
#define CONFIG_SOC_ANA_CMPR_SUPPORTED 1
#define CONFIG_SOC_DEDICATED_GPIO_SUPPORTED 1
#define CONFIG_SOC_UART_SUPPORTED 1
#define CONFIG_SOC_GDMA_SUPPORTED 1
#define CONFIG_SOC_UHCI_SUPPORTED 1
#define CONFIG_SOC_AHB_GDMA_SUPPORTED 1
#define CONFIG_SOC_AXI_GDMA_SUPPORTED 1
#define CONFIG_SOC_DW_GDMA_SUPPORTED 1
#define CONFIG_SOC_DMA2D_SUPPORTED 1
#define CONFIG_SOC_GPTIMER_SUPPORTED 1
#define CONFIG_SOC_PCNT_SUPPORTED 1
#define CONFIG_SOC_LCDCAM_SUPPORTED 1
#define CONFIG_SOC_LCDCAM_CAM_SUPPORTED 1
#define CONFIG_SOC_LCDCAM_I80_LCD_SUPPORTED 1
#define CONFIG_SOC_LCDCAM_RGB_LCD_SUPPORTED 1
#define CONFIG_SOC_MIPI_CSI_SUPPORTED 1
#define CONFIG_SOC_MIPI_DSI_SUPPORTED 1
#define CONFIG_SOC_MCPWM_SUPPORTED 1
#define CONFIG_SOC_TWAI_SUPPORTED 1
#define CONFIG_SOC_ETM_SUPPORTED 1
#define CONFIG_SOC_PARLIO_SUPPORTED 1
#define CONFIG_SOC_ASYNC_MEMCPY_SUPPORTED 1
#define CONFIG_SOC_EMAC_SUPPORTED 1
#define CONFIG_SOC_USB_OTG_SUPPORTED 1
#define CONFIG_SOC_WIRELESS_HOST_SUPPORTED 1
#define CONFIG_SOC_USB_SERIAL_JTAG_SUPPORTED 1
#define CONFIG_SOC_TEMP_SENSOR_SUPPORTED 1
#define CONFIG_SOC_SUPPORTS_SECURE_DL_MODE 1
#define CONFIG_SOC_ULP_SUPPORTED 1
#define CONFIG_SOC_LP_CORE_SUPPORTED 1
#define CONFIG_SOC_EFUSE_KEY_PURPOSE_FIELD 1
#define CONFIG_SOC_EFUSE_SUPPORTED 1
#define CONFIG_SOC_RTC_FAST_MEM_SUPPORTED 1
#define CONFIG_SOC_RTC_MEM_SUPPORTED 1
#define CONFIG_SOC_RMT_SUPPORTED 1
#define CONFIG_SOC_I2S_SUPPORTED 1
#define CONFIG_SOC_SDM_SUPPORTED 1
#define CONFIG_SOC_GPSPI_SUPPORTED 1
#define CONFIG_SOC_LEDC_SUPPORTED 1
#define CONFIG_SOC_ISP_SUPPORTED 1
#define CONFIG_SOC_I2C_SUPPORTED 1
#define CONFIG_SOC_SYSTIMER_SUPPORTED 1
#define CONFIG_SOC_AES_SUPPORTED 1
#define CONFIG_SOC_MPI_SUPPORTED 1
#define CONFIG_SOC_SHA_SUPPORTED 1
#define CONFIG_SOC_HMAC_SUPPORTED 1
#define CONFIG_SOC_DIG_SIGN_SUPPORTED 1
#define CONFIG_SOC_ECC_SUPPORTED 1
#define CONFIG_SOC_ECC_EXTENDED_MODES_SUPPORTED 1
#define CONFIG_SOC_FLASH_ENC_SUPPORTED 1
#define CONFIG_SOC_SECURE_BOOT_SUPPORTED 1
#define CONFIG_SOC_BOD_SUPPORTED 1
#define CONFIG_SOC_VBAT_SUPPORTED 1
#define CONFIG_SOC_APM_SUPPORTED 1
#define CONFIG_SOC_PMU_SUPPORTED 1
#define CONFIG_SOC_DCDC_SUPPORTED 1
#define CONFIG_SOC_PAU_SUPPORTED 1
#define CONFIG_SOC_LP_TIMER_SUPPORTED 1
#define CONFIG_SOC_ULP_LP_UART_SUPPORTED 1
#define CONFIG_SOC_LP_GPIO_MATRIX_SUPPORTED 1
#define CONFIG_SOC_LP_PERIPHERALS_SUPPORTED 1
#define CONFIG_SOC_LP_I2C_SUPPORTED 1
#define CONFIG_SOC_LP_I2S_SUPPORTED 1
#define CONFIG_SOC_LP_SPI_SUPPORTED 1
#define CONFIG_SOC_LP_ADC_SUPPORTED 1
#define CONFIG_SOC_LP_VAD_SUPPORTED 1
#define CONFIG_SOC_SPIRAM_SUPPORTED 1
#define CONFIG_SOC_PSRAM_DMA_CAPABLE 1
#define CONFIG_SOC_SDMMC_HOST_SUPPORTED 1
#define CONFIG_SOC_CLK_TREE_SUPPORTED 1
#define CONFIG_SOC_ASSIST_DEBUG_SUPPORTED 1
#define CONFIG_SOC_DEBUG_PROBE_SUPPORTED 1
#define CONFIG_SOC_WDT_SUPPORTED 1
#define CONFIG_SOC_SPI_FLASH_SUPPORTED 1
#define CONFIG_SOC_TOUCH_SENSOR_SUPPORTED 1
#define CONFIG_SOC_RNG_SUPPORTED 1
#define CONFIG_SOC_GP_LDO_SUPPORTED 1
#define CONFIG_SOC_PPA_SUPPORTED 1
#define CONFIG_SOC_LIGHT_SLEEP_SUPPORTED 1
#define CONFIG_SOC_DEEP_SLEEP_SUPPORTED 1
#define CONFIG_SOC_PM_SUPPORTED 1
#define CONFIG_SOC_BITSCRAMBLER_SUPPORTED 1
#define CONFIG_SOC_SIMD_INSTRUCTION_SUPPORTED 1
#define CONFIG_SOC_I3C_MASTER_SUPPORTED 1
#define CONFIG_SOC_XTAL_SUPPORT_40M 1
#define CONFIG_SOC_AES_SUPPORT_DMA 1
#define CONFIG_SOC_AES_SUPPORT_GCM 1
#define CONFIG_SOC_AES_GDMA 1
#define CONFIG_SOC_AES_SUPPORT_AES_128 1
#define CONFIG_SOC_AES_SUPPORT_AES_256 1
#define CONFIG_SOC_ADC_RTC_CTRL_SUPPORTED 1
#define CONFIG_SOC_ADC_DIG_CTRL_SUPPORTED 1
#define CONFIG_SOC_ADC_DMA_SUPPORTED 1
#define CONFIG_SOC_ADC_PERIPH_NUM 2
#define CONFIG_SOC_ADC_MAX_CHANNEL_NUM 8
#define CONFIG_SOC_ADC_ATTEN_NUM 4
#define CONFIG_SOC_ADC_DIGI_CONTROLLER_NUM 2
#define CONFIG_SOC_ADC_PATT_LEN_MAX 16
#define CONFIG_SOC_ADC_DIGI_MAX_BITWIDTH 12
#define CONFIG_SOC_ADC_DIGI_MIN_BITWIDTH 12
#define CONFIG_SOC_ADC_DIGI_IIR_FILTER_NUM 2
#define CONFIG_SOC_ADC_DIGI_MONITOR_NUM 2
#define CONFIG_SOC_ADC_DIGI_RESULT_BYTES 4
#define CONFIG_SOC_ADC_DIGI_DATA_BYTES_PER_CONV 4
#define CONFIG_SOC_ADC_SAMPLE_FREQ_THRES_HIGH 83333
#define CONFIG_SOC_ADC_SAMPLE_FREQ_THRES_LOW 611
#define CONFIG_SOC_ADC_RTC_MIN_BITWIDTH 12
#define CONFIG_SOC_ADC_RTC_MAX_BITWIDTH 12
#define CONFIG_SOC_ADC_CALIBRATION_V1_SUPPORTED 1
#define CONFIG_SOC_ADC_SELF_HW_CALI_SUPPORTED 1
#define CONFIG_SOC_ADC_CALIB_CHAN_COMPENS_SUPPORTED 1
#define CONFIG_SOC_ADC_SHARED_POWER 1
#define CONFIG_SOC_BROWNOUT_RESET_SUPPORTED 1
#define CONFIG_SOC_SHARED_IDCACHE_SUPPORTED 1
#define CONFIG_SOC_CACHE_WRITEBACK_SUPPORTED 1
#define CONFIG_SOC_CACHE_FREEZE_SUPPORTED 1
#define CONFIG_SOC_CACHE_INTERNAL_MEM_VIA_L1CACHE 1
#define CONFIG_SOC_CPU_CORES_NUM 2
#define CONFIG_SOC_CPU_INTR_NUM 32
#define CONFIG_SOC_CPU_HAS_FLEXIBLE_INTC 1
#define CONFIG_SOC_INT_CLIC_SUPPORTED 1
#define CONFIG_SOC_INT_HW_NESTED_SUPPORTED 1
#define CONFIG_SOC_BRANCH_PREDICTOR_SUPPORTED 1
#define CONFIG_SOC_CPU_COPROC_NUM 3
#define CONFIG_SOC_CPU_HAS_FPU 1
#define CONFIG_SOC_CPU_HAS_FPU_EXT_ILL_BUG 1
#define CONFIG_SOC_CPU_HAS_HWLOOP 1
#define CONFIG_SOC_CPU_HAS_HWLOOP_STATE_BUG 1
#define CONFIG_SOC_CPU_HAS_PIE 1
#define CONFIG_SOC_HP_CPU_HAS_MULTIPLE_CORES 1
#define CONFIG_SOC_CPU_BREAKPOINTS_NUM 3
#define CONFIG_SOC_CPU_WATCHPOINTS_NUM 3
#define CONFIG_SOC_CPU_WATCHPOINT_MAX_REGION_SIZE 0x100
#define CONFIG_SOC_CPU_HAS_PMA 1
#define CONFIG_SOC_CPU_IDRAM_SPLIT_USING_PMP 1
#define CONFIG_SOC_CPU_PMP_REGION_GRANULARITY 128
#define CONFIG_SOC_CPU_HAS_LOCKUP_RESET 1
#define CONFIG_SOC_SIMD_PREFERRED_DATA_ALIGNMENT 16
#define CONFIG_SOC_DS_SIGNATURE_MAX_BIT_LEN 4096
#define CONFIG_SOC_DS_KEY_PARAM_MD_IV_LENGTH 16
#define CONFIG_SOC_DS_KEY_CHECK_MAX_WAIT_US 1100
#define CONFIG_SOC_DMA_CAN_ACCESS_FLASH 1
#define CONFIG_SOC_AHB_GDMA_VERSION 2
#define CONFIG_SOC_GDMA_SUPPORT_CRC 1
#define CONFIG_SOC_GDMA_NUM_GROUPS_MAX 2
#define CONFIG_SOC_GDMA_PAIRS_PER_GROUP_MAX 3
#define CONFIG_SOC_AXI_GDMA_SUPPORT_PSRAM 1
#define CONFIG_SOC_GDMA_SUPPORT_ETM 1
#define CONFIG_SOC_GDMA_SUPPORT_SLEEP_RETENTION 1
#define CONFIG_SOC_AXI_DMA_EXT_MEM_ENC_ALIGNMENT 16
#define CONFIG_SOC_DMA2D_GROUPS 1
#define CONFIG_SOC_DMA2D_TX_CHANNELS_PER_GROUP 3
#define CONFIG_SOC_DMA2D_RX_CHANNELS_PER_GROUP 2
#define CONFIG_SOC_ETM_GROUPS 1
#define CONFIG_SOC_ETM_CHANNELS_PER_GROUP 50
#define CONFIG_SOC_ETM_SUPPORT_SLEEP_RETENTION 1
#define CONFIG_SOC_GPIO_PORT 1
#define CONFIG_SOC_GPIO_PIN_COUNT 55
#define CONFIG_SOC_GPIO_SUPPORT_PIN_GLITCH_FILTER 1
#define CONFIG_SOC_GPIO_FLEX_GLITCH_FILTER_NUM 8
#define CONFIG_SOC_GPIO_SUPPORT_PIN_HYS_FILTER 1
#define CONFIG_SOC_GPIO_SUPPORT_ETM 1
#define CONFIG_SOC_GPIO_SUPPORT_RTC_INDEPENDENT 1
#define CONFIG_SOC_GPIO_SUPPORT_DEEPSLEEP_WAKEUP 1
#define CONFIG_SOC_LP_IO_HAS_INDEPENDENT_WAKEUP_SOURCE 1
#define CONFIG_SOC_LP_IO_CLOCK_IS_INDEPENDENT 1
#define CONFIG_SOC_GPIO_VALID_GPIO_MASK 0x007FFFFFFFFFFFFF
#define CONFIG_SOC_GPIO_IN_RANGE_MAX 54
#define CONFIG_SOC_GPIO_OUT_RANGE_MAX 54
#define CONFIG_SOC_GPIO_DEEP_SLEEP_WAKE_VALID_GPIO_MASK 0
#define CONFIG_SOC_GPIO_DEEP_SLEEP_WAKE_SUPPORTED_PIN_CNT 16
#define CONFIG_SOC_GPIO_VALID_DIGITAL_IO_PAD_MASK 0x007FFFFFFFFF0000
#define CONFIG_SOC_GPIO_CLOCKOUT_BY_GPIO_MATRIX 1
#define CONFIG_SOC_GPIO_CLOCKOUT_CHANNEL_NUM 2
#define CONFIG_SOC_CLOCKOUT_SUPPORT_CHANNEL_DIVIDER 1
#define CONFIG_SOC_DEBUG_PROBE_NUM_UNIT 1
#define CONFIG_SOC_DEBUG_PROBE_MAX_OUTPUT_WIDTH 16
#define CONFIG_SOC_GPIO_SUPPORT_FORCE_HOLD 1
#define CONFIG_SOC_RTCIO_PIN_COUNT 16
#define CONFIG_SOC_RTCIO_INPUT_OUTPUT_SUPPORTED 1
#define CONFIG_SOC_RTCIO_HOLD_SUPPORTED 1
#define CONFIG_SOC_RTCIO_WAKE_SUPPORTED 1
#define CONFIG_SOC_RTCIO_EDGE_WAKE_SUPPORTED 1
#define CONFIG_SOC_DEDIC_GPIO_OUT_CHANNELS_NUM 8
#define CONFIG_SOC_DEDIC_GPIO_IN_CHANNELS_NUM 8
#define CONFIG_SOC_DEDIC_PERIPH_ALWAYS_ENABLE 1
#define CONFIG_SOC_ANA_CMPR_NUM 2
#define CONFIG_SOC_ANA_CMPR_CAN_DISTINGUISH_EDGE 1
#define CONFIG_SOC_ANA_CMPR_SUPPORT_ETM 1
#define CONFIG_SOC_I2C_NUM 3
#define CONFIG_SOC_HP_I2C_NUM 2
#define CONFIG_SOC_I2C_FIFO_LEN 32
#define CONFIG_SOC_I2C_CMD_REG_NUM 8
#define CONFIG_SOC_I2C_SUPPORT_SLAVE 1
#define CONFIG_SOC_I2C_SUPPORT_HW_FSM_RST 1
#define CONFIG_SOC_I2C_SUPPORT_HW_CLR_BUS 1
#define CONFIG_SOC_I2C_SUPPORT_XTAL 1
#define CONFIG_SOC_I2C_SUPPORT_RTC 1
#define CONFIG_SOC_I2C_SUPPORT_10BIT_ADDR 1
#define CONFIG_SOC_I2C_SLAVE_SUPPORT_BROADCAST 1
#define CONFIG_SOC_I2C_SLAVE_CAN_GET_STRETCH_CAUSE 1
#define CONFIG_SOC_I2C_SLAVE_SUPPORT_I2CRAM_ACCESS 1
#define CONFIG_SOC_I2C_SLAVE_SUPPORT_SLAVE_UNMATCH 1
#define CONFIG_SOC_I2C_SUPPORT_SLEEP_RETENTION 1
#define CONFIG_SOC_LP_I2C_NUM 1
#define CONFIG_SOC_LP_I2C_FIFO_LEN 16
#define CONFIG_SOC_I2S_NUM 3
#define CONFIG_SOC_I2S_HW_VERSION_2 1
#define CONFIG_SOC_I2S_SUPPORTS_ETM 1
#define CONFIG_SOC_I2S_SUPPORTS_XTAL 1
#define CONFIG_SOC_I2S_SUPPORTS_APLL 1
#define CONFIG_SOC_I2S_SUPPORTS_PCM 1
#define CONFIG_SOC_I2S_SUPPORTS_PDM 1
#define CONFIG_SOC_I2S_SUPPORTS_PDM_TX 1
#define CONFIG_SOC_I2S_SUPPORTS_PCM2PDM 1
#define CONFIG_SOC_I2S_SUPPORTS_PDM_RX 1
#define CONFIG_SOC_I2S_SUPPORTS_PDM2PCM 1
#define CONFIG_SOC_I2S_SUPPORTS_PDM_RX_HP_FILTER 1
#define CONFIG_SOC_I2S_SUPPORTS_TX_SYNC_CNT 1
#define CONFIG_SOC_I2S_SUPPORTS_TDM 1
#define CONFIG_SOC_I2S_PDM_MAX_TX_LINES 2
#define CONFIG_SOC_I2S_PDM_MAX_RX_LINES 4
#define CONFIG_SOC_I2S_TDM_FULL_DATA_WIDTH 1
#define CONFIG_SOC_I2S_SUPPORT_SLEEP_RETENTION 1
#define CONFIG_SOC_LP_I2S_NUM 1
#define CONFIG_SOC_ISP_BF_SUPPORTED 1
#define CONFIG_SOC_ISP_CCM_SUPPORTED 1
#define CONFIG_SOC_ISP_DEMOSAIC_SUPPORTED 1
#define CONFIG_SOC_ISP_DVP_SUPPORTED 1
#define CONFIG_SOC_ISP_SHARPEN_SUPPORTED 1
#define CONFIG_SOC_ISP_COLOR_SUPPORTED 1
#define CONFIG_SOC_ISP_LSC_SUPPORTED 1
#define CONFIG_SOC_ISP_SHARE_CSI_BRG 1
#define CONFIG_SOC_ISP_NUMS 1
#define CONFIG_SOC_ISP_DVP_CTLR_NUMS 1
#define CONFIG_SOC_ISP_AE_CTLR_NUMS 1
#define CONFIG_SOC_ISP_AE_BLOCK_X_NUMS 5
#define CONFIG_SOC_ISP_AE_BLOCK_Y_NUMS 5
#define CONFIG_SOC_ISP_AF_CTLR_NUMS 1
#define CONFIG_SOC_ISP_AF_WINDOW_NUMS 3
#define CONFIG_SOC_ISP_BF_TEMPLATE_X_NUMS 3
#define CONFIG_SOC_ISP_BF_TEMPLATE_Y_NUMS 3
#define CONFIG_SOC_ISP_CCM_DIMENSION 3
#define CONFIG_SOC_ISP_DEMOSAIC_GRAD_RATIO_INT_BITS 2
#define CONFIG_SOC_ISP_DEMOSAIC_GRAD_RATIO_DEC_BITS 4
#define CONFIG_SOC_ISP_DEMOSAIC_GRAD_RATIO_RES_BITS 26
#define CONFIG_SOC_ISP_DVP_DATA_WIDTH_MAX 16
#define CONFIG_SOC_ISP_SHARPEN_TEMPLATE_X_NUMS 3
#define CONFIG_SOC_ISP_SHARPEN_TEMPLATE_Y_NUMS 3
#define CONFIG_SOC_ISP_SHARPEN_H_FREQ_COEF_INT_BITS 3
#define CONFIG_SOC_ISP_SHARPEN_H_FREQ_COEF_DEC_BITS 5
#define CONFIG_SOC_ISP_SHARPEN_H_FREQ_COEF_RES_BITS 24
#define CONFIG_SOC_ISP_SHARPEN_M_FREQ_COEF_INT_BITS 3
#define CONFIG_SOC_ISP_SHARPEN_M_FREQ_COEF_DEC_BITS 5
#define CONFIG_SOC_ISP_SHARPEN_M_FREQ_COEF_RES_BITS 24
#define CONFIG_SOC_ISP_HIST_CTLR_NUMS 1
#define CONFIG_SOC_ISP_HIST_BLOCK_X_NUMS 5
#define CONFIG_SOC_ISP_HIST_BLOCK_Y_NUMS 5
#define CONFIG_SOC_ISP_HIST_SEGMENT_NUMS 16
#define CONFIG_SOC_ISP_HIST_INTERVAL_NUMS 15
#define CONFIG_SOC_ISP_LSC_GRAD_RATIO_INT_BITS 2
#define CONFIG_SOC_ISP_LSC_GRAD_RATIO_DEC_BITS 8
#define CONFIG_SOC_ISP_LSC_GRAD_RATIO_RES_BITS 22
#define CONFIG_SOC_LEDC_SUPPORT_PLL_DIV_CLOCK 1
#define CONFIG_SOC_LEDC_SUPPORT_XTAL_CLOCK 1
#define CONFIG_SOC_LEDC_TIMER_NUM 4
#define CONFIG_SOC_LEDC_CHANNEL_NUM 8
#define CONFIG_SOC_LEDC_TIMER_BIT_WIDTH 20
#define CONFIG_SOC_LEDC_GAMMA_CURVE_FADE_SUPPORTED 1
#define CONFIG_SOC_LEDC_GAMMA_CURVE_FADE_RANGE_MAX 16
#define CONFIG_SOC_LEDC_SUPPORT_FADE_STOP 1
#define CONFIG_SOC_LEDC_FADE_PARAMS_BIT_WIDTH 10
#define CONFIG_SOC_LEDC_SUPPORT_SLEEP_RETENTION 1
#define CONFIG_SOC_MMU_PERIPH_NUM 2
#define CONFIG_SOC_MMU_LINEAR_ADDRESS_REGION_NUM 2
#define CONFIG_SOC_MMU_DI_VADDR_SHARED 1
#define CONFIG_SOC_MMU_PER_EXT_MEM_TARGET 1
#define CONFIG_SOC_MPU_MIN_REGION_SIZE 0x20000000
#define CONFIG_SOC_MPU_REGIONS_MAX_NUM 8
#define CONFIG_SOC_PCNT_GROUPS 1
#define CONFIG_SOC_PCNT_UNITS_PER_GROUP 4
#define CONFIG_SOC_PCNT_CHANNELS_PER_UNIT 2
#define CONFIG_SOC_PCNT_THRES_POINT_PER_UNIT 2
#define CONFIG_SOC_PCNT_SUPPORT_RUNTIME_THRES_UPDATE 1
#define CONFIG_SOC_PCNT_SUPPORT_CLEAR_SIGNAL 1
#define CONFIG_SOC_PCNT_SUPPORT_SLEEP_RETENTION 1
#define CONFIG_SOC_RMT_GROUPS 1
#define CONFIG_SOC_RMT_TX_CANDIDATES_PER_GROUP 4
#define CONFIG_SOC_RMT_RX_CANDIDATES_PER_GROUP 4
#define CONFIG_SOC_RMT_CHANNELS_PER_GROUP 8
#define CONFIG_SOC_RMT_MEM_WORDS_PER_CHANNEL 48
#define CONFIG_SOC_RMT_SUPPORT_RX_PINGPONG 1
#define CONFIG_SOC_RMT_SUPPORT_RX_DEMODULATION 1
#define CONFIG_SOC_RMT_SUPPORT_TX_ASYNC_STOP 1
#define CONFIG_SOC_RMT_SUPPORT_TX_LOOP_COUNT 1
#define CONFIG_SOC_RMT_SUPPORT_TX_LOOP_AUTO_STOP 1
#define CONFIG_SOC_RMT_SUPPORT_TX_SYNCHRO 1
#define CONFIG_SOC_RMT_SUPPORT_TX_CARRIER_DATA_ONLY 1
#define CONFIG_SOC_RMT_SUPPORT_XTAL 1
#define CONFIG_SOC_RMT_SUPPORT_RC_FAST 1
#define CONFIG_SOC_RMT_SUPPORT_DMA 1
#define CONFIG_SOC_RMT_SUPPORT_SLEEP_RETENTION 1
#define CONFIG_SOC_LCD_I80_SUPPORTED 1
#define CONFIG_SOC_LCD_RGB_SUPPORTED 1
#define CONFIG_SOC_LCDCAM_I80_NUM_BUSES 1
#define CONFIG_SOC_LCDCAM_I80_BUS_WIDTH 24
#define CONFIG_SOC_LCDCAM_RGB_NUM_PANELS 1
#define CONFIG_SOC_LCDCAM_RGB_DATA_WIDTH 24
#define CONFIG_SOC_LCD_SUPPORT_RGB_YUV_CONV 1
#define CONFIG_SOC_MCPWM_GROUPS 2
#define CONFIG_SOC_MCPWM_TIMERS_PER_GROUP 3
#define CONFIG_SOC_MCPWM_OPERATORS_PER_GROUP 3
#define CONFIG_SOC_MCPWM_COMPARATORS_PER_OPERATOR 2
#define CONFIG_SOC_MCPWM_EVENT_COMPARATORS_PER_OPERATOR 2
#define CONFIG_SOC_MCPWM_GENERATORS_PER_OPERATOR 2
#define CONFIG_SOC_MCPWM_TRIGGERS_PER_OPERATOR 2
#define CONFIG_SOC_MCPWM_GPIO_FAULTS_PER_GROUP 3
#define CONFIG_SOC_MCPWM_CAPTURE_TIMERS_PER_GROUP 1
#define CONFIG_SOC_MCPWM_CAPTURE_CHANNELS_PER_TIMER 3
#define CONFIG_SOC_MCPWM_GPIO_SYNCHROS_PER_GROUP 3
#define CONFIG_SOC_MCPWM_SWSYNC_CAN_PROPAGATE 1
#define CONFIG_SOC_MCPWM_SUPPORT_ETM 1
#define CONFIG_SOC_MCPWM_SUPPORT_EVENT_COMPARATOR 1
#define CONFIG_SOC_MCPWM_CAPTURE_CLK_FROM_GROUP 1
#define CONFIG_SOC_MCPWM_SUPPORT_SLEEP_RETENTION 1
#define CONFIG_SOC_USB_OTG_PERIPH_NUM 2
#define CONFIG_SOC_USB_UTMI_PHY_NUM 1
#define CONFIG_SOC_USB_UTMI_PHY_NO_POWER_OFF_ISO 1
#define CONFIG_SOC_PARLIO_GROUPS 1
#define CONFIG_SOC_PARLIO_TX_UNITS_PER_GROUP 1
#define CONFIG_SOC_PARLIO_RX_UNITS_PER_GROUP 1
#define CONFIG_SOC_PARLIO_TX_UNIT_MAX_DATA_WIDTH 16
#define CONFIG_SOC_PARLIO_RX_UNIT_MAX_DATA_WIDTH 16
#define CONFIG_SOC_PARLIO_TX_CLK_SUPPORT_GATING 1
#define CONFIG_SOC_PARLIO_RX_CLK_SUPPORT_GATING 1
#define CONFIG_SOC_PARLIO_RX_CLK_SUPPORT_OUTPUT 1
#define CONFIG_SOC_PARLIO_TRANS_BIT_ALIGN 1
#define CONFIG_SOC_PARLIO_TX_SUPPORT_LOOP_TRANSMISSION 1
#define CONFIG_SOC_PARLIO_SUPPORT_SLEEP_RETENTION 1
#define CONFIG_SOC_PARLIO_SUPPORT_SPI_LCD 1
#define CONFIG_SOC_PARLIO_SUPPORT_I80_LCD 1
#define CONFIG_SOC_MPI_MEM_BLOCKS_NUM 4
#define CONFIG_SOC_MPI_OPERATIONS_NUM 3
#define CONFIG_SOC_RSA_MAX_BIT_LEN 4096
#define CONFIG_SOC_SDMMC_USE_IOMUX 1
#define CONFIG_SOC_SDMMC_USE_GPIO_MATRIX 1
#define CONFIG_SOC_SDMMC_NUM_SLOTS 2
#define CONFIG_SOC_SDMMC_DELAY_PHASE_NUM 4
#define CONFIG_SOC_SDMMC_IO_POWER_EXTERNAL 1
#define CONFIG_SOC_SDMMC_PSRAM_DMA_CAPABLE 1
#define CONFIG_SOC_SDMMC_UHS_I_SUPPORTED 1
#define CONFIG_SOC_SHA_DMA_MAX_BUFFER_SIZE 3968
#define CONFIG_SOC_SHA_SUPPORT_DMA 1
#define CONFIG_SOC_SHA_SUPPORT_RESUME 1
#define CONFIG_SOC_SHA_GDMA 1
#define CONFIG_SOC_SHA_SUPPORT_SHA1 1
#define CONFIG_SOC_SHA_SUPPORT_SHA224 1
#define CONFIG_SOC_SHA_SUPPORT_SHA256 1
#define CONFIG_SOC_SHA_SUPPORT_SHA384 1
#define CONFIG_SOC_SHA_SUPPORT_SHA512 1
#define CONFIG_SOC_SHA_SUPPORT_SHA512_224 1
#define CONFIG_SOC_SHA_SUPPORT_SHA512_256 1
#define CONFIG_SOC_SHA_SUPPORT_SHA512_T 1
#define CONFIG_SOC_ECDSA_SUPPORT_EXPORT_PUBKEY 1
#define CONFIG_SOC_ECDSA_SUPPORT_DETERMINISTIC_MODE 1
#define CONFIG_SOC_ECDSA_USES_MPI 1
#define CONFIG_SOC_SDM_GROUPS 1
#define CONFIG_SOC_SDM_CHANNELS_PER_GROUP 8
#define CONFIG_SOC_SDM_CLK_SUPPORT_PLL_F80M 1
#define CONFIG_SOC_SDM_CLK_SUPPORT_XTAL 1
#define CONFIG_SOC_SPI_PERIPH_NUM 3
#define CONFIG_SOC_SPI_MAX_CS_NUM 6
#define CONFIG_SOC_SPI_MAXIMUM_BUFFER_SIZE 64
#define CONFIG_SOC_SPI_SUPPORT_SLEEP_RETENTION 1
#define CONFIG_SOC_SPI_SUPPORT_SLAVE_HD_VER2 1
#define CONFIG_SOC_SPI_SLAVE_SUPPORT_SEG_TRANS 1
#define CONFIG_SOC_SPI_SUPPORT_DDRCLK 1
#define CONFIG_SOC_SPI_SUPPORT_CD_SIG 1
#define CONFIG_SOC_SPI_SUPPORT_OCT 1
#define CONFIG_SOC_SPI_SUPPORT_CLK_XTAL 1
#define CONFIG_SOC_SPI_SUPPORT_CLK_RC_FAST 1
#define CONFIG_SOC_SPI_SUPPORT_CLK_SPLL 1
#define CONFIG_SOC_MSPI_HAS_INDEPENT_IOMUX 1
#define CONFIG_SOC_MEMSPI_IS_INDEPENDENT 1
#define CONFIG_SOC_SPI_MAX_PRE_DIVIDER 16
#define CONFIG_SOC_LP_SPI_PERIPH_NUM 1
#define CONFIG_SOC_LP_SPI_MAXIMUM_BUFFER_SIZE 64
#define CONFIG_SOC_SPIRAM_XIP_SUPPORTED 1
#define CONFIG_SOC_SPI_MEM_SUPPORT_AUTO_WAIT_IDLE 1
#define CONFIG_SOC_SPI_MEM_SUPPORT_AUTO_SUSPEND 1
#define CONFIG_SOC_SPI_MEM_SUPPORT_AUTO_RESUME 1
#define CONFIG_SOC_SPI_MEM_SUPPORT_IDLE_INTR 1
#define CONFIG_SOC_SPI_MEM_SUPPORT_SW_SUSPEND 1
#define CONFIG_SOC_SPI_MEM_SUPPORT_CHECK_SUS 1
#define CONFIG_SOC_SPI_MEM_SUPPORT_TIMING_TUNING 1
#define CONFIG_SOC_MEMSPI_TIMING_TUNING_BY_DQS 1
#define CONFIG_SOC_MEMSPI_TIMING_TUNING_BY_FLASH_DELAY 1
#define CONFIG_SOC_SPI_MEM_SUPPORT_CACHE_32BIT_ADDR_MAP 1
#define CONFIG_SOC_SPI_PERIPH_SUPPORT_CONTROL_DUMMY_OUT 1
#define CONFIG_SOC_MEMSPI_SRC_FREQ_80M_SUPPORTED 1
#define CONFIG_SOC_MEMSPI_SRC_FREQ_40M_SUPPORTED 1
#define CONFIG_SOC_MEMSPI_SRC_FREQ_20M_SUPPORTED 1
#define CONFIG_SOC_MEMSPI_SRC_FREQ_120M_SUPPORTED 1
#define CONFIG_SOC_MEMSPI_FLASH_PSRAM_INDEPENDENT 1
#define CONFIG_SOC_SYSTIMER_COUNTER_NUM 2
#define CONFIG_SOC_SYSTIMER_ALARM_NUM 3
#define CONFIG_SOC_SYSTIMER_BIT_WIDTH_LO 32
#define CONFIG_SOC_SYSTIMER_BIT_WIDTH_HI 20
#define CONFIG_SOC_SYSTIMER_FIXED_DIVIDER 1
#define CONFIG_SOC_SYSTIMER_SUPPORT_RC_FAST 1
#define CONFIG_SOC_SYSTIMER_INT_LEVEL 1
#define CONFIG_SOC_SYSTIMER_ALARM_MISS_COMPENSATE 1
#define CONFIG_SOC_SYSTIMER_SUPPORT_ETM 1
#define CONFIG_SOC_LP_TIMER_BIT_WIDTH_LO 32
#define CONFIG_SOC_LP_TIMER_BIT_WIDTH_HI 16
#define CONFIG_SOC_TIMER_GROUPS 2
#define CONFIG_SOC_TIMER_GROUP_TIMERS_PER_GROUP 2
#define CONFIG_SOC_TIMER_GROUP_COUNTER_BIT_WIDTH 54
#define CONFIG_SOC_TIMER_GROUP_SUPPORT_XTAL 1
#define CONFIG_SOC_TIMER_GROUP_SUPPORT_RC_FAST 1
#define CONFIG_SOC_TIMER_GROUP_TOTAL_TIMERS 4
#define CONFIG_SOC_TIMER_SUPPORT_ETM 1
#define CONFIG_SOC_TIMER_SUPPORT_SLEEP_RETENTION 1
#define CONFIG_SOC_MWDT_SUPPORT_XTAL 1
#define CONFIG_SOC_MWDT_SUPPORT_SLEEP_RETENTION 1
#define CONFIG_SOC_TOUCH_SENSOR_VERSION 3
#define CONFIG_SOC_TOUCH_SENSOR_NUM 14
#define CONFIG_SOC_TOUCH_MIN_CHAN_ID 1
#define CONFIG_SOC_TOUCH_MAX_CHAN_ID 14
#define CONFIG_SOC_TOUCH_SUPPORT_SLEEP_WAKEUP 1
#define CONFIG_SOC_TOUCH_SUPPORT_BENCHMARK 1
#define CONFIG_SOC_TOUCH_SUPPORT_WATERPROOF 1
#define CONFIG_SOC_TOUCH_SUPPORT_PROX_SENSING 1
#define CONFIG_SOC_TOUCH_PROXIMITY_CHANNEL_NUM 3
#define CONFIG_SOC_TOUCH_PROXIMITY_MEAS_DONE_SUPPORTED 1
#define CONFIG_SOC_TOUCH_SUPPORT_FREQ_HOP 1
#define CONFIG_SOC_TOUCH_SAMPLE_CFG_NUM 3
#define CONFIG_SOC_TWAI_CONTROLLER_NUM 3
#define CONFIG_SOC_TWAI_MASK_FILTER_NUM 1
#define CONFIG_SOC_TWAI_CLK_SUPPORT_XTAL 1
#define CONFIG_SOC_TWAI_BRP_MIN 2
#define CONFIG_SOC_TWAI_BRP_MAX 32768
#define CONFIG_SOC_TWAI_SUPPORTS_RX_STATUS 1
#define CONFIG_SOC_TWAI_SUPPORT_SLEEP_RETENTION 1
#define CONFIG_SOC_EFUSE_DIS_PAD_JTAG 1
#define CONFIG_SOC_EFUSE_DIS_USB_JTAG 1
#define CONFIG_SOC_EFUSE_DIS_DIRECT_BOOT 1
#define CONFIG_SOC_EFUSE_SOFT_DIS_JTAG 1
#define CONFIG_SOC_EFUSE_DIS_DOWNLOAD_MSPI 1
#define CONFIG_SOC_EFUSE_ECDSA_KEY 1
#define CONFIG_SOC_KEY_MANAGER_ECDSA_KEY_DEPLOY 1
#define CONFIG_SOC_KEY_MANAGER_FE_KEY_DEPLOY 1
#define CONFIG_SOC_SECURE_BOOT_V2_RSA 1
#define CONFIG_SOC_SECURE_BOOT_V2_ECC 1
#define CONFIG_SOC_EFUSE_SECURE_BOOT_KEY_DIGESTS 3
#define CONFIG_SOC_EFUSE_REVOKE_BOOT_KEY_DIGESTS 1
#define CONFIG_SOC_SUPPORT_SECURE_BOOT_REVOKE_KEY 1
#define CONFIG_SOC_FLASH_ENCRYPTED_XTS_AES_BLOCK_MAX 64
#define CONFIG_SOC_FLASH_ENCRYPTION_XTS_AES 1
#define CONFIG_SOC_FLASH_ENCRYPTION_XTS_AES_OPTIONS 1
#define CONFIG_SOC_FLASH_ENCRYPTION_XTS_AES_128 1
#define CONFIG_SOC_FLASH_ENCRYPTION_XTS_AES_256 1
#define CONFIG_SOC_UART_NUM 6
#define CONFIG_SOC_UART_HP_NUM 5
#define CONFIG_SOC_UART_LP_NUM 1
#define CONFIG_SOC_UART_FIFO_LEN 128
#define CONFIG_SOC_LP_UART_FIFO_LEN 16
#define CONFIG_SOC_UART_BITRATE_MAX 5000000
#define CONFIG_SOC_UART_SUPPORT_PLL_F80M_CLK 1
#define CONFIG_SOC_UART_SUPPORT_RTC_CLK 1
#define CONFIG_SOC_UART_SUPPORT_XTAL_CLK 1
#define CONFIG_SOC_UART_SUPPORT_WAKEUP_INT 1
#define CONFIG_SOC_UART_HAS_LP_UART 1
#define CONFIG_SOC_UART_SUPPORT_SLEEP_RETENTION 1
#define CONFIG_SOC_UART_SUPPORT_FSM_TX_WAIT_SEND 1
#define CONFIG_SOC_UART_WAKEUP_CHARS_SEQ_MAX_LEN 5
#define CONFIG_SOC_UART_WAKEUP_SUPPORT_ACTIVE_THRESH_MODE 1
#define CONFIG_SOC_UART_WAKEUP_SUPPORT_FIFO_THRESH_MODE 1
#define CONFIG_SOC_UART_WAKEUP_SUPPORT_START_BIT_MODE 1
#define CONFIG_SOC_UART_WAKEUP_SUPPORT_CHAR_SEQ_MODE 1
#define CONFIG_SOC_LP_I2S_SUPPORT_VAD 1
#define CONFIG_SOC_UHCI_NUM 1
#define CONFIG_SOC_COEX_HW_PTI 1
#define CONFIG_SOC_PHY_DIG_REGS_MEM_SIZE 21
#define CONFIG_SOC_WIFI_LIGHT_SLEEP_CLK_WIDTH 12
#define CONFIG_SOC_PM_SUPPORT_EXT1_WAKEUP 1
#define CONFIG_SOC_PM_SUPPORT_EXT1_WAKEUP_MODE_PER_PIN 1
#define CONFIG_SOC_PM_EXT1_WAKEUP_BY_PMU 1
#define CONFIG_SOC_PM_SUPPORT_WIFI_WAKEUP 1
#define CONFIG_SOC_PM_SUPPORT_TOUCH_SENSOR_WAKEUP 1
#define CONFIG_SOC_PM_SUPPORT_XTAL32K_PD 1
#define CONFIG_SOC_PM_SUPPORT_RC32K_PD 1
#define CONFIG_SOC_PM_SUPPORT_RC_FAST_PD 1
#define CONFIG_SOC_PM_SUPPORT_VDDSDIO_PD 1
#define CONFIG_SOC_PM_SUPPORT_TOP_PD 1
#define CONFIG_SOC_PM_SUPPORT_CNNT_PD 1
#define CONFIG_SOC_PM_SUPPORT_RTC_PERIPH_PD 1
#define CONFIG_SOC_PM_SUPPORT_DEEPSLEEP_CHECK_STUB_ONLY 1
#define CONFIG_SOC_PM_CPU_RETENTION_BY_SW 1
#define CONFIG_SOC_PM_CACHE_RETENTION_BY_PAU 1
#define CONFIG_SOC_PM_PAU_LINK_NUM 4
#define CONFIG_SOC_PM_PAU_REGDMA_LINK_MULTI_ADDR 1
#define CONFIG_SOC_PAU_IN_TOP_DOMAIN 1
#define CONFIG_SOC_CPU_IN_TOP_DOMAIN 1
#define CONFIG_SOC_PM_PAU_REGDMA_UPDATE_CACHE_BEFORE_WAIT_COMPARE 1
#define CONFIG_SOC_SLEEP_SYSTIMER_STALL_WORKAROUND 1
#define CONFIG_SOC_SLEEP_TGWDT_STOP_WORKAROUND 1
#define CONFIG_SOC_PM_RETENTION_MODULE_NUM 64
#define CONFIG_SOC_PSRAM_VDD_POWER_MPLL 1
#define CONFIG_SOC_CLK_RC_FAST_SUPPORT_CALIBRATION 1
#define CONFIG_SOC_CLK_APLL_SUPPORTED 1
#define CONFIG_SOC_CLK_MPLL_SUPPORTED 1
#define CONFIG_SOC_CLK_SDIO_PLL_SUPPORTED 1
#define CONFIG_SOC_CLK_XTAL32K_SUPPORTED 1
#define CONFIG_SOC_CLK_RC32K_SUPPORTED 1
#define CONFIG_SOC_CLK_LP_FAST_SUPPORT_LP_PLL 1
#define CONFIG_SOC_CLK_LP_FAST_SUPPORT_XTAL 1
#define CONFIG_SOC_PERIPH_CLK_CTRL_SHARED 1
#define CONFIG_SOC_CLK_ANA_I2C_MST_HAS_ROOT_GATE 1
#define CONFIG_SOC_TEMPERATURE_SENSOR_LP_PLL_SUPPORT 1
#define CONFIG_SOC_TEMPERATURE_SENSOR_INTR_SUPPORT 1
#define CONFIG_SOC_TSENS_IS_INDEPENDENT_FROM_ADC 1
#define CONFIG_SOC_TEMPERATURE_SENSOR_SUPPORT_ETM 1
#define CONFIG_SOC_TEMPERATURE_SENSOR_SUPPORT_SLEEP_RETENTION 1
#define CONFIG_SOC_MEM_TCM_SUPPORTED 1
#define CONFIG_SOC_MEM_NON_CONTIGUOUS_SRAM 1
#define CONFIG_SOC_ASYNCHRONOUS_BUS_ERROR_MODE 1
#define CONFIG_SOC_EMAC_IEEE1588V2_SUPPORTED 1
#define CONFIG_SOC_EMAC_USE_MULTI_IO_MUX 1
#define CONFIG_SOC_EMAC_MII_USE_GPIO_MATRIX 1
#define CONFIG_SOC_JPEG_CODEC_SUPPORTED 1
#define CONFIG_SOC_JPEG_DECODE_SUPPORTED 1
#define CONFIG_SOC_JPEG_ENCODE_SUPPORTED 1
#define CONFIG_SOC_LCDCAM_CAM_SUPPORT_RGB_YUV_CONV 1
#define CONFIG_SOC_LCDCAM_CAM_PERIPH_NUM 1
#define CONFIG_SOC_LCDCAM_CAM_DATA_WIDTH_MAX 16
#define CONFIG_SOC_I3C_MASTER_PERIPH_NUM 1
#define CONFIG_SOC_I3C_MASTER_ADDRESS_TABLE_NUM 12
#define CONFIG_SOC_I3C_MASTER_COMMAND_TABLE_NUM 12
#define CONFIG_SOC_LP_CORE_SUPPORT_ETM 1
#define CONFIG_SOC_LP_CORE_SUPPORT_LP_ADC 1
#define CONFIG_SOC_LP_CORE_SUPPORT_LP_VAD 1
#define CONFIG_SOC_LP_CORE_SUPPORT_STORE_LOAD_EXCEPTIONS 1
#define CONFIG_IDF_CMAKE 1
#define CONFIG_IDF_TOOLCHAIN "gcc"
#define CONFIG_IDF_TOOLCHAIN_GCC 1
#define CONFIG_IDF_TARGET_ARCH_RISCV 1
#define CONFIG_IDF_TARGET_ARCH "riscv"
#define CONFIG_IDF_TARGET "esp32p4"
#define CONFIG_IDF_INIT_VERSION "5.5.0"
#define CONFIG_IDF_TARGET_ESP32P4 1
#define CONFIG_IDF_FIRMWARE_CHIP_ID 0x0012
#define CONFIG_APP_BUILD_TYPE_APP_2NDBOOT 1
#define CONFIG_APP_BUILD_GENERATE_BINARIES 1
#define CONFIG_APP_BUILD_BOOTLOADER 1
#define CONFIG_APP_BUILD_USE_FLASH_SECTIONS 1
#define CONFIG_BOOTLOADER_COMPILE_TIME_DATE 1
#define CONFIG_BOOTLOADER_PROJECT_VER 1
#define CONFIG_BOOTLOADER_OFFSET_IN_FLASH 0x2000
#define CONFIG_BOOTLOADER_COMPILER_OPTIMIZATION_SIZE 1
#define CONFIG_BOOTLOADER_LOG_VERSION_1 1
#define CONFIG_BOOTLOADER_LOG_VERSION 1
#define CONFIG_BOOTLOADER_LOG_LEVEL_INFO 1
#define CONFIG_BOOTLOADER_LOG_LEVEL 3
#define CONFIG_BOOTLOADER_LOG_TIMESTAMP_SOURCE_CPU_TICKS 1
#define CONFIG_BOOTLOADER_LOG_MODE_TEXT_EN 1
#define CONFIG_BOOTLOADER_LOG_MODE_TEXT 1
#define CONFIG_BOOTLOADER_FLASH_XMC_SUPPORT 1
#define CONFIG_BOOTLOADER_REGION_PROTECTION_ENABLE 1
#define CONFIG_BOOTLOADER_WDT_ENABLE 1
#define CONFIG_BOOTLOADER_WDT_TIME_MS 9000
#define CONFIG_BOOTLOADER_RESERVE_RTC_SIZE 0x0
#define CONFIG_SECURE_BOOT_V2_RSA_SUPPORTED 1
#define CONFIG_SECURE_BOOT_V2_ECC_SUPPORTED 1
#define CONFIG_SECURE_BOOT_V2_PREFERRED 1
#define CONFIG_SECURE_ROM_DL_MODE_ENABLED 1
#define CONFIG_APP_COMPILE_TIME_DATE 1
#define CONFIG_APP_RETRIEVE_LEN_ELF_SHA 9
#define CONFIG_ESP_ROM_HAS_CRC_LE 1
#define CONFIG_ESP_ROM_HAS_CRC_BE 1
#define CONFIG_ESP_ROM_UART_CLK_IS_XTAL 1
#define CONFIG_ESP_ROM_USB_SERIAL_DEVICE_NUM 6
#define CONFIG_ESP_ROM_USB_OTG_NUM 5
#define CONFIG_ESP_ROM_HAS_RETARGETABLE_LOCKING 1
#define CONFIG_ESP_ROM_GET_CLK_FREQ 1
#define CONFIG_ESP_ROM_HAS_RVFPLIB 1
#define CONFIG_ESP_ROM_HAS_HAL_WDT 1
#define CONFIG_ESP_ROM_HAS_HAL_SYSTIMER 1
#define CONFIG_ESP_ROM_HAS_LAYOUT_TABLE 1
#define CONFIG_ESP_ROM_WDT_INIT_PATCH 1
#define CONFIG_ESP_ROM_HAS_LP_ROM 1
#define CONFIG_ESP_ROM_WITHOUT_REGI2C 1
#define CONFIG_ESP_ROM_HAS_NEWLIB 1
#define CONFIG_ESP_ROM_HAS_NEWLIB_NANO_FORMAT 1
#define CONFIG_ESP_ROM_HAS_NEWLIB_NANO_PRINTF_FLOAT_BUG 1
#define CONFIG_ESP_ROM_HAS_VERSION 1
#define CONFIG_ESP_ROM_CLIC_INT_TYPE_PATCH 1
#define CONFIG_ESP_ROM_HAS_OUTPUT_PUTC_FUNC 1
#define CONFIG_ESP_ROM_HAS_SUBOPTIMAL_NEWLIB_ON_MISALIGNED_MEMORY 1
#define CONFIG_BOOT_ROM_LOG_ALWAYS_ON 1
#define CONFIG_ESPTOOLPY_FLASHMODE_DIO 1
#define CONFIG_ESPTOOLPY_FLASH_SAMPLE_MODE_STR 1
#define CONFIG_ESPTOOLPY_FLASHMODE "dio"
#define CONFIG_ESPTOOLPY_FLASHFREQ_80M 1
#define CONFIG_ESPTOOLPY_FLASHFREQ_VAL 80
#define CONFIG_ESPTOOLPY_FLASHFREQ "80m"
#define CONFIG_ESPTOOLPY_FLASHSIZE_16MB 1
#define CONFIG_ESPTOOLPY_FLASHSIZE "16MB"
#define CONFIG_ESPTOOLPY_BEFORE_RESET 1
#define CONFIG_ESPTOOLPY_BEFORE "default_reset"
#define CONFIG_ESPTOOLPY_AFTER_RESET 1
#define CONFIG_ESPTOOLPY_AFTER "hard_reset"
#define CONFIG_ESPTOOLPY_MONITOR_BAUD 115200
#define CONFIG_PARTITION_TABLE_CUSTOM 1
#define CONFIG_PARTITION_TABLE_CUSTOM_FILENAME "partitions.csv"
#define CONFIG_PARTITION_TABLE_FILENAME "partitions.csv"
#define CONFIG_PARTITION_TABLE_OFFSET 0x8000
#define CONFIG_PARTITION_TABLE_MD5 1
#define CONFIG_COMPILER_OPTIMIZATION_DEBUG 1
#define CONFIG_COMPILER_OPTIMIZATION_ASSERTIONS_ENABLE 1
#define CONFIG_COMPILER_ASSERT_NDEBUG_EVALUATE 1
#define CONFIG_COMPILER_FLOAT_LIB_FROM_RVFPLIB 1
#define CONFIG_COMPILER_OPTIMIZATION_ASSERTION_LEVEL 2
#define CONFIG_COMPILER_HIDE_PATHS_MACROS 1
#define CONFIG_COMPILER_STACK_CHECK_MODE_NONE 1
#define CONFIG_COMPILER_DISABLE_DEFAULT_ERRORS 1
#define CONFIG_COMPILER_RT_LIB_GCCLIB 1
#define CONFIG_COMPILER_RT_LIB_NAME "gcc"
#define CONFIG_COMPILER_ORPHAN_SECTIONS_WARNING 1
#define CONFIG_EFUSE_MAX_BLK_LEN 256
#define CONFIG_ESP_ERR_TO_NAME_LOOKUP 1
#define CONFIG_ESP32P4_REV_MIN_1 1
#define CONFIG_ESP32P4_REV_MIN_FULL 1
#define CONFIG_ESP_REV_MIN_FULL 1
#define CONFIG_ESP32P4_REV_MAX_FULL 199
#define CONFIG_ESP_REV_MAX_FULL 199
#define CONFIG_ESP_EFUSE_BLOCK_REV_MIN_FULL 0
#define CONFIG_ESP_EFUSE_BLOCK_REV_MAX_FULL 99
#define CONFIG_ESP_MAC_ADDR_UNIVERSE_ETH 1
#define CONFIG_ESP_MAC_UNIVERSAL_MAC_ADDRESSES_ONE 1
#define CONFIG_ESP_MAC_UNIVERSAL_MAC_ADDRESSES 1
#define CONFIG_ESP32P4_UNIVERSAL_MAC_ADDRESSES_ONE 1
#define CONFIG_ESP32P4_UNIVERSAL_MAC_ADDRESSES 1
#define CONFIG_ESP_SLEEP_FLASH_LEAKAGE_WORKAROUND 1
#define CONFIG_ESP_SLEEP_WAIT_FLASH_READY_EXTRA_DELAY 0
#define CONFIG_ESP_SLEEP_GPIO_ENABLE_INTERNAL_RESISTORS 1
#define CONFIG_RTC_CLK_SRC_INT_RC 1
#define CONFIG_RTC_CLK_CAL_CYCLES 1024
#define CONFIG_RTC_FAST_CLK_SRC_RC_FAST 1
#define CONFIG_ESP_PERIPH_CTRL_FUNC_IN_IRAM 1
#define CONFIG_ESP_REGI2C_CTRL_FUNC_IN_IRAM 1
#define CONFIG_GDMA_CTRL_FUNC_IN_IRAM 1
#define CONFIG_GDMA_ISR_HANDLER_IN_IRAM 1
#define CONFIG_GDMA_OBJ_DRAM_SAFE 1
#define CONFIG_XTAL_FREQ_40 1
#define CONFIG_XTAL_FREQ 40
#define CONFIG_ESP_SLEEP_KEEP_DCDC_ALWAYS_ON 1
#define CONFIG_ESP_SLEEP_DCM_VSET_VAL_IN_SLEEP 14
#define CONFIG_ESP_LDO_RESERVE_SPI_NOR_FLASH 1
#define CONFIG_ESP_LDO_CHAN_SPI_NOR_FLASH_DOMAIN 1
#define CONFIG_ESP_LDO_VOLTAGE_SPI_NOR_FLASH_3300_MV 1
#define CONFIG_ESP_LDO_VOLTAGE_SPI_NOR_FLASH_DOMAIN 3300
#define CONFIG_ESP_LDO_RESERVE_PSRAM 1
#define CONFIG_ESP_LDO_CHAN_PSRAM_DOMAIN 2
#define CONFIG_ESP_LDO_VOLTAGE_PSRAM_1900_MV 1
#define CONFIG_ESP_LDO_VOLTAGE_PSRAM_DOMAIN 1900
#define CONFIG_ESP_BROWNOUT_DET 1
#define CONFIG_ESP_BROWNOUT_DET_LVL_SEL_7 1
#define CONFIG_ESP_BROWNOUT_DET_LVL 7
#define CONFIG_ESP_BROWNOUT_USE_INTR 1
#define CONFIG_ESP_INTR_IN_IRAM 1
#define CONFIG_ESP_ROM_PRINT_IN_IRAM 1
#define CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ_360 1
#define CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ 360
#define CONFIG_CACHE_L2_CACHE_128KB 1
#define CONFIG_CACHE_L2_CACHE_SIZE 0x20000
#define CONFIG_CACHE_L2_CACHE_LINE_64B 1
#define CONFIG_CACHE_L2_CACHE_LINE_SIZE 64
#define CONFIG_CACHE_L1_CACHE_LINE_SIZE 64
#define CONFIG_ESP_SYSTEM_PANIC_PRINT_REBOOT 1
#define CONFIG_ESP_SYSTEM_PANIC_REBOOT_DELAY_SECONDS 0
#define CONFIG_ESP_SYSTEM_RTC_FAST_MEM_AS_HEAP_DEPCHECK 1
#define CONFIG_ESP_SYSTEM_ALLOW_RTC_FAST_MEM_AS_HEAP 1
#define CONFIG_ESP_SYSTEM_NO_BACKTRACE 1
#define CONFIG_ESP_SYSTEM_PMP_IDRAM_SPLIT 1
#define CONFIG_ESP_SYSTEM_EVENT_QUEUE_SIZE 32
#define CONFIG_ESP_SYSTEM_EVENT_TASK_STACK_SIZE 2304
#define CONFIG_ESP_MAIN_TASK_STACK_SIZE 3584
#define CONFIG_ESP_MAIN_TASK_AFFINITY_CPU0 1
#define CONFIG_ESP_MAIN_TASK_AFFINITY 0x0
#define CONFIG_ESP_MINIMAL_SHARED_STACK_SIZE 2048
#define CONFIG_ESP_CONSOLE_UART_DEFAULT 1
#define CONFIG_ESP_CONSOLE_SECONDARY_USB_SERIAL_JTAG 1
#define CONFIG_ESP_CONSOLE_USB_SERIAL_JTAG_ENABLED 1
#define CONFIG_ESP_CONSOLE_UART 1
#define CONFIG_ESP_CONSOLE_UART_NUM 0
#define CONFIG_ESP_CONSOLE_ROM_SERIAL_PORT_NUM 0
#define CONFIG_ESP_CONSOLE_UART_BAUDRATE 115200
#define CONFIG_ESP_INT_WDT 1
#define CONFIG_ESP_INT_WDT_TIMEOUT_MS 300
#define CONFIG_ESP_INT_WDT_CHECK_CPU1 1
#define CONFIG_ESP_TASK_WDT_EN 1
#define CONFIG_ESP_TASK_WDT_INIT 1
#define CONFIG_ESP_TASK_WDT_TIMEOUT_S 5
#define CONFIG_ESP_TASK_WDT_CHECK_IDLE_TASK_CPU0 1
#define CONFIG_ESP_TASK_WDT_CHECK_IDLE_TASK_CPU1 1
#define CONFIG_ESP_DEBUG_OCDAWARE 1
#define CONFIG_ESP_SYSTEM_CHECK_INT_LEVEL_4 1
#define CONFIG_ESP_SYSTEM_HW_STACK_GUARD 1
#define CONFIG_ESP_SYSTEM_HW_PC_RECORD 1
#define CONFIG_ESP_IPC_TASK_STACK_SIZE 1024
#define CONFIG_ESP_IPC_USES_CALLERS_PRIORITY 1
#define CONFIG_ESP_IPC_ISR_ENABLE 1
#define CONFIG_FREERTOS_HZ 100
#define CONFIG_FREERTOS_CHECK_STACKOVERFLOW_CANARY 1
#define CONFIG_FREERTOS_THREAD_LOCAL_STORAGE_POINTERS 1
#define CONFIG_FREERTOS_IDLE_TASK_STACKSIZE 1536
#define CONFIG_FREERTOS_MAX_TASK_NAME_LEN 16
#define CONFIG_FREERTOS_USE_TIMERS 1
#define CONFIG_FREERTOS_TIMER_SERVICE_TASK_NAME "Tmr Svc"
#define CONFIG_FREERTOS_TIMER_TASK_NO_AFFINITY 1
#define CONFIG_FREERTOS_TIMER_SERVICE_TASK_CORE_AFFINITY 0x7FFFFFFF
#define CONFIG_FREERTOS_TIMER_TASK_PRIORITY 1
#define CONFIG_FREERTOS_TIMER_TASK_STACK_DEPTH 2048
#define CONFIG_FREERTOS_TIMER_QUEUE_LENGTH 10
#define CONFIG_FREERTOS_QUEUE_REGISTRY_SIZE 0
#define CONFIG_FREERTOS_TASK_NOTIFICATION_ARRAY_ENTRIES 1
#define CONFIG_FREERTOS_TASK_FUNCTION_WRAPPER 1
#define CONFIG_FREERTOS_TLSP_DELETION_CALLBACKS 1
#define CONFIG_FREERTOS_CHECK_MUTEX_GIVEN_BY_OWNER 1
#define CONFIG_FREERTOS_ISR_STACKSIZE 1536
#define CONFIG_FREERTOS_INTERRUPT_BACKTRACE 1
#define CONFIG_FREERTOS_TICK_SUPPORT_SYSTIMER 1
#define CONFIG_FREERTOS_CORETIMER_SYSTIMER_LVL1 1
#define CONFIG_FREERTOS_SYSTICK_USES_SYSTIMER 1
#define CONFIG_FREERTOS_PORT 1
#define CONFIG_FREERTOS_NO_AFFINITY 0x7FFFFFFF
#define CONFIG_FREERTOS_SUPPORT_STATIC_ALLOCATION 1
#define CONFIG_FREERTOS_DEBUG_OCDAWARE 1
#define CONFIG_FREERTOS_ENABLE_TASK_SNAPSHOT 1
#define CONFIG_FREERTOS_PLACE_SNAPSHOT_FUNS_INTO_FLASH 1
#define CONFIG_FREERTOS_NUMBER_OF_CORES 2
#define CONFIG_FREERTOS_IN_IRAM 1
#define CONFIG_HAL_ASSERTION_EQUALS_SYSTEM 1
#define CONFIG_HAL_DEFAULT_ASSERTION_LEVEL 2
#define CONFIG_HAL_SYSTIMER_USE_ROM_IMPL 1
#define CONFIG_HAL_WDT_USE_ROM_IMPL 1
#define CONFIG_LOG_VERSION_1 1
#define CONFIG_LOG_VERSION 1
#define CONFIG_LOG_DEFAULT_LEVEL_INFO 1
#define CONFIG_LOG_DEFAULT_LEVEL 3
#define CONFIG_LOG_MAXIMUM_EQUALS_DEFAULT 1
#define CONFIG_LOG_MAXIMUM_LEVEL 3
#define CONFIG_LOG_DYNAMIC_LEVEL_CONTROL 1
#define CONFIG_LOG_TAG_LEVEL_IMPL_CACHE_AND_LINKED_LIST 1
#define CONFIG_LOG_TAG_LEVEL_CACHE_BINARY_MIN_HEAP 1
#define CONFIG_LOG_TAG_LEVEL_IMPL_CACHE_SIZE 31
#define CONFIG_LOG_TIMESTAMP_SOURCE_RTOS 1
#define CONFIG_LOG_MODE_TEXT_EN 1
#define CONFIG_LOG_MODE_TEXT 1
#define CONFIG_LOG_IN_IRAM 1
#define CONFIG_LIBC_NEWLIB 1
#define CONFIG_LIBC_MISC_IN_IRAM 1
#define CONFIG_LIBC_LOCKS_PLACE_IN_IRAM 1
#define CONFIG_LIBC_TIME_SYSCALL_USE_RTC_HRT 1
#define CONFIG_MMU_PAGE_SIZE_64KB 1
#define CONFIG_MMU_PAGE_MODE "64KB"
#define CONFIG_MMU_PAGE_SIZE 0x10000
#define CONFIG_SPI_FLASH_BROWNOUT_RESET_XMC 1
#define CONFIG_SPI_FLASH_BROWNOUT_RESET 1
#define CONFIG_SPI_FLASH_HPM_AUTO 1
#define CONFIG_SPI_FLASH_HPM_ON 1
#define CONFIG_SPI_FLASH_HPM_DC_AUTO 1
#define CONFIG_SPI_FLASH_SUSPEND_TSUS_VAL_US 50
#define CONFIG_SPI_FLASH_PLACE_FUNCTIONS_IN_IRAM 1
#define CONFIG_SPI_FLASH_ROM_DRIVER_PATCH 1
#define CONFIG_SPI_FLASH_DANGEROUS_WRITE_ABORTS 1
#define CONFIG_SPI_FLASH_YIELD_DURING_ERASE 1
#define CONFIG_SPI_FLASH_ERASE_YIELD_DURATION_MS 20
#define CONFIG_SPI_FLASH_ERASE_YIELD_TICKS 1
#define CONFIG_SPI_FLASH_WRITE_CHUNK_SIZE 8192
#define CONFIG_SPI_FLASH_VENDOR_XMC_SUPPORTED 1
#define CONFIG_SPI_FLASH_ENABLE_ENCRYPTED_READ_WRITE 1

/* List of deprecated options */
#define CONFIG_BROWNOUT_DET CONFIG_ESP_BROWNOUT_DET
#define CONFIG_BROWNOUT_DET_LVL CONFIG_ESP_BROWNOUT_DET_LVL
#define CONFIG_BROWNOUT_DET_LVL_SEL_7 CONFIG_ESP_BROWNOUT_DET_LVL_SEL_7
#define CONFIG_COMPILER_OPTIMIZATION_DEFAULT CONFIG_COMPILER_OPTIMIZATION_DEBUG
#define CONFIG_COMPILER_OPTIMIZATION_LEVEL_DEBUG CONFIG_COMPILER_OPTIMIZATION_DEBUG
#define CONFIG_CONSOLE_UART CONFIG_ESP_CONSOLE_UART
#define CONFIG_CONSOLE_UART_BAUDRATE CONFIG_ESP_CONSOLE_UART_BAUDRATE
#define CONFIG_CONSOLE_UART_DEFAULT CONFIG_ESP_CONSOLE_UART_DEFAULT
#define CONFIG_CONSOLE_UART_NUM CONFIG_ESP_CONSOLE_UART_NUM
#define CONFIG_ESP_SYSTEM_BROWNOUT_INTR CONFIG_ESP_BROWNOUT_USE_INTR
#define CONFIG_ESP_TASK_WDT CONFIG_ESP_TASK_WDT_INIT
#define CONFIG_FLASHMODE_DIO CONFIG_ESPTOOLPY_FLASHMODE_DIO
#define CONFIG_INT_WDT CONFIG_ESP_INT_WDT
#define CONFIG_INT_WDT_CHECK_CPU1 CONFIG_ESP_INT_WDT_CHECK_CPU1
#define CONFIG_INT_WDT_TIMEOUT_MS CONFIG_ESP_INT_WDT_TIMEOUT_MS
#define CONFIG_IPC_TASK_STACK_SIZE CONFIG_ESP_IPC_TASK_STACK_SIZE
#define CONFIG_LOG_BOOTLOADER_LEVEL CONFIG_BOOTLOADER_LOG_LEVEL
#define CONFIG_LOG_BOOTLOADER_LEVEL_INFO CONFIG_BOOTLOADER_LOG_LEVEL_INFO
#define CONFIG_MAIN_TASK_STACK_SIZE CONFIG_ESP_MAIN_TASK_STACK_SIZE
#define CONFIG_MONITOR_BAUD CONFIG_ESPTOOLPY_MONITOR_BAUD
#define CONFIG_NEWLIB_TIME_SYSCALL_USE_RTC_HRT CONFIG_LIBC_TIME_SYSCALL_USE_RTC_HRT
#define CONFIG_OPTIMIZATION_ASSERTIONS_ENABLED CONFIG_COMPILER_OPTIMIZATION_ASSERTIONS_ENABLE
#define CONFIG_OPTIMIZATION_ASSERTION_LEVEL CONFIG_COMPILER_OPTIMIZATION_ASSERTION_LEVEL
#define CONFIG_OPTIMIZATION_LEVEL_DEBUG CONFIG_COMPILER_OPTIMIZATION_DEBUG
#define CONFIG_PERIPH_CTRL_FUNC_IN_IRAM CONFIG_ESP_PERIPH_CTRL_FUNC_IN_IRAM
#define CONFIG_SPI_FLASH_WRITING_DANGEROUS_REGIONS_ABORTS CONFIG_SPI_FLASH_DANGEROUS_WRITE_ABORTS
#define CONFIG_STACK_CHECK_NONE CONFIG_COMPILER_STACK_CHECK_MODE_NONE
#define CONFIG_SYSTEM_EVENT_QUEUE_SIZE CONFIG_ESP_SYSTEM_EVENT_QUEUE_SIZE
#define CONFIG_SYSTEM_EVENT_TASK_STACK_SIZE CONFIG_ESP_SYSTEM_EVENT_TASK_STACK_SIZE
#define CONFIG_TASK_WDT CONFIG_ESP_TASK_WDT_INIT
#define CONFIG_TASK_WDT_CHECK_IDLE_TASK_CPU0 CONFIG_ESP_TASK_WDT_CHECK_IDLE_TASK_CPU0
#define CONFIG_TASK_WDT_CHECK_IDLE_TASK_CPU1 CONFIG_ESP_TASK_WDT_CHECK_IDLE_TASK_CPU1
#define CONFIG_TASK_WDT_TIMEOUT_S CONFIG_ESP_TASK_WDT_TIMEOUT_S
#define CONFIG_TIMER_QUEUE_LENGTH CONFIG_FREERTOS_TIMER_QUEUE_LENGTH
#define CONFIG_TIMER_TASK_PRIORITY CONFIG_FREERTOS_TIMER_TASK_PRIORITY
#define CONFIG_TIMER_TASK_STACK_DEPTH CONFIG_FREERTOS_TIMER_TASK_STACK_DEPTH
