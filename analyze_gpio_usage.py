#!/usr/bin/env python3
import fitz  # PyMuPDF
import re

def analyze_gpio_detailed():
    """详细分析ESP32-P4的GPIO使用情况，重点检查推荐的GPIO组合"""

    print("=== ESP32-P4 GPIO详细分析 ===")
    print("目标：详细检查推荐GPIO的实际使用情况和可用性")
    print()

    # 需要详细检查的GPIO组合
    target_gpios = [
        (4, 5, "GPIO4/5"),
        (6, 7, "GPIO6/7"),
        (8, 9, "GPIO8/9"),
        (12, 13, "GPIO12/13"),
        (16, 17, "GPIO16/17"),
        (18, 19, "GPIO18/19"),
        (20, 21, "GPIO20/21"),
    ]
    
    # 分析每个GPIO的详细功能
    def analyze_gpio_functions(gpio_num):
        """分析特定GPIO的功能"""
        functions = []
        conflicts = []

        # 根据ESP32-P4数据手册的GPIO功能表
        gpio_functions = {
            # GPIO0-9
            0: ["GPIO0", "通用IO"],
            1: ["GPIO1", "通用IO"],
            2: ["GPIO2", "通用IO"],
            3: ["GPIO3", "Strapping引脚", "MTDO", "JTAG"],
            4: ["GPIO4", "通用IO", "ADC1_CH3"],
            5: ["GPIO5", "通用IO", "ADC1_CH4"],
            6: ["GPIO6", "通用IO", "ADC1_CH5"],
            7: ["GPIO7", "通用IO", "ADC1_CH6"],
            8: ["GPIO8", "通用IO", "ADC1_CH7"],
            9: ["GPIO9", "通用IO", "ADC2_CH0"],

            # GPIO10-19
            10: ["GPIO10", "通用IO", "ADC2_CH1"],
            11: ["GPIO11", "通用IO", "ADC2_CH2"],
            12: ["GPIO12", "通用IO", "ADC2_CH3"],
            13: ["GPIO13", "通用IO", "ADC2_CH4"],
            14: ["GPIO14", "通用IO", "ADC2_CH5"],
            15: ["GPIO15", "通用IO", "ADC2_CH6"],
            16: ["GPIO16", "通用IO", "ADC2_CH7"],
            17: ["GPIO17", "通用IO", "ADC2_CH8"],
            18: ["GPIO18", "通用IO", "ADC2_CH9"],
            19: ["GPIO19", "通用IO"],

            # GPIO20-29
            20: ["GPIO20", "通用IO"],
            21: ["GPIO21", "通用IO"],
            22: ["GPIO22", "通用IO"],
            23: ["GPIO23", "通用IO"],
            24: ["GPIO24", "USB_D-", "FS_PHY1"],
            25: ["GPIO25", "USB_D+", "FS_PHY1"],
            26: ["GPIO26", "USB_D-", "FS_PHY2"],
            27: ["GPIO27", "USB_D+", "FS_PHY2"],
            28: ["GPIO28", "通用IO", "SPI2_CLK"],
            29: ["GPIO29", "通用IO", "SPI2_CS0"],

            # 特殊功能GPIO
            37: ["GPIO37", "UART0_TXD", "烧录串口"],
            38: ["GPIO38", "UART0_RXD", "烧录串口"],
            45: ["GPIO45", "Strapping引脚"],
            46: ["GPIO46", "Strapping引脚"],
        }

        if gpio_num in gpio_functions:
            functions = gpio_functions[gpio_num]

            # 检查冲突
            if "Strapping" in str(functions):
                conflicts.append("Strapping引脚冲突")
            if "UART0" in str(functions):
                conflicts.append("UART0烧录串口冲突")
            if "USB" in str(functions):
                conflicts.append("USB PHY冲突")
            if "FS_PHY" in str(functions):
                conflicts.append("USB PHY冲突")

        return functions, conflicts
    
    print("=== 详细分析推荐的GPIO组合 ===")
    print()

    for tx_pin, rx_pin, name in target_gpios:
        print(f"📍 {name} 分析:")

        # 分析TX引脚
        tx_functions, tx_conflicts = analyze_gpio_functions(tx_pin)
        print(f"  TX=GPIO{tx_pin}: {', '.join(tx_functions)}")
        if tx_conflicts:
            print(f"    ⚠️  TX冲突: {', '.join(tx_conflicts)}")
        else:
            print(f"    ✅ TX可用")

        # 分析RX引脚
        rx_functions, rx_conflicts = analyze_gpio_functions(rx_pin)
        print(f"  RX=GPIO{rx_pin}: {', '.join(rx_functions)}")
        if rx_conflicts:
            print(f"    ⚠️  RX冲突: {', '.join(rx_conflicts)}")
        else:
            print(f"    ✅ RX可用")

        # 总体评估
        total_conflicts = len(tx_conflicts) + len(rx_conflicts)
        if total_conflicts == 0:
            print(f"  🎯 总体评估: 完全可用 ✅")
        elif total_conflicts <= 2 and "ADC" in str(tx_functions + rx_functions):
            print(f"  🎯 总体评估: 基本可用 (仅ADC功能重叠) ⚠️")
        else:
            print(f"  🎯 总体评估: 有冲突，不推荐 ❌")
        print()

    # 特别检查当前项目可能使用的功能
    print("=== 当前项目功能占用检查 ===")
    print("检查当前项目是否使用了这些GPIO的其他功能...")
    print()

    # 检查ADC使用情况
    adc_gpios = [4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18]
    print("ADC功能GPIO:", adc_gpios)

    # 检查SPI使用情况
    spi_gpios = [28, 29, 30, 31, 32, 33]
    print("SPI功能GPIO:", spi_gpios)

    print()
    print("=== 最终推荐 ===")

    final_recommendations = [
        (20, 21, "GPIO20/21", "最佳选择：无ADC冲突，无特殊功能"),
        (22, 23, "GPIO22/23", "次佳选择：无ADC冲突，无特殊功能"),
        (4, 5, "GPIO4/5", "可用：仅ADC功能重叠"),
        (6, 7, "GPIO6/7", "可用：仅ADC功能重叠"),
        (8, 9, "GPIO8/9", "可用：仅ADC功能重叠"),
    ]

    for tx, rx, name, desc in final_recommendations:
        print(f"  {name}: {desc}")

    return final_recommendations
if __name__ == "__main__":
    analyze_gpio_detailed()
