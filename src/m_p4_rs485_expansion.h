/**
 * ESP32-P4 水质监测系统 - TTL转RS485扩展板配置
 * 
 * 针对ICASH B0505XT-1WRS隔离型TTL转RS485扩展板的专用配置
 * 产品特点：
 * - 隔离型设计，电气隔离保护
 * - 串口自动换向，无需手动控制DE/RE
 * - 高速传输，支持最高2Mbps
 * - 兼容3.3V/5V电平
 * - 尺寸：34MM × 18MM
 */

#ifndef M_P4_RS485_EXPANSION_H
#define M_P4_RS485_EXPANSION_H

#include "m_p4_system_config.h"
#include "driver/uart.h"
#include "driver/gpio.h"

// ==================== 扩展板硬件信息 ====================
#define RS485_EXPANSION_MODEL           "ICASH B0505XT-1WRS"
#define RS485_EXPANSION_SIZE_MM         "34×18"
#define RS485_EXPANSION_ISOLATION       true
#define RS485_EXPANSION_AUTO_DIRECTION  true

// ==================== 引脚连接定义 ====================
/**
 * ESP32-P4开发板 <---> TTL转RS485扩展板（基于40PIN排针实际引脚）
 *
 * ESP32-P4引脚    扩展板引脚    40PIN位置      功能说明
 * GPIO20    <---> RX         GPIO20针脚     UART发送（ESP32-P4 TX -> 扩展板 RX）✅最佳GPIO
 * GPIO21    <---> TX         GPIO21针脚     UART接收（ESP32-P4 RX <- 扩展板 TX）✅最佳GPIO
 * 3.3V      <---> VCC        右侧第1针      电源正极（3.3V供电，推荐方案）
 * GND       <---> GND        右侧第3针      电源负极
 *
 * 扩展板RS485端子：
 * A+        <---> 传感器A+   RS485差分信号正
 * B-        <---> 传感器B-   RS485差分信号负
 *
 * 供电说明：
 * - 推荐使用3.3V供电（功耗低，信号匹配）
 * - 也可使用5V供电（右侧第2针，驱动能力更强）
 */

// ==================== 电气特性配置 ====================
#define RS485_EXP_SUPPLY_VOLTAGE_MIN    3000    // 最小供电电压(mV)
#define RS485_EXP_SUPPLY_VOLTAGE_MAX    5500    // 最大供电电压(mV)
#define RS485_EXP_SUPPLY_CURRENT_MA     50      // 典型工作电流(mA)
#define RS485_EXP_ISOLATION_VOLTAGE     2500    // 隔离电压(V)

// ==================== 通信参数配置 ====================
#define RS485_EXP_BAUDRATE_MIN          1200    // 最小波特率
#define RS485_EXP_BAUDRATE_MAX          2000000 // 最大波特率(2Mbps)
#define RS485_EXP_BAUDRATE_DEFAULT      9600    // 默认波特率
#define RS485_EXP_DATA_BITS             8       // 数据位
#define RS485_EXP_STOP_BITS             1       // 停止位
#define RS485_EXP_PARITY                UART_PARITY_DISABLE // 校验位

// ==================== 性能参数 ====================
#define RS485_EXP_RESPONSE_TIME_US      10      // 响应时间(微秒)
#define RS485_EXP_PROPAGATION_DELAY_NS  50      // 传播延迟(纳秒)
#define RS485_EXP_SKEW_NS               5       // 偏斜(纳秒)

// ==================== 扩展板状态结构 ====================
typedef struct {
    bool initialized;           // 是否已初始化
    bool power_on;             // 电源状态
    bool communication_ok;     // 通信状态
    uint32_t baudrate;         // 当前波特率
    uint32_t tx_count;         // 发送计数
    uint32_t rx_count;         // 接收计数
    uint32_t error_count;      // 错误计数
    uint64_t last_activity;    // 最后活动时间
} rs485_expansion_status_t;

// ==================== 扩展板配置结构 ====================
typedef struct {
    uart_port_t uart_num;      // UART端口号
    int tx_pin;                // TX引脚
    int rx_pin;                // RX引脚
    uint32_t baudrate;         // 波特率
    uart_word_length_t data_bits; // 数据位
    uart_stop_bits_t stop_bits;   // 停止位
    uart_parity_t parity;         // 校验位
    bool flow_control;            // 流控制（扩展板自动换向）
} rs485_expansion_config_t;

// ==================== 全局变量声明 ====================
extern rs485_expansion_status_t g_rs485_expansion_status;

// ==================== 函数声明 ====================

/**
 * 初始化TTL转RS485扩展板
 * @param config 扩展板配置
 * @return ESP_OK 成功，其他值表示失败
 */
esp_err_t rs485_expansion_init(const rs485_expansion_config_t* config);

/**
 * 反初始化扩展板
 * @return ESP_OK 成功，其他值表示失败
 */
esp_err_t rs485_expansion_deinit(void);

/**
 * 获取扩展板默认配置
 * @param config 输出配置结构
 */
void rs485_expansion_get_default_config(rs485_expansion_config_t* config);

/**
 * 检测扩展板是否正常工作
 * @return true 正常，false 异常
 */
bool rs485_expansion_is_healthy(void);

/**
 * 获取扩展板状态
 * @return rs485_expansion_status_t 状态结构
 */
rs485_expansion_status_t rs485_expansion_get_status(void);

/**
 * 设置扩展板波特率
 * @param baudrate 波特率
 * @return ESP_OK 成功，其他值表示失败
 */
esp_err_t rs485_expansion_set_baudrate(uint32_t baudrate);

/**
 * 测试扩展板通信
 * @return ESP_OK 通信正常，其他值表示异常
 */
esp_err_t rs485_expansion_test_communication(void);

/**
 * 打印扩展板信息和状态
 */
void rs485_expansion_print_info(void);

/**
 * 重置扩展板统计信息
 */
void rs485_expansion_reset_stats(void);

// ==================== 便利宏定义 ====================

/**
 * 获取扩展板型号信息
 */
#define RS485_EXPANSION_GET_MODEL() RS485_EXPANSION_MODEL

/**
 * 检查波特率是否在支持范围内
 */
#define RS485_EXPANSION_IS_BAUDRATE_VALID(rate) \
    ((rate) >= RS485_EXP_BAUDRATE_MIN && (rate) <= RS485_EXP_BAUDRATE_MAX)

/**
 * 获取推荐的高速波特率配置
 */
#define RS485_EXPANSION_HIGH_SPEED_BAUDRATE     115200  // 推荐高速率
#define RS485_EXPANSION_ULTRA_HIGH_SPEED_BAUDRATE 460800 // 超高速率

#endif // M_P4_RS485_EXPANSION_H
