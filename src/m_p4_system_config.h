/**
 * ESP32-P4 水质监测系统 - 系统配置文件
 * 
 * 基于Arduino版本移植到ESP-IDF平台
 * 保持与原版本的兼容性和功能一致性
 */

#ifndef M_P4_SYSTEM_CONFIG_H
#define M_P4_SYSTEM_CONFIG_H

#include <stdio.h>
#include <stdint.h>
#include <stdbool.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include "freertos/semphr.h"
#include "esp_log.h"
#include "esp_system.h"

// ==================== 系统版本信息 ====================
#define SYSTEM_VERSION "v3.0-P4"
#define SYSTEM_TITLE "ESP32-P4水质监测系统"
#define BUILD_DATE __DATE__
#define BUILD_TIME __TIME__

// ==================== 系统参数配置 ====================
#define SERIAL_BAUDRATE 115200       // 串口波特率
#define SYSTEM_TICK_RATE_HZ 1000     // 系统时钟频率

// ==================== 任务配置 ====================
// 任务优先级定义 (ESP-IDF使用0-24，数字越大优先级越高)
#define TASK_PRIORITY_CRITICAL    20    // 关键任务（看门狗、系统监控）
#define TASK_PRIORITY_HIGH        15    // 高优先级（传感器数据采集）
#define TASK_PRIORITY_NORMAL      10    // 普通优先级（WiFi通信、数据处理）
#define TASK_PRIORITY_LOW         5     // 低优先级（UI更新、日志）
#define TASK_PRIORITY_IDLE        1     // 空闲任务

// 任务栈大小定义 (字节)
#define STACK_SIZE_SMALL          2048   // 2KB - 简单任务
#define STACK_SIZE_MEDIUM         4096   // 4KB - 普通任务
#define STACK_SIZE_LARGE          8192   // 8KB - 复杂任务
#define STACK_SIZE_XLARGE         16384  // 16KB - UI任务

// 任务句柄声明
extern TaskHandle_t sensor_task_handle;
extern TaskHandle_t ui_task_handle;
extern TaskHandle_t wifi_task_handle;
extern TaskHandle_t bluetooth_task_handle;
extern TaskHandle_t system_monitor_task_handle;

// ==================== 队列配置 ====================
#define SENSOR_DATA_QUEUE_SIZE    10    // 传感器数据队列大小
#define UI_EVENT_QUEUE_SIZE       20    // UI事件队列大小
#define WIFI_MSG_QUEUE_SIZE       15    // WiFi消息队列大小

// 队列句柄声明
extern QueueHandle_t sensor_data_queue;
extern QueueHandle_t ui_event_queue;
extern QueueHandle_t wifi_msg_queue;

// ==================== 传感器配置 ====================
#define MAX_COMMAND_LENGTH        16    // 单个命令最大长度
#define MAX_COMMANDS              10    // 每个传感器最多支持的命令数量
#define MAX_DEVICES               10    // 最大设备数量
#define SENSOR_READ_INTERVAL      3000  // 传感器读取间隔(毫秒)
#define MAX_SENSORS               10    // 最大传感器数量

// ==================== 数据管理配置 ====================
#define MAX_STRING_LENGTH         512   // 最大字符串长度
#define LOG_BUFFER_SIZE           1024  // 日志缓冲区大小
#define SENSOR_NAME_MAX_LENGTH    64    // 传感器名称最大长度
#define MESSAGE_BUFFER_SIZE       256   // 消息缓冲区大小
#define DEFAULT_WINDOW_SIZE       120   // 默认数据窗口大小

// ==================== 硬件配置 ====================
// I2C总线配置
#define I2C_MASTER_SCL_IO         9     // SCL引脚
#define I2C_MASTER_SDA_IO         8     // SDA引脚
#define I2C_MASTER_NUM            0     // I2C端口号
#define I2C_MASTER_FREQ_HZ        100000 // I2C频率

// GPIO定义
#define GPIO_INPUT_IO_4           4     // GPIO4定义
#define LED_STATUS_GPIO           2     // 状态LED

// IO扩展器配置
#define ESP_IO_EXPANDER_I2C_CH422G_ADDRESS 0x20  // CH422G地址
#define TP_RST                    1     // 触摸屏复位引脚
#define LCD_BL                    2     // LCD背光引脚
#define LCD_RST                   3     // LCD复位引脚

// ==================== 网络配置 ====================
#define WIFI_SSID_MAX_LEN         32    // WiFi SSID最大长度
#define WIFI_PASSWORD_MAX_LEN     64    // WiFi密码最大长度
#define WIFI_RETRY_MAX            5     // WiFi重连最大次数

// ==================== 系统监控配置 ====================
#define WATCHDOG_TIMEOUT_MS       30000 // 看门狗超时时间(30秒)
#define SYSTEM_MONITOR_INTERVAL   5000  // 系统监控间隔(5秒)
#define MEMORY_CHECK_INTERVAL     10000 // 内存检查间隔(10秒)

// ==================== 日志配置 ====================
#define LOG_TAG_SYSTEM           "SYSTEM"
#define LOG_TAG_SENSOR           "SENSOR"
#define LOG_TAG_WIFI             "WIFI"
#define LOG_TAG_UI               "UI"
#define LOG_TAG_DATA             "DATA"

// ==================== 传感器数据结构 ====================
typedef struct {
    float ph_value;              // pH值
    float ph_temp;               // pH温度
    float orp_value;             // ORP值
    float oxygen_value;          // 溶解氧值
    float oxygen_temp;           // 溶解氧温度
    uint32_t timestamp;          // 时间戳
    uint32_t ph_response_time;   // pH传感器响应时间(毫秒)
    uint32_t orp_response_time;  // ORP传感器响应时间(毫秒)
    uint32_t oxygen_response_time; // 溶解氧传感器响应时间(毫秒)
    bool data_valid;             // 数据有效性标志
} sensor_data_t;

// ==================== 系统状态结构 ====================
typedef struct {
    uint32_t uptime_seconds;     // 系统运行时间(秒)
    uint32_t free_heap_size;     // 可用堆内存大小
    uint32_t min_free_heap_size; // 最小可用堆内存大小
    uint8_t cpu_usage_percent;   // CPU使用率百分比
    bool wifi_connected;         // WiFi连接状态
    bool sensor_online;          // 传感器在线状态
    uint32_t sensor_error_count; // 传感器错误计数
    uint32_t system_error_count; // 系统错误计数
} system_status_t;

// ==================== 全局变量声明 ====================
extern system_status_t g_system_status;
extern SemaphoreHandle_t g_system_mutex;

// ==================== 函数声明 ====================
/**
 * 系统初始化
 */
esp_err_t system_init(void);

/**
 * 创建所有系统任务
 */
esp_err_t create_system_tasks(void);

/**
 * 创建所有队列和信号量
 */
esp_err_t create_system_queues(void);

/**
 * 系统状态更新
 */
void update_system_status(void);

/**
 * 获取系统状态
 */
system_status_t get_system_status(void);

/**
 * 系统错误处理
 */
void handle_system_error(const char* error_msg, esp_err_t error_code);

#endif // M_P4_SYSTEM_CONFIG_H
