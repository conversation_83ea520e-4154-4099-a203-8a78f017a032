/**
 * ESP32-P4 水质监测系统 - 系统监控模块实现
 */

#include "m_p4_system_monitor.h"
#include "esp_heap_caps.h"
#include "esp_task_wdt.h"
#include <string.h>

// ==================== 全局变量定义 ====================
system_performance_t g_system_performance = {0};
bool g_monitor_enabled = false;
static TaskHandle_t monitor_task_handle = NULL;

// ==================== 私有函数声明 ====================
static void collect_memory_stats(void);
static void collect_task_stats(void);
static void check_system_thresholds(void);

// ==================== 公共函数实现 ====================

esp_err_t system_monitor_init(void) {
    ESP_LOGI(LOG_TAG_SYSTEM, "初始化系统监控模块...");
    
    // 初始化性能统计结构
    memset(&g_system_performance, 0, sizeof(system_performance_t));
    g_system_performance.total_heap_size = heap_caps_get_total_size(MALLOC_CAP_DEFAULT);
    
    g_monitor_enabled = true;
    
    ESP_LOGI(LOG_TAG_SYSTEM, "系统监控模块初始化完成");
    return ESP_OK;
}

esp_err_t system_monitor_start(void) {
    if (!g_monitor_enabled) {
        ESP_LOGE(LOG_TAG_SYSTEM, "系统监控未初始化");
        return ESP_ERR_INVALID_STATE;
    }
    
    BaseType_t result = xTaskCreate(
        system_monitor_task,
        MONITOR_TASK_NAME,
        MONITOR_TASK_STACK_SIZE,
        NULL,
        MONITOR_TASK_PRIORITY,
        &monitor_task_handle
    );
    
    if (result != pdPASS) {
        ESP_LOGE(LOG_TAG_SYSTEM, "创建系统监控任务失败");
        return ESP_FAIL;
    }
    
    ESP_LOGI(LOG_TAG_SYSTEM, "系统监控任务已启动");
    return ESP_OK;
}

esp_err_t system_monitor_stop(void) {
    if (monitor_task_handle != NULL) {
        vTaskDelete(monitor_task_handle);
        monitor_task_handle = NULL;
    }
    
    g_monitor_enabled = false;
    ESP_LOGI(LOG_TAG_SYSTEM, "系统监控任务已停止");
    return ESP_OK;
}

void system_monitor_task(void *pvParameters) {
    ESP_LOGI(LOG_TAG_SYSTEM, "系统监控任务开始运行");
    
    TickType_t last_wake_time = xTaskGetTickCount();
    uint32_t cycle_count = 0;
    
    while (g_monitor_enabled) {
        cycle_count++;
        
        // 更新系统性能统计
        update_system_performance();
        
        // 每5个周期（5秒）进行详细检查
        if (cycle_count % 5 == 0) {
            check_system_thresholds();
            monitor_task_stacks();
        }
        
        // 每30个周期（30秒）打印状态报告
        if (cycle_count % 30 == 0) {
            print_system_status_report();
        }
        
        // 每60个周期（60秒）打印详细统计
        if (cycle_count % 60 == 0) {
            print_memory_stats();
        }
        
        // 等待下一个周期
        vTaskDelayUntil(&last_wake_time, pdMS_TO_TICKS(MONITOR_FAST_INTERVAL));
    }
    
    ESP_LOGI(LOG_TAG_SYSTEM, "系统监控任务结束");
    vTaskDelete(NULL);
}

void update_system_performance(void) {
    // 更新内存统计
    collect_memory_stats();
    
    // 更新任务统计
    collect_task_stats();
    
    // 更新系统运行时间
    g_system_performance.uptime_seconds = esp_timer_get_time() / 1000000;
}

system_performance_t get_system_performance(void) {
    return g_system_performance;
}

void monitor_memory_usage(void) {
    collect_memory_stats();
    
    // 检查内存警告阈值
    if (g_system_performance.free_heap_size < MEMORY_CRITICAL_THRESHOLD) {
        log_system_error("内存严重不足", ESP_ERR_NO_MEM);
    } else if (g_system_performance.free_heap_size < MEMORY_WARNING_THRESHOLD) {
        log_system_warning("内存不足警告");
    }
}

void print_system_status_report(void) {
    ESP_LOGI(LOG_TAG_SYSTEM, "=== 系统状态报告 ===");
    ESP_LOGI(LOG_TAG_SYSTEM, "运行时间: %lu 秒", g_system_performance.uptime_seconds);
    ESP_LOGI(LOG_TAG_SYSTEM, "可用内存: %lu bytes", g_system_performance.free_heap_size);
    ESP_LOGI(LOG_TAG_SYSTEM, "最小可用内存: %lu bytes", g_system_performance.min_free_heap_size);
    ESP_LOGI(LOG_TAG_SYSTEM, "任务数量: %d", g_system_performance.task_count);
    ESP_LOGI(LOG_TAG_SYSTEM, "错误计数: %lu", g_system_performance.error_count);
    ESP_LOGI(LOG_TAG_SYSTEM, "警告计数: %lu", g_system_performance.warning_count);
    ESP_LOGI(LOG_TAG_SYSTEM, "==================");
}

void print_memory_stats(void) {
    ESP_LOGI(LOG_TAG_SYSTEM, "=== 内存使用统计 ===");
    ESP_LOGI(LOG_TAG_SYSTEM, "总内存: %lu bytes", g_system_performance.total_heap_size);
    ESP_LOGI(LOG_TAG_SYSTEM, "可用内存: %lu bytes", g_system_performance.free_heap_size);
    ESP_LOGI(LOG_TAG_SYSTEM, "最大可用块: %lu bytes", g_system_performance.largest_free_block);
    ESP_LOGI(LOG_TAG_SYSTEM, "内存使用率: %.1f%%", 
             100.0 * (g_system_performance.total_heap_size - g_system_performance.free_heap_size) 
             / g_system_performance.total_heap_size);
    ESP_LOGI(LOG_TAG_SYSTEM, "==================");
}

void log_system_error(const char* error_msg, esp_err_t error_code) {
    g_system_performance.error_count++;
    ESP_LOGE(LOG_TAG_SYSTEM, "系统错误: %s (代码: 0x%x)", error_msg, error_code);
}

void log_system_warning(const char* warning_msg) {
    g_system_performance.warning_count++;
    ESP_LOGW(LOG_TAG_SYSTEM, "系统警告: %s", warning_msg);
}

// ==================== 私有函数实现 ====================

static void collect_memory_stats(void) {
    g_system_performance.free_heap_size = heap_caps_get_free_size(MALLOC_CAP_DEFAULT);
    g_system_performance.min_free_heap_size = heap_caps_get_minimum_free_size(MALLOC_CAP_DEFAULT);
    g_system_performance.largest_free_block = heap_caps_get_largest_free_block(MALLOC_CAP_DEFAULT);
}

static void collect_task_stats(void) {
    UBaseType_t task_count = uxTaskGetNumberOfTasks();
    g_system_performance.task_count = (task_count > 10) ? 10 : task_count;
    
    // 这里可以添加更详细的任务统计收集
    // 由于ESP-IDF的任务统计API限制，暂时简化实现
}

static void check_system_thresholds(void) {
    // 检查内存阈值
    if (g_system_performance.free_heap_size < MEMORY_CRITICAL_THRESHOLD) {
        log_system_error("内存严重不足", ESP_ERR_NO_MEM);
    } else if (g_system_performance.free_heap_size < MEMORY_WARNING_THRESHOLD) {
        log_system_warning("内存不足");
    }
}

void monitor_task_stacks(void) {
    // 监控当前任务的栈使用情况
    UBaseType_t stack_high_water = uxTaskGetStackHighWaterMark(NULL);
    
    if (stack_high_water < (MONITOR_TASK_STACK_SIZE * TASK_STACK_WARNING_RATIO)) {
        log_system_warning("监控任务栈使用率过高");
    }
}
