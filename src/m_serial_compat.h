#ifndef M_SERIAL_COMPAT_H
#define M_SERIAL_COMPAT_H

// ESP-IDF兼容的Serial类
// 用于替代Arduino的Serial接口

#include <stdio.h>
#include <stdarg.h>
#include <string.h>
#include "esp_log.h"
#include "esp_heap_caps.h"
#include "esp_system.h"
#include "driver/uart.h"
#include "freertos/FreeRTOS.h"

// 前向声明
class String;

class SerialClass {
public:
    void begin(int baudrate) {
        // ESP-IDF中UART已经在启动时初始化
        (void)baudrate; // 忽略波特率参数
    }
    
    void print(const char* str) {
        printf("%s", str);
        fflush(stdout);
    }
    
    void print(int value) {
        printf("%d", value);
        fflush(stdout);
    }
    
    void print(unsigned int value) {
        printf("%u", value);
        fflush(stdout);
    }
    
    void print(long value) {
        printf("%ld", value);
        fflush(stdout);
    }
    
    void print(unsigned long value) {
        printf("%lu", value);
        fflush(stdout);
    }
    
    void print(float value) {
        printf("%.2f", value);
        fflush(stdout);
    }
    
    void print(double value) {
        printf("%.2f", value);
        fflush(stdout);
    }
    
    void println() {
        printf("\n");
        fflush(stdout);
    }
    
    void println(const char* str) {
        printf("%s\n", str);
        fflush(stdout);
    }
    
    void println(int value) {
        printf("%d\n", value);
        fflush(stdout);
    }
    
    void println(unsigned int value) {
        printf("%u\n", value);
        fflush(stdout);
    }
    
    void println(long value) {
        printf("%ld\n", value);
        fflush(stdout);
    }
    
    void println(unsigned long value) {
        printf("%lu\n", value);
        fflush(stdout);
    }
    
    void println(float value) {
        printf("%.2f\n", value);
        fflush(stdout);
    }
    
    void println(double value) {
        printf("%.2f\n", value);
        fflush(stdout);
    }

    void print(const String& str);
    void println(const String& str);
    
    void printf(const char* format, ...) {
        va_list args;
        va_start(args, format);
        vprintf(format, args);
        va_end(args);
        fflush(stdout);
    }
    
    // 兼容性方法
    size_t write(uint8_t c) {
        putchar(c);
        fflush(stdout);
        return 1;
    }
    
    size_t write(const uint8_t* buffer, size_t size) {
        for (size_t i = 0; i < size; i++) {
            putchar(buffer[i]);
        }
        fflush(stdout);
        return size;
    }
    
    int available() {
        // 简化实现，总是返回0
        return 0;
    }
    
    int read() {
        // 简化实现，总是返回-1
        return -1;
    }
    
    int peek() {
        // 简化实现，总是返回-1
        return -1;
    }
    
    void flush() {
        fflush(stdout);
    }
};

// 全局Serial对象
extern SerialClass Serial;

// 其他Arduino兼容性定义
#ifndef HIGH
#define HIGH 1
#endif

#ifndef LOW
#define LOW 0
#endif

#ifndef INPUT
#define INPUT 1
#endif

#ifndef OUTPUT
#define OUTPUT 2
#endif

#ifndef INPUT_PULLUP
#define INPUT_PULLUP 3
#endif

// UART配置常量
#define SERIAL_8N1 0x800001c

// 基本类型定义
typedef unsigned char byte;
typedef bool boolean;

// 简化的String类
class String {
private:
    char* data;
    size_t len;
    size_t capacity;

public:
    String() : data(nullptr), len(0), capacity(0) {}

    String(const char* str) : data(nullptr), len(0), capacity(0) {
        if (str) {
            len = strlen(str);
            capacity = len + 1;
            data = (char*)malloc(capacity);
            if (data) {
                strcpy(data, str);
            }
        }
    }

    String(int value) : data(nullptr), len(0), capacity(0) {
        char buffer[32];
        snprintf(buffer, sizeof(buffer), "%d", value);
        *this = buffer;
    }

    String(unsigned int value) : data(nullptr), len(0), capacity(0) {
        char buffer[32];
        snprintf(buffer, sizeof(buffer), "%u", value);
        *this = buffer;
    }

    String(long value) : data(nullptr), len(0), capacity(0) {
        char buffer[32];
        snprintf(buffer, sizeof(buffer), "%ld", value);
        *this = buffer;
    }

    String(unsigned long value) : data(nullptr), len(0), capacity(0) {
        char buffer[32];
        snprintf(buffer, sizeof(buffer), "%lu", value);
        *this = buffer;
    }

    String(float value) : data(nullptr), len(0), capacity(0) {
        char buffer[32];
        snprintf(buffer, sizeof(buffer), "%.2f", value);
        *this = buffer;
    }

    String(double value) : data(nullptr), len(0), capacity(0) {
        char buffer[32];
        snprintf(buffer, sizeof(buffer), "%.2f", value);
        *this = buffer;
    }

    String(const String& other) : data(nullptr), len(0), capacity(0) {
        if (other.data) {
            len = other.len;
            capacity = other.capacity;
            data = (char*)malloc(capacity);
            if (data) {
                strcpy(data, other.data);
            }
        }
    }

    ~String() {
        if (data) {
            free(data);
        }
    }

    String& operator=(const char* str) {
        if (data) {
            free(data);
            data = nullptr;
        }
        if (str) {
            len = strlen(str);
            capacity = len + 1;
            data = (char*)malloc(capacity);
            if (data) {
                strcpy(data, str);
            }
        } else {
            len = 0;
            capacity = 0;
        }
        return *this;
    }

    String& operator=(const String& other) {
        if (this != &other) {
            if (data) {
                free(data);
                data = nullptr;
            }
            if (other.data) {
                len = other.len;
                capacity = other.capacity;
                data = (char*)malloc(capacity);
                if (data) {
                    strcpy(data, other.data);
                }
            } else {
                len = 0;
                capacity = 0;
            }
        }
        return *this;
    }

    const char* c_str() const {
        return data ? data : "";
    }

    size_t length() const {
        return len;
    }

    bool isEmpty() const {
        return len == 0;
    }

    int indexOf(const char* str) const {
        if (!data || !str) return -1;
        char* pos = strstr(data, str);
        return pos ? (pos - data) : -1;
    }

    int indexOf(const String& str) const {
        return indexOf(str.c_str());
    }

    // ArduinoJson需要的write方法
    size_t write(uint8_t c) {
        char ch = (char)c;
        *this += String(&ch, 1);
        return 1;
    }

    size_t write(const uint8_t* buffer, size_t size) {
        if (!buffer || size == 0) return 0;

        String temp((const char*)buffer, size);
        *this += temp;
        return size;
    }

    // 构造函数重载，支持指定长度
    String(const char* str, size_t length) : data(nullptr), len(0), capacity(0) {
        if (str && length > 0) {
            len = length;
            capacity = len + 1;
            data = (char*)malloc(capacity);
            if (data) {
                memcpy(data, str, len);
                data[len] = '\0';
            }
        }
    }

private:
    mutable size_t read_pos = 0;  // 用于read方法的位置跟踪

public:
    // ArduinoJson需要的read方法
    int read() const {
        if (read_pos >= len || !data) {
            return -1;
        }
        return (unsigned char)data[read_pos++];
    }

    // 重置读取位置
    void resetReadPos() const {
        read_pos = 0;
    }

    bool operator==(const char* str) const {
        if (!data && !str) return true;
        if (!data || !str) return false;
        return strcmp(data, str) == 0;
    }

    bool operator==(const String& other) const {
        return operator==(other.c_str());
    }

    String operator+(const char* str) const {
        if (!str) return *this;

        size_t newLen = len + strlen(str);
        String result;
        result.capacity = newLen + 1;
        result.data = (char*)malloc(result.capacity);
        result.len = newLen;

        if (result.data) {
            if (data) {
                strcpy(result.data, data);
                strcat(result.data, str);
            } else {
                strcpy(result.data, str);
            }
        }
        return result;
    }

    String operator+(const String& other) const {
        return operator+(other.c_str());
    }

    String& operator+=(const char* str) {
        if (str) {
            size_t strLen = strlen(str);
            size_t newLen = len + strLen;

            if (newLen + 1 > capacity) {
                capacity = newLen + 1;
                char* newData = (char*)realloc(data, capacity);
                if (newData) {
                    data = newData;
                } else {
                    return *this; // 内存分配失败
                }
            }

            if (data) {
                strcat(data, str);
                len = newLen;
            }
        }
        return *this;
    }

    String& operator+=(const String& other) {
        return operator+=(other.c_str());
    }

    // 比较操作符（std::map需要）
    bool operator<(const String& other) const {
        return strcmp(c_str(), other.c_str()) < 0;
    }

    bool operator>(const String& other) const {
        return strcmp(c_str(), other.c_str()) > 0;
    }

    bool operator<=(const String& other) const {
        return strcmp(c_str(), other.c_str()) <= 0;
    }

    bool operator>=(const String& other) const {
        return strcmp(c_str(), other.c_str()) >= 0;
    }
};

// ArduinoJson支持暂时禁用，避免兼容性问题
// TODO: 在核心功能完成后再实现ArduinoJson支持

// String的全局操作符
inline String operator+(const char* lhs, const String& rhs) {
    String result(lhs);
    return result + rhs;
}

// SerialClass的String方法实现
inline void SerialClass::print(const String& str) {
    printf("%s", str.c_str());
    fflush(stdout);
}

inline void SerialClass::println(const String& str) {
    printf("%s\n", str.c_str());
    fflush(stdout);
}

// IPAddress的println实现将在IPAddress类定义之后

// 时间函数
unsigned long millis();
unsigned long micros();
void delay(unsigned long ms);
void delayMicroseconds(unsigned int us);

// GPIO函数声明
void pinMode(int pin, int mode);
void digitalWrite(int pin, int value);
int digitalRead(int pin);
int analogRead(int pin);
void analogWrite(int pin, int value);

// ESP对象兼容性
class ESPClass {
public:
    size_t getHeapSize() {
        return heap_caps_get_total_size(MALLOC_CAP_DEFAULT);
    }

    size_t getFreeHeap() {
        return heap_caps_get_free_size(MALLOC_CAP_DEFAULT);
    }

    size_t getMinFreeHeap() {
        return heap_caps_get_minimum_free_size(MALLOC_CAP_DEFAULT);
    }

    void restart() {
        esp_restart();
    }

    uint32_t getChipId() {
        return 0; // ESP32-P4暂时返回0
    }

    const char* getChipModel() {
        return "ESP32-P4";
    }

    uint8_t getChipRevision() {
        return 0; // ESP32-P4暂时返回0
    }

    uint32_t getCpuFreqMHz() {
        return 360; // ESP32-P4默认频率
    }

    uint32_t getFlashChipSize() {
        return 16 * 1024 * 1024; // 假设16MB Flash
    }

    uint32_t getFlashChipSpeed() {
        return 80000000; // 80MHz
    }

    uint32_t getSketchSize() {
        return 1024 * 1024; // 假设1MB应用大小
    }

    uint32_t getFreeSketchSpace() {
        return getFlashChipSize() - getSketchSize();
    }

    // getChipModel已在上面定义

    uint32_t getEfuseMac() {
        return 0; // 简化实现
    }
};

extern ESPClass ESP;

// HardwareSerial兼容性类
class HardwareSerial {
private:
    uart_port_t uart_num;

public:
    HardwareSerial(int uart_number) : uart_num((uart_port_t)uart_number) {}

    void begin(unsigned long baud, uint32_t config = 0, int8_t rxPin = -1, int8_t txPin = -1) {
        uart_config_t uart_config = {
            .baud_rate = (int)baud,
            .data_bits = UART_DATA_8_BITS,
            .parity = UART_PARITY_DISABLE,
            .stop_bits = UART_STOP_BITS_1,
            .flow_ctrl = UART_HW_FLOWCTRL_DISABLE,
            .rx_flow_ctrl_thresh = 122,
        };

        uart_param_config(uart_num, &uart_config);

        if (rxPin >= 0 && txPin >= 0) {
            uart_set_pin(uart_num, txPin, rxPin, UART_PIN_NO_CHANGE, UART_PIN_NO_CHANGE);
        }

        uart_driver_install(uart_num, 1024, 1024, 0, NULL, 0);
    }

    void end() {
        uart_driver_delete(uart_num);
    }

    size_t write(uint8_t c) {
        return uart_write_bytes(uart_num, &c, 1);
    }

    size_t write(const uint8_t* buffer, size_t size) {
        return uart_write_bytes(uart_num, buffer, size);
    }

    size_t print(const char* str) {
        return uart_write_bytes(uart_num, str, strlen(str));
    }

    size_t print(const String& str) {
        return print(str.c_str());
    }

    size_t println(const char* str) {
        size_t n = print(str);
        n += print("\n");
        return n;
    }

    size_t println(const String& str) {
        return println(str.c_str());
    }

    int available() {
        size_t available_bytes;
        uart_get_buffered_data_len(uart_num, &available_bytes);
        return (int)available_bytes;
    }

    int read() {
        uint8_t data;
        int len = uart_read_bytes(uart_num, &data, 1, 0);
        return len > 0 ? data : -1;
    }

    void flush() {
        uart_wait_tx_done(uart_num, portMAX_DELAY);
    }
};

// 全局HardwareSerial对象
extern HardwareSerial Serial1;
extern HardwareSerial Serial2;

// IPAddress的println实现将在WiFi兼容层中定义

#endif // M_SERIAL_COMPAT_H
