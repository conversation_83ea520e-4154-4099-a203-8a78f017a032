Command: ninja flash
[1/5] cd /Users/<USER>/Documents/Arduino/libraries/ESP32-S3-Touch-LCD-4.3-Demo/Arduino/examples/esp32P4/build/esp-idf/esptool_py && /Users/<USER>/.espressif/python_env/idf5.5_py3.9_env/bin/python /Users/<USER>/esp/v5.5/esp-idf/components/partition_table/check_sizes.py --offset 0x8000 partition --type app /Users/<USER>/Documents/Arduino/libraries/ESP32-S3-Touch-LCD-4.3-Demo/Arduino/examples/esp32P4/build/partition_table/partition-table.bin /Users/<USER>/Documents/Arduino/libraries/ESP32-S3-Touch-LCD-4.3-Demo/Arduino/examples/esp32P4/build/HelloWorld.bin
HelloWorld.bin binary size 0x3b240 bytes. Smallest app partition is 0x800000 bytes. 0x7c4dc0 bytes (97%) free.
[2/5] Performing build step for 'bootloader'
[1/1] cd /Users/<USER>/Documents/Arduino/libraries/ESP32-S3-Touch-LCD-4.3-Demo/Arduino/examples/esp32P4/build/bootloader/esp-idf/esptool_py && /Users/<USER>/.espressif/python_env/idf5.5_py3.9_env/bin/python /Users/<USER>/esp/v5.5/esp-idf/components/partition_table/check_sizes.py --offset 0x8000 bootloader 0x2000 /Users/<USER>/Documents/Arduino/libraries/ESP32-S3-Touch-LCD-4.3-Demo/Arduino/examples/esp32P4/build/bootloader/bootloader.bin
Bootloader binary size 0x5770 bytes. 0x890 bytes (9%) free.
[3/5] No install step for 'bootloader'
[4/5] Completed 'bootloader'
[4/5] cd /Users/<USER>/esp/v5.5/esp-idf/components/esptool_py && /Users/<USER>/.espressif/tools/cmake/3.30.2/CMake.app/Contents/bin/cmake -D IDF_PATH=/Users/<USER>/esp/v5.5/esp-idf -D "SERIAL_TOOL=/Users/<USER>/.espressif/python_env/idf5.5_py3.9_env/bin/python;;/Users/<USER>/esp/v5.5/esp-idf/components/esptool_py/esptool/esptool.py;--chip;esp32p4" -D "SERIAL_TOOL_ARGS=--before=default_reset;--after=hard_reset;write_flash;@flash_args" -D WORKING_DIRECTORY=/Users/<USER>/Documents/Arduino/libraries/ESP32-S3-Touch-LCD-4.3-Demo/Arduino/examples/esp32P4/build -P /Users/<USER>/esp/v5.5/esp-idf/components/esptool_py/run_serial_tool.cmake
esptool.py --chip esp32p4 -p /dev/cu.wchusbserial5A7A0449951 -b 460800 --before=default_reset --after=hard_reset write_flash --flash_mode dio --flash_freq 80m --flash_size 16MB 0x2000 bootloader/bootloader.bin 0x10000 HelloWorld.bin 0x8000 partition_table/partition-table.bin
esptool.py v4.9.0
Serial port /dev/cu.wchusbserial5A7A0449951
Connecting....
Chip is ESP32-P4 (revision v1.0)
Features: High-Performance MCU
Crystal is 40MHz
MAC: 30:ed:a0:e2:12:40
Uploading stub...
Running stub...
Stub running...
Changing baud rate to 460800
Changed.
Configuring flash size...
Flash will be erased from 0x00002000 to 0x00007fff...
Flash will be erased from 0x00010000 to 0x0004bfff...
Flash will be erased from 0x00008000 to 0x00008fff...
SHA digest in image updated
Compressed 22384 bytes to 13738...
Writing at 0x00002000... (100 %)
Wrote 22384 bytes (13738 compressed) at 0x00002000 in 0.6 seconds (effective 315.3 kbit/s)...
Hash of data verified.
Compressed 242240 bytes to 122930...
Writing at 0x00010000... (12 %)
Writing at 0x0001b64b... (25 %)
Writing at 0x000226a0... (37 %)
Writing at 0x00029894... (50 %)
Writing at 0x00031ac6... (62 %)
Writing at 0x0003883a... (75 %)
Writing at 0x0003f520... (87 %)
Writing at 0x0004676f... (100 %)
Wrote 242240 bytes (122930 compressed) at 0x00010000 in 3.1 seconds (effective 616.2 kbit/s)...
Hash of data verified.
Compressed 3072 bytes to 161...
Writing at 0x00008000... (100 %)
Wrote 3072 bytes (161 compressed) at 0x00008000 in 0.0 seconds (effective 572.5 kbit/s)...
Hash of data verified.

Leaving...
Hard resetting via RTS pin...
