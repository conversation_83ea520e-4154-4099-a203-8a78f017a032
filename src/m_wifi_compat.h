#ifndef M_WIFI_COMPAT_H
#define M_WIFI_COMPAT_H

// ESP32-P4 WiFi兼容层
// 包装ESP-IDF WiFi API以提供Arduino WiFi.h兼容接口

// 强制使用ESP32-P4代码路径
#include "esp_wifi.h"
#include "esp_event.h"
#include "esp_netif.h"
#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/event_groups.h"
#include "lwip/ip_addr.h"
#include "m_serial_compat.h"

// WiFi状态枚举（兼容Arduino）
typedef enum {
    WL_NO_SHIELD        = 255,   // for compatibility with WiFi Shield library
    WL_IDLE_STATUS      = 0,
    WL_NO_SSID_AVAIL    = 1,
    WL_SCAN_COMPLETED   = 2,
    WL_CONNECTED        = 3,
    WL_CONNECT_FAILED   = 4,
    WL_CONNECTION_LOST  = 5,
    WL_DISCONNECTED     = 6
} wl_status_t;

// WiFi模式枚举
typedef enum {
    WIFI_OFF = 0,
    WIFI_STA = 1,
    WIFI_AP = 2,
    WIFI_AP_STA = 3
} wifi_mode_t_compat;

// Arduino兼容常量
#define WIFI_STA WIFI_STA

// IP地址类（简化版）
class IPAddress {
private:
    uint32_t _address;
    
public:
    IPAddress() : _address(0) {}
    IPAddress(uint8_t first_octet, uint8_t second_octet, uint8_t third_octet, uint8_t fourth_octet) {
        _address = (uint32_t(fourth_octet) << 24) | (uint32_t(third_octet) << 16) | 
                   (uint32_t(second_octet) << 8) | uint32_t(first_octet);
    }
    IPAddress(uint32_t address) : _address(address) {}
    
    uint8_t operator[](int index) const {
        return (_address >> (8 * index)) & 0xFF;
    }
    
    uint32_t operator=(uint32_t address) {
        _address = address;
        return _address;
    }
    
    bool operator==(const IPAddress& addr) const {
        return _address == addr._address;
    }
    
    String toString() const {
        return String(_address & 0xFF) + "." + 
               String((_address >> 8) & 0xFF) + "." + 
               String((_address >> 16) & 0xFF) + "." + 
               String((_address >> 24) & 0xFF);
    }
    
    uint32_t toInt() const { return _address; }
};

#ifdef CONFIG_IDF_TARGET_ESP32P4

// WiFi事件位
#define WIFI_CONNECTED_BIT    BIT0
#define WIFI_FAIL_BIT         BIT1
#define WIFI_DISCONNECTED_BIT BIT2

class WiFiClass {
private:
    static WiFiClass* instance;
    esp_netif_t* sta_netif;
    esp_netif_t* ap_netif;
    EventGroupHandle_t wifi_event_group;
    bool initialized;
    wl_status_t current_status;
    String current_ssid;
    String current_password;
    int retry_count;
    static const int MAX_RETRY = 5;
    
    // 静态事件处理函数
    static void wifi_event_handler(void* arg, esp_event_base_t event_base,
                                   int32_t event_id, void* event_data);
    static void ip_event_handler(void* arg, esp_event_base_t event_base,
                                 int32_t event_id, void* event_data);
    
    // 内部方法
    esp_err_t wifi_init();
    void update_status(wl_status_t status);
    
public:
    WiFiClass();
    ~WiFiClass();
    
    // 基本连接方法
    wl_status_t begin(const char* ssid, const char* passphrase = nullptr);
    wl_status_t begin(const String& ssid, const String& passphrase = "");
    void disconnect(bool wifioff = false);
    void end();
    
    // 状态查询
    wl_status_t status();
    bool isConnected();
    
    // 网络信息
    IPAddress localIP();
    IPAddress gatewayIP();
    IPAddress subnetMask();
    IPAddress dnsIP(uint8_t dns_no = 0);
    
    // SSID和网络信息
    String SSID();
    String psk();
    uint8_t* BSSID();
    int32_t RSSI();
    uint8_t encryptionType();
    
    // AP模式方法
    bool softAP(const char* ssid, const char* passphrase = nullptr, int channel = 1, int ssid_hidden = 0, int max_connection = 4);
    bool softAPdisconnect(bool wifioff = false);
    IPAddress softAPIP();
    
    // 扫描方法
    int16_t scanNetworks(bool async = false, bool show_hidden = false, bool passive = false, uint32_t max_ms_per_chan = 300);
    String SSID(uint8_t networkItem);
    int32_t RSSI(uint8_t networkItem);
    uint8_t encryptionType(uint8_t networkItem);
    
    // 配置方法
    bool config(IPAddress local_ip, IPAddress gateway, IPAddress subnet, IPAddress dns1 = IPAddress(0, 0, 0, 0), IPAddress dns2 = IPAddress(0, 0, 0, 0));
    bool setDNS(IPAddress dns1, IPAddress dns2 = IPAddress(0, 0, 0, 0));
    
    // 电源管理
    bool setSleep(bool enable);
    bool getSleep();
    
    // MAC地址
    String macAddress();
    uint8_t* macAddress(uint8_t* mac);
    
    // 事件回调
    void onEvent(void (*cb)(int32_t event));

    // WiFi模式设置
    bool mode(wifi_mode_t_compat mode);
    
    // 静态实例获取
    static WiFiClass& getInstance();
};

// 全局WiFi对象
extern WiFiClass WiFi;

#else
// 非ESP32-P4平台，使用原生Arduino WiFi
extern WiFiClass WiFi;
#endif

// IPAddress println通过toString()方法实现

#endif // M_WIFI_COMPAT_H
