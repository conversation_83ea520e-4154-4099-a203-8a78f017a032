#!/usr/bin/env python3
"""
ESP32-P4 RS485通信诊断工具
用于诊断TTL转RS485板子(HVD75芯片)通信问题
"""

import serial
import time
import sys
import struct

def print_header():
    print("=" * 60)
    print("🔧 ESP32-P4 RS485通信诊断工具")
    print("   适用于HVD75芯片的TTL转RS485扩展板")
    print("=" * 60)

def check_hardware_config():
    print("\n📋 硬件配置检查:")
    print("   ESP32-P4引脚配置:")
    print("   ├─ UART2_TX_PIN: GPIO20 (ESP32-P4 TX → 扩展板 RX)")
    print("   ├─ UART2_RX_PIN: GPIO21 (ESP32-P4 RX ← 扩展板 TX)")
    print("   ├─ 供电: 3.3V (推荐) 或 5V")
    print("   └─ GND: 共地连接")
    print()
    print("   HVD75扩展板连接:")
    print("   ├─ VCC → ESP32-P4 3.3V")
    print("   ├─ GND → ESP32-P4 GND")
    print("   ├─ RX  → ESP32-P4 GPIO20 (TX)")
    print("   ├─ TX  → ESP32-P4 GPIO21 (RX)")
    print("   ├─ A+  → RS485设备 A+")
    print("   └─ B-  → RS485设备 B-")

def check_common_issues():
    print("\n⚠️  常见问题检查:")
    print("   1. 引脚连接问题:")
    print("      ❌ ESP32-P4 TX连接到扩展板TX (错误)")
    print("      ✅ ESP32-P4 TX连接到扩展板RX (正确)")
    print()
    print("   2. 供电问题:")
    print("      ❌ 扩展板供电不足或不稳定")
    print("      ✅ 使用3.3V稳定供电")
    print()
    print("   3. 波特率不匹配:")
    print("      ❌ ESP32-P4与RS485设备波特率不同")
    print("      ✅ 确保两端波特率一致(默认9600)")
    print()
    print("   4. RS485总线问题:")
    print("      ❌ A+/B-接线错误或松动")
    print("      ❌ 总线终端电阻缺失(长距离通信)")
    print("      ✅ 正确连接A+到A+, B-到B-")
    print()
    print("   5. 自动换向问题:")
    print("      ❌ 扩展板不支持自动换向，需要DE/RE控制")
    print("      ✅ HVD75通常支持自动换向")

def test_modbus_frame():
    print("\n🧪 Modbus帧格式测试:")
    
    # 构建标准Modbus RTU读取保持寄存器请求
    slave_addr = 0x01
    function_code = 0x03  # 读取保持寄存器
    start_addr = 0x0000
    quantity = 0x0001
    
    # 构建请求帧
    frame = struct.pack('>BBHH', slave_addr, function_code, start_addr, quantity)
    
    # 计算CRC16
    crc = calculate_crc16(frame)
    frame += struct.pack('<H', crc)  # CRC是小端序
    
    print(f"   测试帧 (读取从站{slave_addr}的寄存器{start_addr}):")
    print(f"   原始数据: {' '.join([f'{b:02X}' for b in frame])}")
    print(f"   帧长度: {len(frame)} 字节")
    print(f"   从站地址: 0x{slave_addr:02X}")
    print(f"   功能码: 0x{function_code:02X} (读取保持寄存器)")
    print(f"   起始地址: 0x{start_addr:04X}")
    print(f"   寄存器数量: 0x{quantity:04X}")
    print(f"   CRC16: 0x{crc:04X}")
    
    return frame

def calculate_crc16(data):
    """计算Modbus CRC16校验码"""
    crc = 0xFFFF
    for byte in data:
        crc ^= byte
        for _ in range(8):
            if crc & 0x0001:
                crc >>= 1
                crc ^= 0xA001
            else:
                crc >>= 1
    return crc

def test_serial_loopback():
    print("\n🔄 串口回环测试建议:")
    print("   1. 断开RS485设备连接")
    print("   2. 将扩展板的A+和B-短接")
    print("   3. 运行ESP32-P4程序发送数据")
    print("   4. 检查是否能接收到相同数据")
    print("   5. 如果回环成功，说明扩展板工作正常")

def suggest_debug_steps():
    print("\n🔍 调试步骤建议:")
    print("   1. 检查硬件连接:")
    print("      - 用万用表测量扩展板供电电压")
    print("      - 检查GPIO20/21与扩展板连接")
    print("      - 确认RS485 A+/B-连接正确")
    print()
    print("   2. 软件调试:")
    print("      - 启用详细日志输出")
    print("      - 检查UART驱动是否正确安装")
    print("      - 验证波特率配置")
    print()
    print("   3. 信号测试:")
    print("      - 用示波器观察GPIO20的TX信号")
    print("      - 检查RS485差分信号是否正常")
    print("      - 测试不同波特率")
    print()
    print("   4. 逐步排查:")
    print("      - 先测试UART基本收发")
    print("      - 再测试RS485回环")
    print("      - 最后连接实际设备")

def main():
    print_header()
    check_hardware_config()
    check_common_issues()
    test_frame = test_modbus_frame()
    test_serial_loopback()
    suggest_debug_steps()
    
    print("\n" + "=" * 60)
    print("🎯 关键检查点:")
    print("   1. GPIO20/21引脚配置是否统一")
    print("   2. 扩展板供电是否正常(3.3V)")
    print("   3. TX/RX连接是否交叉正确")
    print("   4. RS485设备地址和波特率是否匹配")
    print("   5. 是否需要终端电阻(长距离通信)")
    print("=" * 60)

if __name__ == "__main__":
    main()
