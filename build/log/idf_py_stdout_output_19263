Command: /Users/<USER>/.espressif/python_env/idf5.5_py3.9_env/bin/python /Users/<USER>/esp/v5.5/esp-idf/tools/idf_monitor.py -p /dev/cu.wchusbserial5A7A0449951 -b 115200 --toolchain-prefix riscv32-esp-elf- --target esp32p4 --revision 1 --decode-panic backtrace /Users/<USER>/Documents/Arduino/libraries/ESP32-S3-Touch-LCD-4.3-Demo/Arduino/examples/esp32P4/build/HelloWorld.elf /Users/<USER>/Documents/Arduino/libraries/ESP32-S3-Touch-LCD-4.3-Demo/Arduino/examples/esp32P4/build/bootloader/bootloader.elf -m '/Users/<USER>/.espressif/python_env/idf5.5_py3.9_env/bin/python' '/Users/<USER>/esp/v5.5/esp-idf/tools/idf.py'
ESP-ROM:esp32p4-eco2-20240710
Build:Jul 10 2024
rst:0x1 (POWERON),boot:0x30f (SPI_FAST_FLASH_BOOT)
SPI mode:DIO, clock div:1
load:0x4ff33ce0,len:0x164c
load:0x4ff29ed0,len:0xd64
load:0x4ff2cbd0,len:0x3364
entry 0x4ff29eda
[0;32mI (25) boot: ESP-IDF v5.5-dirty 2nd stage bootloader[0m
[0;32mI (26) boot: compile time Jul 29 2025 12:48:23[0m
[0;32mI (26) boot: Multicore bootloader[0m
[0;32mI (27) boot: chip revision: v1.0[0m
[0;32mI (29) boot: efuse block revision: v0.3[0m
[0;32mI (33) boot.esp32p4: SPI Speed      : 80MHz[0m
[0;32mI (36) boot.esp32p4: SPI Mode       : DIO[0m
[0;32mI (40) boot.esp32p4: SPI Flash Size : 16MB[0m
[0;32mI (44) boot: Enabling RNG early entropy source...[0m
[0;32mI (49) boot: Partition Table:[0m
[0;32mI (51) boot: ## Label            Usage          Type ST Offset   Length[0m
[0;32mI (57) boot:  0 nvs              WiFi data        01 02 00009000 00006000[0m
[0;32mI (64) boot:  1 phy_init         RF data          01 01 0000f000 00001000[0m
[0;32mI (70) boot:  2 factory          factory app      00 00 00010000 00800000[0m
[0;32mI (77) boot:  3 storage          Unknown data     01 82 00810000 00600000[0m
[0;32mI (83) boot:  4 coredump         Unknown data     01 03 00e10000 00010000[0m
[0;32mI (90) boot:  5 nvs_key          NVS keys         01 04 00e20000 00001000[0m
[0;32mI (96) boot:  6 data             Unknown data     01 81 00e30000 001d0000[0m
[0;32mI (104) boot: End of partition table[0m
[0;32mI (107) esp_image: segment 0: paddr=00010020 vaddr=40020020 size=0be58h ( 48728) map[0m
[0;32mI (123) esp_image: segment 1: paddr=0001be80 vaddr=30100000 size=00044h (    68) load[0m
[0;32mI (125) esp_image: segment 2: paddr=0001becc vaddr=4ff00000 size=0414ch ( 16716) load[0m
[0;32mI (133) esp_image: segment 3: paddr=00020020 vaddr=40000020 size=1dd40h (122176) map[0m
[0;32mI (158) esp_image: segment 4: paddr=0003dd68 vaddr=4ff0414c size=0b824h ( 47140) load[0m
[0;32mI (169) esp_image: segment 5: paddr=00049594 vaddr=4ff0f980 size=01da8h (  7592) load[0m
[0;32mI (175) boot: Loaded app from partition at offset 0x10000[0m
[0;32mI (175) boot: Disabling RNG early entropy source...[0m
[0;32mI (186) cpu_start: Multicore app[0m
[0;32mI (195) cpu_start: Pro cpu start user code[0m
[0;32mI (195) cpu_start: cpu freq: 360000000 Hz[0m
[0;32mI (196) app_init: Application information:[0m
[0;32mI (196) app_init: Project name:     HelloWorld[0m
[0;32mI (200) app_init: App version:      1[0m
[0;32mI (203) app_init: Compile time:     Jul 29 2025 12:48:17[0m
[0;32mI (208) app_init: ELF file SHA256:  3eeb394c4...[0m
[0;32mI (212) app_init: ESP-IDF:          v5.5-dirty[0m
[0;32mI (216) efuse_init: Min chip rev:     v0.1[0m
[0;32mI (220) efuse_init: Max chip rev:     v1.99 [0m
[0;32mI (224) efuse_init: Chip rev:         v1.0[0m
[0;32mI (228) heap_init: Initializing. RAM available for dynamic allocation:[0m
[0;32mI (234) heap_init: At 4FF12E90 len 00028130 (160 KiB): RAM[0m
[0;32mI (240) heap_init: At 4FF3AFC0 len 00004BF0 (18 KiB): RAM[0m
[0;32mI (245) heap_init: At 4FF40000 len 00060000 (384 KiB): RAM[0m
[0;32mI (250) heap_init: At 50108080 len 00007F80 (31 KiB): RTCRAM[0m
[0;32mI (255) heap_init: At 30100044 len 00001FBC (7 KiB): TCM[0m
[0;33mW (261) spi_flash: GigaDevice detected but related driver is not linked, please check option `SPI_FLASH_SUPPORT_GD_CHIP`[0m
[0;32mI (271) spi_flash: detected chip: generic[0m
[0;32mI (275) spi_flash: flash io: dio[0m
[0;32mI (278) main_task: Started on CPU0[0m
[0;32mI (318) main_task: Calling app_main()[0m
[0;32mI (318) RS485_LOOPBACK: === ESP32-P4 RS485回环测试程序 ===[0m
[0;32mI (318) RS485_LOOPBACK: 🎯 测试目标: 验证HVD75扩展板回环功能[0m
[0;32mI (318) RS485_LOOPBACK: 📋 测试条件:[0m
[0;32mI (328) RS485_LOOPBACK:    - HVD75扩展板A+和B-已短接[0m
[0;32mI (328) RS485_LOOPBACK:    - ESP32-P4 GPIO21 → 扩展板RX (TX_PIN)[0m
[0;32mI (338) RS485_LOOPBACK:    - ESP32-P4 GPIO20 ← 扩展板TX (RX_PIN)[0m
[0;32mI (348) RS485_LOOPBACK:    - 3.3V供电正常[0m
[0;32mI (348) RS485_LOOPBACK: 🚀 开始测试...[0m

[0;32mI (348) RS485_LOOPBACK: 🔧 RS485回环测试开始[0m
[0;32mI (358) RS485_LOOPBACK:    UART: 2, TX: GPIO21, RX: GPIO20, 波特率: 9600[0m
[0;32mI (368) RS485_LOOPBACK:    前提条件: HVD75扩展板A+和B-已短接[0m
[0;32mI (368) RS485_LOOPBACK: ✅ UART配置完成[0m
[0;32mI (378) RS485_LOOPBACK: === 第1次回环测试 ===[0m
[0;32mI (378) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (388) main_task: Returned from app_main()[0m
[0;32mI (448) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (448) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (448) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (1468) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (1468) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (1468) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (1468) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (1468) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (1478) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (1478) RS485_LOOPBACK: 📊 统计: 0/1 成功 (0.0%)[0m
[0;32mI (1488) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (4488) RS485_LOOPBACK: === 第2次回环测试 ===[0m
[0;32mI (4488) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (4548) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (4548) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (4558) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (5578) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (5578) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (5578) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (5578) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (5578) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (5588) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (5588) RS485_LOOPBACK: 📊 统计: 0/2 成功 (0.0%)[0m
[0;32mI (5598) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (8598) RS485_LOOPBACK: === 第3次回环测试 ===[0m
[0;32mI (8598) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (8658) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (8658) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (8668) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (9688) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (9688) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (9688) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (9688) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (9688) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (9698) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (9698) RS485_LOOPBACK: 📊 统计: 0/3 成功 (0.0%)[0m
[0;32mI (9708) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (12708) RS485_LOOPBACK: === 第4次回环测试 ===[0m
[0;32mI (12708) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (12768) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (12768) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (12778) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (13798) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (13798) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (13798) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (13798) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (13798) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (13808) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (13808) RS485_LOOPBACK: 📊 统计: 0/4 成功 (0.0%)[0m
[0;32mI (13818) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (16818) RS485_LOOPBACK: === 第5次回环测试 ===[0m
[0;32mI (16818) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (16878) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (16878) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (16888) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (17908) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (17908) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (17908) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (17908) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (17908) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (17918) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (17918) RS485_LOOPBACK: 📊 统计: 0/5 成功 (0.0%)[0m
[0;32mI (17928) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (20928) RS485_LOOPBACK: === 第6次回环测试 ===[0m
[0;32mI (20928) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (20988) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (20988) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (20988) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (22008) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (22008) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (22008) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (22008) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (22008) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (22018) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (22018) RS485_LOOPBACK: 📊 统计: 0/6 成功 (0.0%)[0m
[0;32mI (22028) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (25028) RS485_LOOPBACK: === 第7次回环测试 ===[0m
[0;32mI (25028) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (25088) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (25088) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (25098) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (26118) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (26118) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (26118) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (26118) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (26118) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (26128) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (26128) RS485_LOOPBACK: 📊 统计: 0/7 成功 (0.0%)[0m
[0;32mI (26138) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (29138) RS485_LOOPBACK: === 第8次回环测试 ===[0m
[0;32mI (29138) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (29198) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (29198) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (29208) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (30228) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (30228) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (30228) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (30228) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (30228) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (30238) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (30238) RS485_LOOPBACK: 📊 统计: 0/8 成功 (0.0%)[0m
[0;32mI (30248) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (33248) RS485_LOOPBACK: === 第9次回环测试 ===[0m
[0;32mI (33248) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (33308) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (33308) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (33318) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (34338) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (34338) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (34338) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (34338) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (34338) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (34348) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (34348) RS485_LOOPBACK: 📊 统计: 0/9 成功 (0.0%)[0m
[0;32mI (34358) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (37358) RS485_LOOPBACK: === 第10次回环测试 ===[0m
[0;32mI (37358) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (37418) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (37418) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (37428) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (38448) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (38448) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (38448) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (38448) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (38448) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (38458) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (38458) RS485_LOOPBACK: 📊 统计: 0/10 成功 (0.0%)[0m
[0;32mI (38468) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (41468) RS485_LOOPBACK: === 第11次回环测试 ===[0m
[0;32mI (41468) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (41528) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (41528) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (41528) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (42548) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (42548) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (42548) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (42548) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (42548) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (42558) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (42558) RS485_LOOPBACK: 📊 统计: 0/11 成功 (0.0%)[0m
[0;32mI (42568) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (45568) RS485_LOOPBACK: === 第12次回环测试 ===[0m
[0;32mI (45568) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (45628) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (45628) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (45638) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (46658) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (46658) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (46658) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (46658) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (46658) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (46668) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (46668) RS485_LOOPBACK: 📊 统计: 0/12 成功 (0.0%)[0m
[0;32mI (46678) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (49678) RS485_LOOPBACK: === 第13次回环测试 ===[0m
[0;32mI (49678) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (49738) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (49738) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (49748) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (50768) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (50768) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (50768) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (50768) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (50768) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (50778) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (50778) RS485_LOOPBACK: 📊 统计: 0/13 成功 (0.0%)[0m
[0;32mI (50788) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (53788) RS485_LOOPBACK: === 第14次回环测试 ===[0m
[0;32mI (53788) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (53848) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (53848) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (53858) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (54878) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (54878) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (54878) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (54878) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (54878) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (54888) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (54888) RS485_LOOPBACK: 📊 统计: 0/14 成功 (0.0%)[0m
[0;32mI (54898) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (57898) RS485_LOOPBACK: === 第15次回环测试 ===[0m
[0;32mI (57898) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (57958) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (57958) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (57968) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (58988) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (58988) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (58988) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (58988) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (58988) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (58998) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (58998) RS485_LOOPBACK: 📊 统计: 0/15 成功 (0.0%)[0m
[0;32mI (59008) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (62008) RS485_LOOPBACK: === 第16次回环测试 ===[0m
[0;32mI (62008) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (62068) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (62068) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (62068) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (63088) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (63088) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (63088) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (63088) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (63088) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (63098) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (63098) RS485_LOOPBACK: 📊 统计: 0/16 成功 (0.0%)[0m
[0;32mI (63108) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (66108) RS485_LOOPBACK: === 第17次回环测试 ===[0m
[0;32mI (66108) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (66168) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (66168) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (66178) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (67198) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (67198) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (67198) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (67198) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (67198) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (67208) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (67208) RS485_LOOPBACK: 📊 统计: 0/17 成功 (0.0%)[0m
[0;32mI (67218) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (70218) RS485_LOOPBACK: === 第18次回环测试 ===[0m
[0;32mI (70218) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (70278) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (70278) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (70288) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (71308) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (71308) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (71308) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (71308) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (71308) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (71318) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (71318) RS485_LOOPBACK: 📊 统计: 0/18 成功 (0.0%)[0m
[0;32mI (71328) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (74328) RS485_LOOPBACK: === 第19次回环测试 ===[0m
[0;32mI (74328) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (74388) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (74388) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (74398) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (75418) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (75418) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (75418) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (75418) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (75418) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (75428) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (75428) RS485_LOOPBACK: 📊 统计: 0/19 成功 (0.0%)[0m
[0;32mI (75438) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (78438) RS485_LOOPBACK: === 第20次回环测试 ===[0m
[0;32mI (78438) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (78498) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (78498) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (78508) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (79528) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (79528) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (79528) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (79528) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (79528) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (79538) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (79538) RS485_LOOPBACK: 📊 统计: 0/20 成功 (0.0%)[0m
[0;32mI (79548) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (82548) RS485_LOOPBACK: === 第21次回环测试 ===[0m
[0;32mI (82548) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (82608) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (82608) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (82608) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (83628) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (83628) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (83628) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (83628) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (83628) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (83638) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (83638) RS485_LOOPBACK: 📊 统计: 0/21 成功 (0.0%)[0m
[0;32mI (83648) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (86648) RS485_LOOPBACK: === 第22次回环测试 ===[0m
[0;32mI (86648) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (86708) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (86708) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (86718) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (87738) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (87738) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (87738) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (87738) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (87738) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (87748) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (87748) RS485_LOOPBACK: 📊 统计: 0/22 成功 (0.0%)[0m
[0;32mI (87758) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (90758) RS485_LOOPBACK: === 第23次回环测试 ===[0m
[0;32mI (90758) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (90818) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (90818) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (90828) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (91848) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (91848) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (91848) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (91848) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (91848) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (91858) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (91858) RS485_LOOPBACK: 📊 统计: 0/23 成功 (0.0%)[0m
[0;32mI (91868) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (94868) RS485_LOOPBACK: === 第24次回环测试 ===[0m
[0;32mI (94868) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (94928) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (94928) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (94938) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (95958) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (95958) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (95958) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (95958) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (95958) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (95968) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (95968) RS485_LOOPBACK: 📊 统计: 0/24 成功 (0.0%)[0m
[0;32mI (95978) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (98978) RS485_LOOPBACK: === 第25次回环测试 ===[0m
[0;32mI (98978) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (99038) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (99038) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (99048) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (100068) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (100068) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (100068) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (100068) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (100068) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (100078) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (100078) RS485_LOOPBACK: 📊 统计: 0/25 成功 (0.0%)[0m
[0;32mI (100088) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (103098) RS485_LOOPBACK: === 第26次回环测试 ===[0m
[0;32mI (103098) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (103158) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (103158) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (103158) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (104178) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (104178) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (104178) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (104178) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (104178) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (104188) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (104188) RS485_LOOPBACK: 📊 统计: 0/26 成功 (0.0%)[0m
[0;32mI (104198) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (107208) RS485_LOOPBACK: === 第27次回环测试 ===[0m
[0;32mI (107208) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (107268) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (107268) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (107278) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (108298) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (108298) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (108298) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (108298) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (108298) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (108308) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (108308) RS485_LOOPBACK: 📊 统计: 0/27 成功 (0.0%)[0m
[0;32mI (108318) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (111328) RS485_LOOPBACK: === 第28次回环测试 ===[0m
[0;32mI (111328) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (111388) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (111388) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (111398) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (112418) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (112418) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (112418) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (112418) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (112418) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (112428) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (112428) RS485_LOOPBACK: 📊 统计: 0/28 成功 (0.0%)[0m
[0;32mI (112438) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (115448) RS485_LOOPBACK: === 第29次回环测试 ===[0m
[0;32mI (115448) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (115508) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (115508) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (115518) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (116538) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (116538) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (116538) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (116538) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (116538) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (116548) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (116548) RS485_LOOPBACK: 📊 统计: 0/29 成功 (0.0%)[0m
[0;32mI (116558) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (119568) RS485_LOOPBACK: === 第30次回环测试 ===[0m
[0;32mI (119568) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (119628) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (119628) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (119638) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (120658) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (120658) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (120658) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (120658) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (120658) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (120668) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (120668) RS485_LOOPBACK: 📊 统计: 0/30 成功 (0.0%)[0m
[0;32mI (120678) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (123688) RS485_LOOPBACK: === 第31次回环测试 ===[0m
[0;32mI (123688) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (123748) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (123748) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (123748) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (124768) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (124768) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (124768) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (124768) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (124768) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (124778) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (124778) RS485_LOOPBACK: 📊 统计: 0/31 成功 (0.0%)[0m
[0;32mI (124788) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (127798) RS485_LOOPBACK: === 第32次回环测试 ===[0m
[0;32mI (127798) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (127858) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (127858) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (127868) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (128888) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (128888) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (128888) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (128888) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (128888) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (128898) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (128898) RS485_LOOPBACK: 📊 统计: 0/32 成功 (0.0%)[0m
[0;32mI (128908) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (131918) RS485_LOOPBACK: === 第33次回环测试 ===[0m
[0;32mI (131918) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (131978) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (131978) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (131988) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (133008) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (133008) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (133008) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (133008) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (133008) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (133018) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (133018) RS485_LOOPBACK: 📊 统计: 0/33 成功 (0.0%)[0m
[0;32mI (133028) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (136038) RS485_LOOPBACK: === 第34次回环测试 ===[0m
[0;32mI (136038) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (136098) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (136098) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (136108) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (137128) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (137128) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (137128) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (137128) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (137128) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (137138) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (137138) RS485_LOOPBACK: 📊 统计: 0/34 成功 (0.0%)[0m
[0;32mI (137148) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (140158) RS485_LOOPBACK: === 第35次回环测试 ===[0m
[0;32mI (140158) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (140218) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (140218) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (140228) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (141248) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (141248) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (141248) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (141248) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (141248) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (141258) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (141258) RS485_LOOPBACK: 📊 统计: 0/35 成功 (0.0%)[0m
[0;32mI (141268) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (144278) RS485_LOOPBACK: === 第36次回环测试 ===[0m
[0;32mI (144278) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (144338) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (144338) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (144338) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (145358) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (145358) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (145358) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (145358) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (145358) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (145368) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (145368) RS485_LOOPBACK: 📊 统计: 0/36 成功 (0.0%)[0m
[0;32mI (145378) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (148388) RS485_LOOPBACK: === 第37次回环测试 ===[0m
[0;32mI (148388) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (148448) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (148448) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (148458) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (149478) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (149478) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (149478) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (149478) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (149478) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (149488) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (149488) RS485_LOOPBACK: 📊 统计: 0/37 成功 (0.0%)[0m
[0;32mI (149498) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (152508) RS485_LOOPBACK: === 第38次回环测试 ===[0m
[0;32mI (152508) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (152568) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (152568) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (152578) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (153598) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (153598) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (153598) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (153598) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (153598) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (153608) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (153608) RS485_LOOPBACK: 📊 统计: 0/38 成功 (0.0%)[0m
[0;32mI (153618) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (156628) RS485_LOOPBACK: === 第39次回环测试 ===[0m
[0;32mI (156628) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (156688) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (156688) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (156698) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (157718) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (157718) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (157718) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (157718) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (157718) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (157728) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (157728) RS485_LOOPBACK: 📊 统计: 0/39 成功 (0.0%)[0m
[0;32mI (157738) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (160748) RS485_LOOPBACK: === 第40次回环测试 ===[0m
[0;32mI (160748) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (160808) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (160808) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (160818) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (161838) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (161838) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (161838) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (161838) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (161838) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (161848) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (161848) RS485_LOOPBACK: 📊 统计: 0/40 成功 (0.0%)[0m
[0;32mI (161858) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (164868) RS485_LOOPBACK: === 第41次回环测试 ===[0m
[0;32mI (164868) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (164928) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (164928) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (164928) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (165948) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (165948) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (165948) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (165948) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (165948) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (165958) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (165958) RS485_LOOPBACK: 📊 统计: 0/41 成功 (0.0%)[0m
[0;32mI (165968) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (168978) RS485_LOOPBACK: === 第42次回环测试 ===[0m
[0;32mI (168978) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (169038) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (169038) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (169048) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (170068) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (170068) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (170068) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (170068) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (170068) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (170078) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (170078) RS485_LOOPBACK: 📊 统计: 0/42 成功 (0.0%)[0m
[0;32mI (170088) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (173098) RS485_LOOPBACK: === 第43次回环测试 ===[0m
[0;32mI (173098) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (173158) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (173158) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (173168) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (174188) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (174188) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (174188) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (174188) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (174188) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (174198) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (174198) RS485_LOOPBACK: 📊 统计: 0/43 成功 (0.0%)[0m
[0;32mI (174208) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (177218) RS485_LOOPBACK: === 第44次回环测试 ===[0m
[0;32mI (177218) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (177278) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (177278) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (177288) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (178308) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (178308) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (178308) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (178308) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (178308) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (178318) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (178318) RS485_LOOPBACK: 📊 统计: 0/44 成功 (0.0%)[0m
[0;32mI (178328) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (181338) RS485_LOOPBACK: === 第45次回环测试 ===[0m
[0;32mI (181338) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (181398) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (181398) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (181408) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (182428) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (182428) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (182428) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (182428) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (182428) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (182438) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (182438) RS485_LOOPBACK: 📊 统计: 0/45 成功 (0.0%)[0m
[0;32mI (182448) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (185458) RS485_LOOPBACK: === 第46次回环测试 ===[0m
[0;32mI (185458) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (185518) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (185518) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (185518) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (186538) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (186538) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (186538) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (186538) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (186538) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (186548) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (186548) RS485_LOOPBACK: 📊 统计: 0/46 成功 (0.0%)[0m
[0;32mI (186558) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (189568) RS485_LOOPBACK: === 第47次回环测试 ===[0m
[0;32mI (189568) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (189628) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (189628) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (189638) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (190658) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (190658) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (190658) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (190658) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (190658) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (190668) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (190668) RS485_LOOPBACK: 📊 统计: 0/47 成功 (0.0%)[0m
[0;32mI (190678) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (193688) RS485_LOOPBACK: === 第48次回环测试 ===[0m
[0;32mI (193688) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (193748) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (193748) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (193758) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (194778) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (194778) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (194778) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (194778) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (194778) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (194788) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (194788) RS485_LOOPBACK: 📊 统计: 0/48 成功 (0.0%)[0m
[0;32mI (194798) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (197808) RS485_LOOPBACK: === 第49次回环测试 ===[0m
[0;32mI (197808) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (197868) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (197868) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (197878) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (198898) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (198898) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (198898) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (198898) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (198898) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (198908) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (198908) RS485_LOOPBACK: 📊 统计: 0/49 成功 (0.0%)[0m
[0;32mI (198918) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (201928) RS485_LOOPBACK: === 第50次回环测试 ===[0m
[0;32mI (201928) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (201988) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (201988) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (201998) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (203018) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (203018) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (203018) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (203018) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (203018) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (203028) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (203028) RS485_LOOPBACK: 📊 统计: 0/50 成功 (0.0%)[0m
[0;32mI (203038) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (206048) RS485_LOOPBACK: === 第51次回环测试 ===[0m
[0;32mI (206048) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (206108) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (206108) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (206108) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (207128) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (207128) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (207128) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (207128) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (207128) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (207138) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (207138) RS485_LOOPBACK: 📊 统计: 0/51 成功 (0.0%)[0m
[0;32mI (207148) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (210158) RS485_LOOPBACK: === 第52次回环测试 ===[0m
[0;32mI (210158) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (210218) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (210218) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (210228) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (211248) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (211248) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (211248) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (211248) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (211248) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (211258) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (211258) RS485_LOOPBACK: 📊 统计: 0/52 成功 (0.0%)[0m
[0;32mI (211268) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (214278) RS485_LOOPBACK: === 第53次回环测试 ===[0m
[0;32mI (214278) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (214338) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (214338) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (214348) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (215368) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (215368) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (215368) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (215368) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (215368) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (215378) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (215378) RS485_LOOPBACK: 📊 统计: 0/53 成功 (0.0%)[0m
[0;32mI (215388) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (218398) RS485_LOOPBACK: === 第54次回环测试 ===[0m
[0;32mI (218398) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (218458) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (218458) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (218468) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (219488) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (219488) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (219488) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (219488) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (219488) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (219498) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (219498) RS485_LOOPBACK: 📊 统计: 0/54 成功 (0.0%)[0m
[0;32mI (219508) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (222518) RS485_LOOPBACK: === 第55次回环测试 ===[0m
[0;32mI (222518) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (222578) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (222578) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (222588) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (223608) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (223608) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (223608) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (223608) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (223608) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (223618) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (223618) RS485_LOOPBACK: 📊 统计: 0/55 成功 (0.0%)[0m
[0;32mI (223628) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (226638) RS485_LOOPBACK: === 第56次回环测试 ===[0m
[0;32mI (226638) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (226698) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (226698) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (226698) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (227718) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (227718) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (227718) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (227718) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (227718) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (227728) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (227728) RS485_LOOPBACK: 📊 统计: 0/56 成功 (0.0%)[0m
[0;32mI (227738) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (230748) RS485_LOOPBACK: === 第57次回环测试 ===[0m
[0;32mI (230748) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (230808) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (230808) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (230818) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (231838) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (231838) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (231838) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (231838) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (231838) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (231848) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (231848) RS485_LOOPBACK: 📊 统计: 0/57 成功 (0.0%)[0m
[0;32mI (231858) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (234868) RS485_LOOPBACK: === 第58次回环测试 ===[0m
[0;32mI (234868) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (234928) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (234928) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (234938) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (235958) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (235958) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (235958) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (235958) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (235958) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (235968) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (235968) RS485_LOOPBACK: 📊 统计: 0/58 成功 (0.0%)[0m
[0;32mI (235978) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (238988) RS485_LOOPBACK: === 第59次回环测试 ===[0m
[0;32mI (238988) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (239048) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (239048) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (239058) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (240078) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (240078) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (240078) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (240078) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (240078) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (240088) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (240088) RS485_LOOPBACK: 📊 统计: 0/59 成功 (0.0%)[0m
[0;32mI (240098) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (243108) RS485_LOOPBACK: === 第60次回环测试 ===[0m
[0;32mI (243108) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (243168) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (243168) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (243178) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (244198) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (244198) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (244198) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (244198) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (244198) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (244208) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (244208) RS485_LOOPBACK: 📊 统计: 0/60 成功 (0.0%)[0m
[0;32mI (244218) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (247228) RS485_LOOPBACK: === 第61次回环测试 ===[0m
[0;32mI (247228) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (247288) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (247288) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (247288) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (248308) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (248308) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (248308) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (248308) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (248308) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (248318) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (248318) RS485_LOOPBACK: 📊 统计: 0/61 成功 (0.0%)[0m
[0;32mI (248328) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (251338) RS485_LOOPBACK: === 第62次回环测试 ===[0m
[0;32mI (251338) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (251398) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (251398) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (251408) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (252428) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (252428) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (252428) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (252428) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (252428) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (252438) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (252438) RS485_LOOPBACK: 📊 统计: 0/62 成功 (0.0%)[0m
[0;32mI (252448) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (255458) RS485_LOOPBACK: === 第63次回环测试 ===[0m
[0;32mI (255458) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (255518) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (255518) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (255528) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (256548) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (256548) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (256548) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (256548) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (256548) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (256558) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (256558) RS485_LOOPBACK: 📊 统计: 0/63 成功 (0.0%)[0m
[0;32mI (256568) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (259578) RS485_LOOPBACK: === 第64次回环测试 ===[0m
[0;32mI (259578) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (259638) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (259638) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (259648) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (260668) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (260668) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (260668) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (260668) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (260668) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (260678) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (260678) RS485_LOOPBACK: 📊 统计: 0/64 成功 (0.0%)[0m
[0;32mI (260688) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (263698) RS485_LOOPBACK: === 第65次回环测试 ===[0m
[0;32mI (263698) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (263758) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (263758) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (263768) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (264788) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (264788) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (264788) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (264788) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (264788) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (264798) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (264798) RS485_LOOPBACK: 📊 统计: 0/65 成功 (0.0%)[0m
[0;32mI (264808) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (267818) RS485_LOOPBACK: === 第66次回环测试 ===[0m
[0;32mI (267818) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (267878) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (267878) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (267878) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (268898) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (268898) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (268898) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (268898) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (268898) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (268908) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (268908) RS485_LOOPBACK: 📊 统计: 0/66 成功 (0.0%)[0m
[0;32mI (268918) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (271928) RS485_LOOPBACK: === 第67次回环测试 ===[0m
[0;32mI (271928) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (271988) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (271988) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (271998) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (273018) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (273018) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (273018) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (273018) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (273018) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (273028) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (273028) RS485_LOOPBACK: 📊 统计: 0/67 成功 (0.0%)[0m
[0;32mI (273038) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (276048) RS485_LOOPBACK: === 第68次回环测试 ===[0m
[0;32mI (276048) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (276108) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (276108) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (276118) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (277138) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (277138) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (277138) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (277138) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (277138) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (277148) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (277148) RS485_LOOPBACK: 📊 统计: 0/68 成功 (0.0%)[0m
[0;32mI (277158) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (280168) RS485_LOOPBACK: === 第69次回环测试 ===[0m
[0;32mI (280168) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (280228) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (280228) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (280238) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (281258) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (281258) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (281258) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (281258) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (281258) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (281268) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (281268) RS485_LOOPBACK: 📊 统计: 0/69 成功 (0.0%)[0m
[0;32mI (281278) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (284288) RS485_LOOPBACK: === 第70次回环测试 ===[0m
[0;32mI (284288) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (284348) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (284348) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (284358) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (285378) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (285378) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (285378) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (285378) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (285378) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (285388) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (285388) RS485_LOOPBACK: 📊 统计: 0/70 成功 (0.0%)[0m
[0;32mI (285398) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (288408) RS485_LOOPBACK: === 第71次回环测试 ===[0m
[0;32mI (288408) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (288468) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (288468) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (288468) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (289488) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (289488) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (289488) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (289488) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (289488) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (289498) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (289498) RS485_LOOPBACK: 📊 统计: 0/71 成功 (0.0%)[0m
[0;32mI (289508) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (292518) RS485_LOOPBACK: === 第72次回环测试 ===[0m
[0;32mI (292518) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (292578) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (292578) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (292588) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (293608) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (293608) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (293608) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (293608) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (293608) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (293618) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (293618) RS485_LOOPBACK: 📊 统计: 0/72 成功 (0.0%)[0m
[0;32mI (293628) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (296638) RS485_LOOPBACK: === 第73次回环测试 ===[0m
[0;32mI (296638) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (296698) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (296698) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (296708) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (297728) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (297728) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (297728) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (297728) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (297728) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (297738) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (297738) RS485_LOOPBACK: 📊 统计: 0/73 成功 (0.0%)[0m
[0;32mI (297748) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (300758) RS485_LOOPBACK: === 第74次回环测试 ===[0m
[0;32mI (300758) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (300818) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (300818) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (300828) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (301848) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (301848) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (301848) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (301848) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (301848) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (301858) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (301858) RS485_LOOPBACK: 📊 统计: 0/74 成功 (0.0%)[0m
[0;32mI (301868) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (304878) RS485_LOOPBACK: === 第75次回环测试 ===[0m
[0;32mI (304878) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (304938) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (304938) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (304948) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (305968) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (305968) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (305968) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (305968) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (305968) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (305978) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (305978) RS485_LOOPBACK: 📊 统计: 0/75 成功 (0.0%)[0m
[0;32mI (305988) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (308998) RS485_LOOPBACK: === 第76次回环测试 ===[0m
[0;32mI (308998) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (309058) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (309058) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (309058) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (310078) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (310078) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (310078) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (310078) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (310078) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (310088) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (310088) RS485_LOOPBACK: 📊 统计: 0/76 成功 (0.0%)[0m
[0;32mI (310098) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (313108) RS485_LOOPBACK: === 第77次回环测试 ===[0m
[0;32mI (313108) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (313168) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (313168) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (313178) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (314198) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (314198) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (314198) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (314198) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (314198) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (314208) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (314208) RS485_LOOPBACK: 📊 统计: 0/77 成功 (0.0%)[0m
[0;32mI (314218) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (317228) RS485_LOOPBACK: === 第78次回环测试 ===[0m
[0;32mI (317228) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (317288) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (317288) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (317298) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (318318) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (318318) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (318318) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (318318) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (318318) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (318328) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (318328) RS485_LOOPBACK: 📊 统计: 0/78 成功 (0.0%)[0m
[0;32mI (318338) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (321348) RS485_LOOPBACK: === 第79次回环测试 ===[0m
[0;32mI (321348) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (321408) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (321408) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (321418) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (322438) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (322438) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (322438) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (322438) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (322438) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (322448) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (322448) RS485_LOOPBACK: 📊 统计: 0/79 成功 (0.0%)[0m
[0;32mI (322458) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (325468) RS485_LOOPBACK: === 第80次回环测试 ===[0m
[0;32mI (325468) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (325528) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (325528) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (325538) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (326558) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (326558) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (326558) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (326558) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (326558) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (326568) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (326568) RS485_LOOPBACK: 📊 统计: 0/80 成功 (0.0%)[0m
[0;32mI (326578) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (329588) RS485_LOOPBACK: === 第81次回环测试 ===[0m
[0;32mI (329588) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (329648) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (329648) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (329648) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (330668) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (330668) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (330668) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (330668) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (330668) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (330678) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (330678) RS485_LOOPBACK: 📊 统计: 0/81 成功 (0.0%)[0m
[0;32mI (330688) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (333698) RS485_LOOPBACK: === 第82次回环测试 ===[0m
[0;32mI (333698) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (333758) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (333758) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (333768) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (334788) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (334788) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (334788) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (334788) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (334788) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (334798) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (334798) RS485_LOOPBACK: 📊 统计: 0/82 成功 (0.0%)[0m
[0;32mI (334808) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (337818) RS485_LOOPBACK: === 第83次回环测试 ===[0m
[0;32mI (337818) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (337878) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (337878) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (337888) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (338908) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (338908) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (338908) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (338908) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (338908) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (338918) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (338918) RS485_LOOPBACK: 📊 统计: 0/83 成功 (0.0%)[0m
[0;32mI (338928) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (341938) RS485_LOOPBACK: === 第84次回环测试 ===[0m
[0;32mI (341938) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (341998) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (341998) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (342008) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (343028) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (343028) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (343028) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (343028) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (343028) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (343038) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (343038) RS485_LOOPBACK: 📊 统计: 0/84 成功 (0.0%)[0m
[0;32mI (343048) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (346058) RS485_LOOPBACK: === 第85次回环测试 ===[0m
[0;32mI (346058) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (346118) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (346118) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (346128) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (347148) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (347148) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (347148) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (347148) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (347148) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (347158) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (347158) RS485_LOOPBACK: 📊 统计: 0/85 成功 (0.0%)[0m
[0;32mI (347168) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (350178) RS485_LOOPBACK: === 第86次回环测试 ===[0m
[0;32mI (350178) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (350238) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (350238) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (350238) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (351258) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (351258) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (351258) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (351258) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (351258) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (351268) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (351268) RS485_LOOPBACK: 📊 统计: 0/86 成功 (0.0%)[0m
[0;32mI (351278) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (354288) RS485_LOOPBACK: === 第87次回环测试 ===[0m
[0;32mI (354288) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (354348) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (354348) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (354358) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (355378) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (355378) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (355378) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (355378) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (355378) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (355388) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (355388) RS485_LOOPBACK: 📊 统计: 0/87 成功 (0.0%)[0m
[0;32mI (355398) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (358408) RS485_LOOPBACK: === 第88次回环测试 ===[0m
[0;32mI (358408) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (358468) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (358468) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (358478) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (359498) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (359498) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (359498) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (359498) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (359498) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (359508) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (359508) RS485_LOOPBACK: 📊 统计: 0/88 成功 (0.0%)[0m
[0;32mI (359518) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (362528) RS485_LOOPBACK: === 第89次回环测试 ===[0m
[0;32mI (362528) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (362588) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (362588) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (362598) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (363618) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (363618) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (363618) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (363618) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (363618) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (363628) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (363628) RS485_LOOPBACK: 📊 统计: 0/89 成功 (0.0%)[0m
[0;32mI (363638) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (366648) RS485_LOOPBACK: === 第90次回环测试 ===[0m
[0;32mI (366648) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (366708) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (366708) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (366718) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (367738) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (367738) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (367738) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (367738) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (367738) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (367748) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (367748) RS485_LOOPBACK: 📊 统计: 0/90 成功 (0.0%)[0m
[0;32mI (367758) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (370768) RS485_LOOPBACK: === 第91次回环测试 ===[0m
[0;32mI (370768) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (370828) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (370828) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (370828) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (371848) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (371848) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (371848) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (371848) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (371848) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (371858) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (371858) RS485_LOOPBACK: 📊 统计: 0/91 成功 (0.0%)[0m
[0;32mI (371868) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (374878) RS485_LOOPBACK: === 第92次回环测试 ===[0m
[0;32mI (374878) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (374938) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (374938) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (374948) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (375968) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (375968) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (375968) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (375968) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (375968) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (375978) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (375978) RS485_LOOPBACK: 📊 统计: 0/92 成功 (0.0%)[0m
[0;32mI (375988) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (378998) RS485_LOOPBACK: === 第93次回环测试 ===[0m
[0;32mI (378998) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (379058) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (379058) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (379068) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (380088) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (380088) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (380088) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (380088) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (380088) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (380098) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (380098) RS485_LOOPBACK: 📊 统计: 0/93 成功 (0.0%)[0m
[0;32mI (380108) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (383118) RS485_LOOPBACK: === 第94次回环测试 ===[0m
[0;32mI (383118) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (383178) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (383178) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (383188) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (384208) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (384208) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (384208) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (384208) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (384208) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (384218) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (384218) RS485_LOOPBACK: 📊 统计: 0/94 成功 (0.0%)[0m
[0;32mI (384228) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (387238) RS485_LOOPBACK: === 第95次回环测试 ===[0m
[0;32mI (387238) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (387298) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (387298) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (387308) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (388328) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (388328) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (388328) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (388328) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (388328) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (388338) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (388338) RS485_LOOPBACK: 📊 统计: 0/95 成功 (0.0%)[0m
[0;32mI (388348) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (391358) RS485_LOOPBACK: === 第96次回环测试 ===[0m
[0;32mI (391358) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (391418) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (391418) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (391418) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (392438) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (392438) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (392438) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (392438) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (392438) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (392448) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (392448) RS485_LOOPBACK: 📊 统计: 0/96 成功 (0.0%)[0m
[0;32mI (392458) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (395468) RS485_LOOPBACK: === 第97次回环测试 ===[0m
[0;32mI (395468) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (395528) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (395528) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (395538) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (396558) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (396558) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (396558) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (396558) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (396558) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (396568) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (396568) RS485_LOOPBACK: 📊 统计: 0/97 成功 (0.0%)[0m
[0;32mI (396578) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (399588) RS485_LOOPBACK: === 第98次回环测试 ===[0m
[0;32mI (399588) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (399648) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (399648) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (399658) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[1;31mE (400678) RS485_LOOPBACK: ❌ 回环测试失败 - 未接收到任何数据[0m
[1;31mE (400678) RS485_LOOPBACK:    可能原因:[0m
[1;31mE (400678) RS485_LOOPBACK:    1. A+B-未正确短接[0m
[1;31mE (400678) RS485_LOOPBACK:    2. HVD75扩展板供电异常[0m
[1;31mE (400678) RS485_LOOPBACK:    3. GPIO20/21连接错误[0m
[1;31mE (400688) RS485_LOOPBACK:    4. 扩展板故障[0m
[0;32mI (400688) RS485_LOOPBACK: 📊 统计: 0/98 成功 (0.0%)[0m
[0;32mI (400698) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (403708) RS485_LOOPBACK: === 第99次回环测试 ===[0m
[0;32mI (403708) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (403768) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (403768) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (403778) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (403798) RS485_LOOPBACK: 📥 接收到 13 字节，总计 13/13[0m
[0;32mI (403798) RS485_LOOPBACK: 📥 接收完成: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (403798) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (403798) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (403808) RS485_LOOPBACK: 📊 统计: 1/99 成功 (1.0%)[0m
[0;32mI (403808) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (406818) RS485_LOOPBACK: === 第100次回环测试 ===[0m
[0;32mI (406818) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (406878) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (406878) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (406888) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (406908) RS485_LOOPBACK: 📥 接收到 16 字节，总计 16/16[0m
[0;32mI (406908) RS485_LOOPBACK: 📥 接收完成: "0123456789ABCDEF" (16字节)[0m
[0;32mI (406908) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (406908) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (406918) RS485_LOOPBACK: 📊 统计: 2/100 成功 (2.0%)[0m
[0;32mI (406918) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (409928) RS485_LOOPBACK: === 第101次回环测试 ===[0m
[0;32mI (409928) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (409988) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (409988) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (409988) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (410008) RS485_LOOPBACK: 📥 接收到 5 字节，总计 5/5[0m
[0;32mI (410008) RS485_LOOPBACK: 📥 接收完成: "HELLO" (5字节)[0m
[0;32mI (410008) RS485_LOOPBACK: ⏱️  总耗时: 20 ms[0m
[0;32mI (410008) RS485_LOOPBACK: ⚠️  数据匹配但时间过短(20.0ms)，可能是缓冲区残留[0m
[0;32mI (410018) RS485_LOOPBACK: 📊 统计: 2/101 成功 (2.0%)[0m
[0;32mI (410018) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (413028) RS485_LOOPBACK: === 第102次回环测试 ===[0m
[0;32mI (413028) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (413088) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (413088) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (413098) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (413118) RS485_LOOPBACK: 📥 接收到 10 字节，总计 10/10[0m
[0;32mI (413118) RS485_LOOPBACK: 📥 接收完成: "RS485_TEST" (10字节)[0m
[0;32mI (413118) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (413118) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (413128) RS485_LOOPBACK: 📊 统计: 3/102 成功 (2.9%)[0m
[0;32mI (413128) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (416138) RS485_LOOPBACK: === 第103次回环测试 ===[0m
[0;32mI (416138) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (416198) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (416198) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (416208) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (416228) RS485_LOOPBACK: 📥 接收到 11 字节，总计 11/11[0m
[0;32mI (416228) RS485_LOOPBACK: 📥 接收完成: "LOOPBACK_OK" (11字节)[0m
[0;32mI (416228) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (416228) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (416238) RS485_LOOPBACK: 📊 统计: 4/103 成功 (3.9%)[0m
[0;32mI (416238) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (419248) RS485_LOOPBACK: === 第104次回环测试 ===[0m
[0;32mI (419248) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (419308) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (419308) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (419318) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (419338) RS485_LOOPBACK: 📥 接收到 13 字节，总计 13/13[0m
[0;32mI (419338) RS485_LOOPBACK: 📥 接收完成: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (419338) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (419338) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (419348) RS485_LOOPBACK: 📊 统计: 5/104 成功 (4.8%)[0m
[0;32mI (419348) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (422358) RS485_LOOPBACK: === 第105次回环测试 ===[0m
[0;32mI (422358) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (422418) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (422418) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (422428) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (422448) RS485_LOOPBACK: 📥 接收到 16 字节，总计 16/16[0m
[0;32mI (422448) RS485_LOOPBACK: 📥 接收完成: "0123456789ABCDEF" (16字节)[0m
[0;32mI (422448) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (422448) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (422458) RS485_LOOPBACK: 📊 统计: 6/105 成功 (5.7%)[0m
[0;32mI (422458) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (425468) RS485_LOOPBACK: === 第106次回环测试 ===[0m
[0;32mI (425468) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (425528) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (425528) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (425528) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (425548) RS485_LOOPBACK: 📥 接收到 5 字节，总计 5/5[0m
[0;32mI (425548) RS485_LOOPBACK: 📥 接收完成: "HELLO" (5字节)[0m
[0;32mI (425548) RS485_LOOPBACK: ⏱️  总耗时: 20 ms[0m
[0;32mI (425548) RS485_LOOPBACK: ⚠️  数据匹配但时间过短(20.0ms)，可能是缓冲区残留[0m
[0;32mI (425558) RS485_LOOPBACK: 📊 统计: 6/106 成功 (5.7%)[0m
[0;32mI (425558) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (428568) RS485_LOOPBACK: === 第107次回环测试 ===[0m
[0;32mI (428568) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (428628) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (428628) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (428638) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (428658) RS485_LOOPBACK: 📥 接收到 10 字节，总计 10/10[0m
[0;32mI (428658) RS485_LOOPBACK: 📥 接收完成: "RS485_TEST" (10字节)[0m
[0;32mI (428658) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (428658) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (428668) RS485_LOOPBACK: 📊 统计: 7/107 成功 (6.5%)[0m
[0;32mI (428668) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (431678) RS485_LOOPBACK: === 第108次回环测试 ===[0m
[0;32mI (431678) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (431738) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (431738) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (431748) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (431768) RS485_LOOPBACK: 📥 接收到 11 字节，总计 11/11[0m
[0;32mI (431768) RS485_LOOPBACK: 📥 接收完成: "LOOPBACK_OK" (11字节)[0m
[0;32mI (431768) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (431768) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (431778) RS485_LOOPBACK: 📊 统计: 8/108 成功 (7.4%)[0m
[0;32mI (431778) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (434788) RS485_LOOPBACK: === 第109次回环测试 ===[0m
[0;32mI (434788) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (434848) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (434848) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (434858) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (434878) RS485_LOOPBACK: 📥 接收到 13 字节，总计 13/13[0m
[0;32mI (434878) RS485_LOOPBACK: 📥 接收完成: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (434878) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (434878) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (434888) RS485_LOOPBACK: 📊 统计: 9/109 成功 (8.3%)[0m
[0;32mI (434888) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (437898) RS485_LOOPBACK: === 第110次回环测试 ===[0m
[0;32mI (437898) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (437958) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (437958) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (437968) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (437988) RS485_LOOPBACK: 📥 接收到 16 字节，总计 16/16[0m
[0;32mI (437988) RS485_LOOPBACK: 📥 接收完成: "0123456789ABCDEF" (16字节)[0m
[0;32mI (437988) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (437988) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (437998) RS485_LOOPBACK: 📊 统计: 10/110 成功 (9.1%)[0m
[0;32mI (437998) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (441008) RS485_LOOPBACK: === 第111次回环测试 ===[0m
[0;32mI (441008) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (441068) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (441068) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (441068) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (441088) RS485_LOOPBACK: 📥 接收到 5 字节，总计 5/5[0m
[0;32mI (441088) RS485_LOOPBACK: 📥 接收完成: "HELLO" (5字节)[0m
[0;32mI (441088) RS485_LOOPBACK: ⏱️  总耗时: 20 ms[0m
[0;32mI (441088) RS485_LOOPBACK: ⚠️  数据匹配但时间过短(20.0ms)，可能是缓冲区残留[0m
[0;32mI (441098) RS485_LOOPBACK: 📊 统计: 10/111 成功 (9.0%)[0m
[0;32mI (441098) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (444108) RS485_LOOPBACK: === 第112次回环测试 ===[0m
[0;32mI (444108) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (444168) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (444168) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (444178) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (444198) RS485_LOOPBACK: 📥 接收到 10 字节，总计 10/10[0m
[0;32mI (444198) RS485_LOOPBACK: 📥 接收完成: "RS485_TEST" (10字节)[0m
[0;32mI (444198) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (444198) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (444208) RS485_LOOPBACK: 📊 统计: 11/112 成功 (9.8%)[0m
[0;32mI (444208) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (447218) RS485_LOOPBACK: === 第113次回环测试 ===[0m
[0;32mI (447218) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (447278) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (447278) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (447288) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (447308) RS485_LOOPBACK: 📥 接收到 11 字节，总计 11/11[0m
[0;32mI (447308) RS485_LOOPBACK: 📥 接收完成: "LOOPBACK_OK" (11字节)[0m
[0;32mI (447308) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (447308) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (447318) RS485_LOOPBACK: 📊 统计: 12/113 成功 (10.6%)[0m
[0;32mI (447318) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (450328) RS485_LOOPBACK: === 第114次回环测试 ===[0m
[0;32mI (450328) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (450388) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (450388) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (450398) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (450418) RS485_LOOPBACK: 📥 接收到 13 字节，总计 13/13[0m
[0;32mI (450418) RS485_LOOPBACK: 📥 接收完成: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (450418) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (450418) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (450428) RS485_LOOPBACK: 📊 统计: 13/114 成功 (11.4%)[0m
[0;32mI (450428) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (453438) RS485_LOOPBACK: === 第115次回环测试 ===[0m
[0;32mI (453438) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (453498) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (453498) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (453508) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (453528) RS485_LOOPBACK: 📥 接收到 16 字节，总计 16/16[0m
[0;32mI (453528) RS485_LOOPBACK: 📥 接收完成: "0123456789ABCDEF" (16字节)[0m
[0;32mI (453528) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (453528) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (453538) RS485_LOOPBACK: 📊 统计: 14/115 成功 (12.2%)[0m
[0;32mI (453538) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (456548) RS485_LOOPBACK: === 第116次回环测试 ===[0m
[0;32mI (456548) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (456608) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (456608) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (456608) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (456628) RS485_LOOPBACK: 📥 接收到 5 字节，总计 5/5[0m
[0;32mI (456628) RS485_LOOPBACK: 📥 接收完成: "HELLO" (5字节)[0m
[0;32mI (456628) RS485_LOOPBACK: ⏱️  总耗时: 20 ms[0m
[0;32mI (456628) RS485_LOOPBACK: ⚠️  数据匹配但时间过短(20.0ms)，可能是缓冲区残留[0m
[0;32mI (456638) RS485_LOOPBACK: 📊 统计: 14/116 成功 (12.1%)[0m
[0;32mI (456638) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (3488) RS485_LOOPBACK: === 第2次回环测试 ===[0m
[0;32mI (3488) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (3548) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (3548) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (3558) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (3578) RS485_LOOPBACK: 📥 接收到 10 字节，总计 10/10[0m
[0;32mI (3578) RS485_LOOPBACK: 📥 接收完成: "RS485_TEST" (10字节)[0m
[0;32mI (3578) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (3578) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (3588) RS485_LOOPBACK: 📊 统计: 1/2 成功 (50.0%)[0m
[0;32mI (3588) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (6598) RS485_LOOPBACK: === 第3次回环测试 ===[0m
[0;32mI (6598) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (6658) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (6658) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (6668) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (6688) RS485_LOOPBACK: 📥 接收到 11 字节，总计 11/11[0m
[0;32mI (6688) RS485_LOOPBACK: 📥 接收完成: "LOOPBACK_OK" (11字节)[0m
[0;32mI (6688) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (6688) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (6698) RS485_LOOPBACK: 📊 统计: 2/3 成功 (66.7%)[0m
[0;32mI (6698) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (9708) RS485_LOOPBACK: === 第4次回环测试 ===[0m
[0;32mI (9708) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (9768) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (9768) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (9778) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (9798) RS485_LOOPBACK: 📥 接收到 13 字节，总计 13/13[0m
[0;32mI (9798) RS485_LOOPBACK: 📥 接收完成: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (9798) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (9798) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (9808) RS485_LOOPBACK: 📊 统计: 3/4 成功 (75.0%)[0m
[0;32mI (9808) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (12818) RS485_LOOPBACK: === 第5次回环测试 ===[0m
[0;32mI (12818) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (12878) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (12878) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (12888) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (12908) RS485_LOOPBACK: 📥 接收到 16 字节，总计 16/16[0m
[0;32mI (12908) RS485_LOOPBACK: 📥 接收完成: "0123456789ABCDEF" (16字节)[0m
[0;32mI (12908) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (12908) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (12918) RS485_LOOPBACK: 📊 统计: 4/5 成功 (80.0%)[0m
[0;32mI (12918) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (15928) RS485_LOOPBACK: === 第6次回环测试 ===[0m
[0;32mI (15928) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (15988) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (15988) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (15988) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (16008) RS485_LOOPBACK: 📥 接收到 5 字节，总计 5/5[0m
[0;32mI (16008) RS485_LOOPBACK: 📥 接收完成: "HELLO" (5字节)[0m
[0;32mI (16008) RS485_LOOPBACK: ⏱️  总耗时: 20 ms[0m
[0;32mI (16008) RS485_LOOPBACK: ⚠️  数据匹配但时间过短(20.0ms)，可能是缓冲区残留[0m
[0;32mI (16018) RS485_LOOPBACK: 📊 统计: 4/6 成功 (66.7%)[0m
[0;32mI (16018) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (19028) RS485_LOOPBACK: === 第7次回环测试 ===[0m
[0;32mI (19028) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (19088) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (19088) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (19098) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (19118) RS485_LOOPBACK: 📥 接收到 10 字节，总计 10/10[0m
[0;32mI (19118) RS485_LOOPBACK: 📥 接收完成: "RS485_TEST" (10字节)[0m
[0;32mI (19118) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (19118) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (19128) RS485_LOOPBACK: 📊 统计: 5/7 成功 (71.4%)[0m
[0;32mI (19128) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (22138) RS485_LOOPBACK: === 第8次回环测试 ===[0m
[0;32mI (22138) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (22198) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (22198) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (22208) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (22228) RS485_LOOPBACK: 📥 接收到 11 字节，总计 11/11[0m
[0;32mI (22228) RS485_LOOPBACK: 📥 接收完成: "LOOPBACK_OK" (11字节)[0m
[0;32mI (22228) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (22228) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (22238) RS485_LOOPBACK: 📊 统计: 6/8 成功 (75.0%)[0m
[0;32mI (22238) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (25248) RS485_LOOPBACK: === 第9次回环测试 ===[0m
[0;32mI (25248) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (25308) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (25308) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (25318) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (25338) RS485_LOOPBACK: 📥 接收到 13 字节，总计 13/13[0m
[0;32mI (25338) RS485_LOOPBACK: 📥 接收完成: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (25338) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (25338) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (25348) RS485_LOOPBACK: 📊 统计: 7/9 成功 (77.8%)[0m
[0;32mI (25348) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (28358) RS485_LOOPBACK: === 第10次回环测试 ===[0m
[0;32mI (28358) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (28418) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (28418) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (28428) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (28448) RS485_LOOPBACK: 📥 接收到 16 字节，总计 16/16[0m
[0;32mI (28448) RS485_LOOPBACK: 📥 接收完成: "0123456789ABCDEF" (16字节)[0m
[0;32mI (28448) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (28448) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (28458) RS485_LOOPBACK: 📊 统计: 8/10 成功 (80.0%)[0m
[0;32mI (28458) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (31468) RS485_LOOPBACK: === 第11次回环测试 ===[0m
[0;32mI (31468) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (31528) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (31528) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (31528) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (31548) RS485_LOOPBACK: 📥 接收到 5 字节，总计 5/5[0m
[0;32mI (31548) RS485_LOOPBACK: 📥 接收完成: "HELLO" (5字节)[0m
[0;32mI (31548) RS485_LOOPBACK: ⏱️  总耗时: 20 ms[0m
[0;32mI (31548) RS485_LOOPBACK: ⚠️  数据匹配但时间过短(20.0ms)，可能是缓冲区残留[0m
[0;32mI (31558) RS485_LOOPBACK: 📊 统计: 8/11 成功 (72.7%)[0m
[0;32mI (31558) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (34568) RS485_LOOPBACK: === 第12次回环测试 ===[0m
[0;32mI (34568) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (34628) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (34628) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (34638) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (34658) RS485_LOOPBACK: 📥 接收到 10 字节，总计 10/10[0m
[0;32mI (34658) RS485_LOOPBACK: 📥 接收完成: "RS485_TEST" (10字节)[0m
[0;32mI (34658) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (34658) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (34668) RS485_LOOPBACK: 📊 统计: 9/12 成功 (75.0%)[0m
[0;32mI (34668) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (37678) RS485_LOOPBACK: === 第13次回环测试 ===[0m
[0;32mI (37678) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (37738) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (37738) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (37748) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (37768) RS485_LOOPBACK: 📥 接收到 11 字节，总计 11/11[0m
[0;32mI (37768) RS485_LOOPBACK: 📥 接收完成: "LOOPBACK_OK" (11字节)[0m
[0;32mI (37768) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (37768) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (37778) RS485_LOOPBACK: 📊 统计: 10/13 成功 (76.9%)[0m
[0;32mI (37778) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (40788) RS485_LOOPBACK: === 第14次回环测试 ===[0m
[0;32mI (40788) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (40848) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (40848) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (40858) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (40878) RS485_LOOPBACK: 📥 接收到 13 字节，总计 13/13[0m
[0;32mI (40878) RS485_LOOPBACK: 📥 接收完成: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (40878) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (40878) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (40888) RS485_LOOPBACK: 📊 统计: 11/14 成功 (78.6%)[0m
[0;32mI (40888) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (43898) RS485_LOOPBACK: === 第15次回环测试 ===[0m
[0;32mI (43898) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (43958) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (43958) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (43968) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (43988) RS485_LOOPBACK: 📥 接收到 16 字节，总计 16/16[0m
[0;32mI (43988) RS485_LOOPBACK: 📥 接收完成: "0123456789ABCDEF" (16字节)[0m
[0;32mI (43988) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (43988) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (43998) RS485_LOOPBACK: 📊 统计: 12/15 成功 (80.0%)[0m
[0;32mI (43998) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (47008) RS485_LOOPBACK: === 第16次回环测试 ===[0m
[0;32mI (47008) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (47068) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (47068) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (47068) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (47088) RS485_LOOPBACK: 📥 接收到 5 字节，总计 5/5[0m
[0;32mI (47088) RS485_LOOPBACK: 📥 接收完成: "HELLO" (5字节)[0m
[0;32mI (47088) RS485_LOOPBACK: ⏱️  总耗时: 20 ms[0m
[0;32mI (47088) RS485_LOOPBACK: ⚠️  数据匹配但时间过短(20.0ms)，可能是缓冲区残留[0m
[0;32mI (47098) RS485_LOOPBACK: 📊 统计: 12/16 成功 (75.0%)[0m
[0;32mI (47098) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (50108) RS485_LOOPBACK: === 第17次回环测试 ===[0m
[0;32mI (50108) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (50168) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (50168) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (50178) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (50198) RS485_LOOPBACK: 📥 接收到 10 字节，总计 10/10[0m
[0;32mI (50198) RS485_LOOPBACK: 📥 接收完成: "RS485_TEST" (10字节)[0m
[0;32mI (50198) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (50198) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (50208) RS485_LOOPBACK: 📊 统计: 13/17 成功 (76.5%)[0m
[0;32mI (50208) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (53218) RS485_LOOPBACK: === 第18次回环测试 ===[0m
[0;32mI (53218) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (53278) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (53278) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (53288) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (53308) RS485_LOOPBACK: 📥 接收到 11 字节，总计 11/11[0m
[0;32mI (53308) RS485_LOOPBACK: 📥 接收完成: "LOOPBACK_OK" (11字节)[0m
[0;32mI (53308) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (53308) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (53318) RS485_LOOPBACK: 📊 统计: 14/18 成功 (77.8%)[0m
[0;32mI (53318) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (56328) RS485_LOOPBACK: === 第19次回环测试 ===[0m
[0;32mI (56328) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (56388) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (56388) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (56398) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (56418) RS485_LOOPBACK: 📥 接收到 13 字节，总