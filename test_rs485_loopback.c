/**
 * ESP32-P4 RS485回环测试程序
 * 测试HVD75扩展板的回环功能（A+B-短接）
 */

#include <stdio.h>
#include <string.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "driver/uart.h"
#include "driver/gpio.h"
#include "esp_log.h"
#include "esp_timer.h"

static const char *TAG = "RS485_LOOPBACK";

#define UART_NUM        UART_NUM_2
#define TX_PIN          21
#define RX_PIN          20
#define BAUDRATE        9600
#define BUF_SIZE        256

static void rs485_loopback_test_task(void *arg)
{
    ESP_LOGI(TAG, "🔧 RS485回环测试开始");
    ESP_LOGI(TAG, "   UART: %d, TX: GPIO%d, RX: GPIO%d, 波特率: %d", 
             UART_NUM, TX_PIN, RX_PIN, BAUDRATE);
    ESP_LOGI(TAG, "   前提条件: HVD75扩展板A+和B-已短接");

    // 配置UART
    uart_config_t uart_config = {
        .baud_rate = BAUDRATE,
        .data_bits = UART_DATA_8_BITS,
        .parity = UART_PARITY_DISABLE,
        .stop_bits = UART_STOP_BITS_1,
        .flow_ctrl = UART_HW_FLOWCTRL_DISABLE,
        .source_clk = UART_SCLK_DEFAULT,
    };

    // 安装UART驱动
    ESP_ERROR_CHECK(uart_driver_install(UART_NUM, BUF_SIZE * 2, BUF_SIZE * 2, 0, NULL, 0));
    ESP_ERROR_CHECK(uart_param_config(UART_NUM, &uart_config));
    ESP_ERROR_CHECK(uart_set_pin(UART_NUM, TX_PIN, RX_PIN, UART_PIN_NO_CHANGE, UART_PIN_NO_CHANGE));

    ESP_LOGI(TAG, "✅ UART配置完成");

    // 测试数据
    const char* test_messages[] = {
        "HELLO",
        "RS485_TEST",
        "LOOPBACK_OK",
        "ESP32P4_HVD75",
        "0123456789ABCDEF"
    };
    int num_messages = sizeof(test_messages) / sizeof(test_messages[0]);
    
    uint8_t rx_buffer[BUF_SIZE];
    int test_count = 0;
    int success_count = 0;

    while (1) {
        test_count++;
        const char* test_msg = test_messages[(test_count - 1) % num_messages];
        int msg_len = strlen(test_msg);
        
        ESP_LOGI(TAG, "=== 第%d次回环测试 ===", test_count);
        ESP_LOGI(TAG, "测试消息: \"%s\" (%d字节)", test_msg, msg_len);

        // 彻底清空接收缓冲区
        uart_flush(UART_NUM);
        // 等待一段时间确保缓冲区完全清空
        vTaskDelay(pdMS_TO_TICKS(50));
        // 额外清空：读取所有残留数据
        uint8_t temp_buffer[256];
        int temp_read = 0;
        do {
            temp_read = uart_read_bytes(UART_NUM, temp_buffer, sizeof(temp_buffer), pdMS_TO_TICKS(10));
        } while (temp_read > 0);
        ESP_LOGI(TAG, "🧹 缓冲区已清空");
        
        // 记录发送时间
        uint64_t send_time = esp_timer_get_time();
        
        // 发送数据
        int bytes_written = uart_write_bytes(UART_NUM, test_msg, msg_len);
        ESP_LOGI(TAG, "📤 发送: %d/%d 字节", bytes_written, msg_len);
        
        if (bytes_written != msg_len) {
            ESP_LOGE(TAG, "❌ 发送失败！");
            continue;
        }

        // 等待发送完成
        uart_wait_tx_done(UART_NUM, pdMS_TO_TICKS(100));
        ESP_LOGI(TAG, "✅ 发送完成，开始接收...");

        // 短暂延时让RS485总线稳定
        vTaskDelay(pdMS_TO_TICKS(20));

        // 接收数据
        int total_received = 0;
        uint64_t receive_start = esp_timer_get_time();
        const int timeout_ms = 1000;
        
        while (total_received < msg_len && 
               (esp_timer_get_time() - receive_start) < (timeout_ms * 1000)) {
            
            int bytes_received = uart_read_bytes(UART_NUM, 
                                               rx_buffer + total_received,
                                               msg_len - total_received,
                                               pdMS_TO_TICKS(50));
            
            if (bytes_received > 0) {
                total_received += bytes_received;
                ESP_LOGI(TAG, "📥 接收到 %d 字节，总计 %d/%d", 
                         bytes_received, total_received, msg_len);
            }
        }

        uint64_t total_time = (esp_timer_get_time() - send_time) / 1000; // 转换为毫秒

        if (total_received > 0) {
            rx_buffer[total_received] = '\0';
            ESP_LOGI(TAG, "📥 接收完成: \"%s\" (%d字节)", (char*)rx_buffer, total_received);
            ESP_LOGI(TAG, "⏱️  总耗时: %llu ms", total_time);

            // 严格验证数据
            if (total_received == msg_len && memcmp(test_msg, rx_buffer, msg_len) == 0) {
                // 额外验证：检查接收时间是否合理
                if (total_time > 20) { // 至少需要20ms才可能是真实回环
                    success_count++;
                    ESP_LOGI(TAG, "✅ 回环测试成功！数据完全匹配");
                } else {
                    ESP_LOGI(TAG, "⚠️  数据匹配但时间过短(%.1fms)，可能是缓冲区残留", (float)total_time);
                }
            } else {
                ESP_LOGI(TAG, "⚠️  回环测试部分成功，但数据不匹配");
                ESP_LOGI(TAG, "   期望: \"%s\" (%d字节)", test_msg, msg_len);
                ESP_LOGI(TAG, "   实际: \"%s\" (%d字节)", (char*)rx_buffer, total_received);
            }
        } else {
            ESP_LOGE(TAG, "❌ 回环测试失败 - 未接收到任何数据");
            ESP_LOGE(TAG, "   可能原因:");
            ESP_LOGE(TAG, "   1. A+B-未正确短接");
            ESP_LOGE(TAG, "   2. HVD75扩展板供电异常");
            ESP_LOGE(TAG, "   3. GPIO20/21连接错误");
            ESP_LOGE(TAG, "   4. 扩展板故障");
        }

        // 统计信息
        float success_rate = (float)success_count / test_count * 100.0f;
        ESP_LOGI(TAG, "📊 统计: %d/%d 成功 (%.1f%%)", success_count, test_count, success_rate);

        ESP_LOGI(TAG, "⏳ 等待3秒后进行下一次测试...\n");
        vTaskDelay(pdMS_TO_TICKS(3000));
    }
}

void app_main(void)
{
    ESP_LOGI(TAG, "=== ESP32-P4 RS485回环测试程序 ===");
    ESP_LOGI(TAG, "🎯 测试目标: 验证HVD75扩展板回环功能");
    ESP_LOGI(TAG, "📋 测试条件:");
    ESP_LOGI(TAG, "   - HVD75扩展板A+和B-已短接");
    ESP_LOGI(TAG, "   - ESP32-P4 GPIO21 → 扩展板RX (TX_PIN)");
    ESP_LOGI(TAG, "   - ESP32-P4 GPIO20 ← 扩展板TX (RX_PIN)");
    ESP_LOGI(TAG, "   - 3.3V供电正常");
    ESP_LOGI(TAG, "🚀 开始测试...\n");

    xTaskCreate(rs485_loopback_test_task, "rs485_loopback", 4096, NULL, 10, NULL);
}
