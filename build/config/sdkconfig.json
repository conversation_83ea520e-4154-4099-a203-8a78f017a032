{"ADC_CALI_SUPPRESS_DEPRECATE_WARN": false, "ADC_CONTINUOUS_ISR_IRAM_SAFE": false, "ADC_ENABLE_DEBUG_LOG": false, "ADC_ONESHOT_CTRL_FUNC_IN_IRAM": false, "ADC_SKIP_LEGACY_CONFLICT_CHECK": false, "ADC_SUPPRESS_DEPRECATE_WARN": false, "ANA_CMPR_CTRL_FUNC_IN_IRAM": false, "ANA_CMPR_ENABLE_DEBUG_LOG": false, "ANA_CMPR_ISR_CACHE_SAFE": false, "ANA_CMPR_ISR_HANDLER_IN_IRAM": true, "ANA_CMPR_OBJ_CACHE_SAFE": true, "APPTRACE_DEST_JTAG": false, "APPTRACE_DEST_NONE": true, "APPTRACE_DEST_UART1": false, "APPTRACE_DEST_UART2": false, "APPTRACE_DEST_UART_NONE": true, "APPTRACE_LOCK_ENABLE": true, "APPTRACE_UART_TASK_PRIO": 1, "APP_BUILD_BOOTLOADER": true, "APP_BUILD_GENERATE_BINARIES": true, "APP_BUILD_TYPE_APP_2NDBOOT": true, "APP_BUILD_TYPE_RAM": false, "APP_BUILD_USE_FLASH_SECTIONS": true, "APP_COMPILE_TIME_DATE": true, "APP_EXCLUDE_PROJECT_NAME_VAR": false, "APP_EXCLUDE_PROJECT_VER_VAR": false, "APP_NO_BLOBS": false, "APP_PROJECT_VER_FROM_CONFIG": false, "APP_REPRODUCIBLE_BUILD": false, "APP_RETRIEVE_LEN_ELF_SHA": 9, "BITSCRAMBLER_CTRL_FUNC_IN_IRAM": false, "BOOTLOADER_APP_ROLLBACK_ENABLE": false, "BOOTLOADER_APP_TEST": false, "BOOTLOADER_COMPILER_OPTIMIZATION_DEBUG": false, "BOOTLOADER_COMPILER_OPTIMIZATION_PERF": false, "BOOTLOADER_COMPILER_OPTIMIZATION_SIZE": true, "BOOTLOADER_COMPILE_TIME_DATE": true, "BOOTLOADER_CUSTOM_RESERVE_RTC": false, "BOOTLOADER_FACTORY_RESET": false, "BOOTLOADER_FLASH_DC_AWARE": false, "BOOTLOADER_FLASH_XMC_SUPPORT": true, "BOOTLOADER_LOG_COLORS": false, "BOOTLOADER_LOG_LEVEL": 3, "BOOTLOADER_LOG_LEVEL_DEBUG": false, "BOOTLOADER_LOG_LEVEL_ERROR": false, "BOOTLOADER_LOG_LEVEL_INFO": true, "BOOTLOADER_LOG_LEVEL_NONE": false, "BOOTLOADER_LOG_LEVEL_VERBOSE": false, "BOOTLOADER_LOG_LEVEL_WARN": false, "BOOTLOADER_LOG_MODE_TEXT": true, "BOOTLOADER_LOG_MODE_TEXT_EN": true, "BOOTLOADER_LOG_TIMESTAMP_SOURCE_CPU_TICKS": true, "BOOTLOADER_LOG_VERSION": 1, "BOOTLOADER_LOG_VERSION_1": true, "BOOTLOADER_OFFSET_IN_FLASH": 8192, "BOOTLOADER_PROJECT_VER": 1, "BOOTLOADER_REGION_PROTECTION_ENABLE": true, "BOOTLOADER_RESERVE_RTC_SIZE": 0, "BOOTLOADER_SKIP_VALIDATE_ALWAYS": false, "BOOTLOADER_SKIP_VALIDATE_IN_DEEP_SLEEP": false, "BOOTLOADER_SKIP_VALIDATE_ON_POWER_ON": false, "BOOTLOADER_WDT_DISABLE_IN_USER_CODE": false, "BOOTLOADER_WDT_ENABLE": true, "BOOTLOADER_WDT_TIME_MS": 9000, "BOOT_ROM_LOG_ALWAYS_OFF": false, "BOOT_ROM_LOG_ALWAYS_ON": true, "BOOT_ROM_LOG_ON_GPIO_HIGH": false, "BOOT_ROM_LOG_ON_GPIO_LOW": false, "BT_BLE_LOG_SPI_OUT_ENABLED": false, "BT_ENABLED": false, "CACHE_L1_CACHE_LINE_SIZE": 64, "CACHE_L2_CACHE_128KB": true, "CACHE_L2_CACHE_256KB": false, "CACHE_L2_CACHE_512KB": false, "CACHE_L2_CACHE_LINE_128B": false, "CACHE_L2_CACHE_LINE_64B": true, "CACHE_L2_CACHE_LINE_SIZE": 64, "CACHE_L2_CACHE_SIZE": 131072, "CAM_CTLR_DVP_CAM_ISR_CACHE_SAFE": false, "CAM_CTLR_ISP_DVP_ISR_CACHE_SAFE": false, "CAM_CTLR_MIPI_CSI_ISR_CACHE_SAFE": false, "COMPILER_ASSERT_NDEBUG_EVALUATE": true, "COMPILER_CXX_EXCEPTIONS": false, "COMPILER_CXX_RTTI": false, "COMPILER_DISABLE_DEFAULT_ERRORS": true, "COMPILER_DISABLE_GCC12_WARNINGS": false, "COMPILER_DISABLE_GCC13_WARNINGS": false, "COMPILER_DISABLE_GCC14_WARNINGS": false, "COMPILER_DUMP_RTL_FILES": false, "COMPILER_FLOAT_LIB_FROM_GCCLIB": false, "COMPILER_FLOAT_LIB_FROM_RVFPLIB": true, "COMPILER_HIDE_PATHS_MACROS": true, "COMPILER_NO_MERGE_CONSTANTS": false, "COMPILER_OPTIMIZATION_ASSERTIONS_DISABLE": false, "COMPILER_OPTIMIZATION_ASSERTIONS_ENABLE": true, "COMPILER_OPTIMIZATION_ASSERTIONS_SILENT": false, "COMPILER_OPTIMIZATION_ASSERTION_LEVEL": 2, "COMPILER_OPTIMIZATION_CHECKS_SILENT": false, "COMPILER_OPTIMIZATION_DEBUG": true, "COMPILER_OPTIMIZATION_NONE": false, "COMPILER_OPTIMIZATION_PERF": false, "COMPILER_OPTIMIZATION_SIZE": false, "COMPILER_ORPHAN_SECTIONS_PLACE": false, "COMPILER_ORPHAN_SECTIONS_WARNING": true, "COMPILER_RT_LIB_GCCLIB": true, "COMPILER_RT_LIB_NAME": "gcc", "COMPILER_SAVE_RESTORE_LIBCALLS": false, "COMPILER_STACK_CHECK_MODE_ALL": false, "COMPILER_STACK_CHECK_MODE_NONE": true, "COMPILER_STACK_CHECK_MODE_NORM": false, "COMPILER_STACK_CHECK_MODE_STRONG": false, "COMPILER_STATIC_ANALYZER": false, "COMPILER_WARN_WRITE_STRINGS": false, "CONSOLE_SORTED_HELP": false, "DMA2D_ISR_IRAM_SAFE": false, "DMA2D_OPERATION_FUNC_IN_IRAM": false, "DW_GDMA_ENABLE_DEBUG_LOG": false, "EFUSE_CUSTOM_TABLE": false, "EFUSE_MAX_BLK_LEN": 256, "EFUSE_VIRTUAL": false, "ESP32P4_REV_MAX_FULL": 199, "ESP32P4_REV_MIN_0": false, "ESP32P4_REV_MIN_1": true, "ESP32P4_REV_MIN_100": false, "ESP32P4_REV_MIN_FULL": 1, "ESP32P4_UNIVERSAL_MAC_ADDRESSES": 1, "ESP32P4_UNIVERSAL_MAC_ADDRESSES_ONE": true, "ESPHID_TASK_SIZE_BLE": 4096, "ESPHID_TASK_SIZE_BT": 2048, "ESPTOOLPY_AFTER": "hard_reset", "ESPTOOLPY_AFTER_NORESET": false, "ESPTOOLPY_AFTER_RESET": true, "ESPTOOLPY_BEFORE": "default_reset", "ESPTOOLPY_BEFORE_NORESET": false, "ESPTOOLPY_BEFORE_RESET": true, "ESPTOOLPY_FLASHFREQ": "80m", "ESPTOOLPY_FLASHFREQ_20M": false, "ESPTOOLPY_FLASHFREQ_40M": false, "ESPTOOLPY_FLASHFREQ_80M": true, "ESPTOOLPY_FLASHFREQ_VAL": 80, "ESPTOOLPY_FLASHMODE": "dio", "ESPTOOLPY_FLASHMODE_DIO": true, "ESPTOOLPY_FLASHMODE_DOUT": false, "ESPTOOLPY_FLASHMODE_QIO": false, "ESPTOOLPY_FLASHMODE_QOUT": false, "ESPTOOLPY_FLASHSIZE": "16MB", "ESPTOOLPY_FLASHSIZE_128MB": false, "ESPTOOLPY_FLASHSIZE_16MB": true, "ESPTOOLPY_FLASHSIZE_1MB": false, "ESPTOOLPY_FLASHSIZE_2MB": false, "ESPTOOLPY_FLASHSIZE_32MB": false, "ESPTOOLPY_FLASHSIZE_4MB": false, "ESPTOOLPY_FLASHSIZE_64MB": false, "ESPTOOLPY_FLASHSIZE_8MB": false, "ESPTOOLPY_FLASH_SAMPLE_MODE_STR": true, "ESPTOOLPY_HEADER_FLASHSIZE_UPDATE": false, "ESPTOOLPY_MONITOR_BAUD": 115200, "ESPTOOLPY_NO_STUB": false, "ESP_BROWNOUT_DET": true, "ESP_BROWNOUT_DET_LVL": 7, "ESP_BROWNOUT_DET_LVL_SEL_5": false, "ESP_BROWNOUT_DET_LVL_SEL_6": false, "ESP_BROWNOUT_DET_LVL_SEL_7": true, "ESP_BROWNOUT_USE_INTR": true, "ESP_COEX_GPIO_DEBUG": false, "ESP_CONSOLE_NONE": false, "ESP_CONSOLE_ROM_SERIAL_PORT_NUM": 0, "ESP_CONSOLE_SECONDARY_NONE": false, "ESP_CONSOLE_SECONDARY_USB_SERIAL_JTAG": true, "ESP_CONSOLE_UART": true, "ESP_CONSOLE_UART_BAUDRATE": 115200, "ESP_CONSOLE_UART_CUSTOM": false, "ESP_CONSOLE_UART_DEFAULT": true, "ESP_CONSOLE_UART_NUM": 0, "ESP_CONSOLE_USB_SERIAL_JTAG": false, "ESP_CONSOLE_USB_SERIAL_JTAG_ENABLED": true, "ESP_COREDUMP_ENABLE_TO_FLASH": false, "ESP_COREDUMP_ENABLE_TO_NONE": true, "ESP_COREDUMP_ENABLE_TO_UART": false, "ESP_DEBUG_OCDAWARE": true, "ESP_DEBUG_STUBS_ENABLE": false, "ESP_DEFAULT_CPU_FREQ_MHZ": 360, "ESP_DEFAULT_CPU_FREQ_MHZ_360": true, "ESP_EFUSE_BLOCK_REV_MAX_FULL": 99, "ESP_EFUSE_BLOCK_REV_MIN_FULL": 0, "ESP_ERR_TO_NAME_LOOKUP": true, "ESP_EVENT_LOOP_PROFILING": false, "ESP_EVENT_POST_FROM_IRAM_ISR": true, "ESP_EVENT_POST_FROM_ISR": true, "ESP_GDBSTUB_ENABLED": true, "ESP_GDBSTUB_MAX_TASKS": 32, "ESP_GDBSTUB_SUPPORT_TASKS": true, "ESP_HOST_WIFI_ENABLED": false, "ESP_HTTPS_OTA_ALLOW_HTTP": false, "ESP_HTTPS_OTA_DECRYPT_CB": false, "ESP_HTTPS_OTA_EVENT_POST_TIMEOUT": 2000, "ESP_HTTPS_SERVER_CERT_SELECT_HOOK": false, "ESP_HTTPS_SERVER_ENABLE": false, "ESP_HTTPS_SERVER_EVENT_POST_TIMEOUT": 2000, "ESP_HTTP_CLIENT_ENABLE_BASIC_AUTH": false, "ESP_HTTP_CLIENT_ENABLE_CUSTOM_TRANSPORT": false, "ESP_HTTP_CLIENT_ENABLE_DIGEST_AUTH": false, "ESP_HTTP_CLIENT_ENABLE_HTTPS": true, "ESP_HTTP_CLIENT_EVENT_POST_TIMEOUT": 2000, "ESP_INTR_IN_IRAM": true, "ESP_INT_WDT": true, "ESP_INT_WDT_CHECK_CPU1": true, "ESP_INT_WDT_TIMEOUT_MS": 300, "ESP_IPC_ISR_ENABLE": true, "ESP_IPC_TASK_STACK_SIZE": 1024, "ESP_IPC_USES_CALLERS_PRIORITY": true, "ESP_LDO_CHAN_PSRAM_DOMAIN": 2, "ESP_LDO_CHAN_SPI_NOR_FLASH_DOMAIN": 1, "ESP_LDO_RESERVE_PSRAM": true, "ESP_LDO_RESERVE_SPI_NOR_FLASH": true, "ESP_LDO_VOLTAGE_PSRAM_1900_MV": true, "ESP_LDO_VOLTAGE_PSRAM_DOMAIN": 1900, "ESP_LDO_VOLTAGE_SPI_NOR_FLASH_3300_MV": true, "ESP_LDO_VOLTAGE_SPI_NOR_FLASH_DOMAIN": 3300, "ESP_MAC_ADDR_UNIVERSE_ETH": true, "ESP_MAC_UNIVERSAL_MAC_ADDRESSES": 1, "ESP_MAC_UNIVERSAL_MAC_ADDRESSES_ONE": true, "ESP_MAC_USE_CUSTOM_MAC_AS_BASE_MAC": false, "ESP_MAIN_TASK_AFFINITY": 0, "ESP_MAIN_TASK_AFFINITY_CPU0": true, "ESP_MAIN_TASK_AFFINITY_CPU1": false, "ESP_MAIN_TASK_AFFINITY_NO_AFFINITY": false, "ESP_MAIN_TASK_STACK_SIZE": 3584, "ESP_MINIMAL_SHARED_STACK_SIZE": 2048, "ESP_MM_CACHE_MSYNC_C2M_CHUNKED_OPS": false, "ESP_NETIF_BRIDGE_EN": false, "ESP_NETIF_IP_LOST_TIMER_INTERVAL": 120, "ESP_NETIF_L2_TAP": false, "ESP_NETIF_LOOPBACK": false, "ESP_NETIF_PROVIDE_CUSTOM_IMPLEMENTATION": false, "ESP_NETIF_RECEIVE_REPORT_ERRORS": false, "ESP_NETIF_REPORT_DATA_TRAFFIC": true, "ESP_NETIF_SET_DNS_PER_DEFAULT_NETIF": false, "ESP_NETIF_TCPIP_LWIP": true, "ESP_NETIF_USES_TCPIP_WITH_BSD_API": true, "ESP_PANIC_HANDLER_IRAM": false, "ESP_PERIPH_CTRL_FUNC_IN_IRAM": true, "ESP_PROTOCOMM_SUPPORT_SECURITY_PATCH_VERSION": true, "ESP_PROTOCOMM_SUPPORT_SECURITY_VERSION_0": true, "ESP_PROTOCOMM_SUPPORT_SECURITY_VERSION_1": true, "ESP_PROTOCOMM_SUPPORT_SECURITY_VERSION_2": true, "ESP_REGI2C_CTRL_FUNC_IN_IRAM": true, "ESP_REV_MAX_FULL": 199, "ESP_REV_MIN_FULL": 1, "ESP_ROM_CLIC_INT_TYPE_PATCH": true, "ESP_ROM_GET_CLK_FREQ": true, "ESP_ROM_HAS_CRC_BE": true, "ESP_ROM_HAS_CRC_LE": true, "ESP_ROM_HAS_HAL_SYSTIMER": true, "ESP_ROM_HAS_HAL_WDT": true, "ESP_ROM_HAS_LAYOUT_TABLE": true, "ESP_ROM_HAS_LP_ROM": true, "ESP_ROM_HAS_NEWLIB": true, "ESP_ROM_HAS_NEWLIB_NANO_FORMAT": true, "ESP_ROM_HAS_NEWLIB_NANO_PRINTF_FLOAT_BUG": true, "ESP_ROM_HAS_OUTPUT_PUTC_FUNC": true, "ESP_ROM_HAS_RETARGETABLE_LOCKING": true, "ESP_ROM_HAS_RVFPLIB": true, "ESP_ROM_HAS_SUBOPTIMAL_NEWLIB_ON_MISALIGNED_MEMORY": true, "ESP_ROM_HAS_VERSION": true, "ESP_ROM_PRINT_IN_IRAM": true, "ESP_ROM_UART_CLK_IS_XTAL": true, "ESP_ROM_USB_OTG_NUM": 5, "ESP_ROM_USB_SERIAL_DEVICE_NUM": 6, "ESP_ROM_WDT_INIT_PATCH": true, "ESP_ROM_WITHOUT_REGI2C": true, "ESP_SLEEP_CACHE_SAFE_ASSERTION": false, "ESP_SLEEP_DCM_VSET_VAL_IN_SLEEP": 14, "ESP_SLEEP_DEBUG": false, "ESP_SLEEP_FLASH_LEAKAGE_WORKAROUND": true, "ESP_SLEEP_GPIO_ENABLE_INTERNAL_RESISTORS": true, "ESP_SLEEP_GPIO_RESET_WORKAROUND": false, "ESP_SLEEP_KEEP_DCDC_ALWAYS_ON": true, "ESP_SLEEP_MSPI_NEED_ALL_IO_PU": false, "ESP_SLEEP_WAIT_FLASH_READY_EXTRA_DELAY": 0, "ESP_SPI_BUS_LOCK_ISR_FUNCS_IN_IRAM": true, "ESP_SYSTEM_ALLOW_RTC_FAST_MEM_AS_HEAP": true, "ESP_SYSTEM_CHECK_INT_LEVEL_4": true, "ESP_SYSTEM_EVENT_QUEUE_SIZE": 32, "ESP_SYSTEM_EVENT_TASK_STACK_SIZE": 2304, "ESP_SYSTEM_GDBSTUB_RUNTIME": false, "ESP_SYSTEM_HW_PC_RECORD": true, "ESP_SYSTEM_HW_STACK_GUARD": true, "ESP_SYSTEM_NO_BACKTRACE": true, "ESP_SYSTEM_PANIC_GDBSTUB": false, "ESP_SYSTEM_PANIC_PRINT_HALT": false, "ESP_SYSTEM_PANIC_PRINT_REBOOT": true, "ESP_SYSTEM_PANIC_REBOOT_DELAY_SECONDS": 0, "ESP_SYSTEM_PANIC_SILENT_REBOOT": false, "ESP_SYSTEM_PMP_IDRAM_SPLIT": true, "ESP_SYSTEM_PMP_LP_CORE_RESERVE_MEM_EXECUTABLE": false, "ESP_SYSTEM_RTC_FAST_MEM_AS_HEAP_DEPCHECK": true, "ESP_SYSTEM_USE_EH_FRAME": false, "ESP_SYSTEM_USE_FRAME_POINTER": false, "ESP_TASK_WDT_CHECK_IDLE_TASK_CPU0": true, "ESP_TASK_WDT_CHECK_IDLE_TASK_CPU1": true, "ESP_TASK_WDT_EN": true, "ESP_TASK_WDT_INIT": true, "ESP_TASK_WDT_PANIC": false, "ESP_TASK_WDT_TIMEOUT_S": 5, "ESP_TIMER_IMPL_SYSTIMER": true, "ESP_TIMER_INTERRUPT_LEVEL": 1, "ESP_TIMER_IN_IRAM": true, "ESP_TIMER_ISR_AFFINITY_CPU0": true, "ESP_TIMER_PROFILING": false, "ESP_TIMER_SHOW_EXPERIMENTAL": false, "ESP_TIMER_SUPPORTS_ISR_DISPATCH_METHOD": false, "ESP_TIMER_TASK_AFFINITY": 0, "ESP_TIMER_TASK_AFFINITY_CPU0": true, "ESP_TIMER_TASK_STACK_SIZE": 3584, "ESP_TIME_FUNCS_USE_ESP_TIMER": true, "ESP_TIME_FUNCS_USE_RTC_TIMER": true, "ESP_TLS_CLIENT_SESSION_TICKETS": false, "ESP_TLS_INSECURE": false, "ESP_TLS_PSK_VERIFICATION": false, "ESP_TLS_SERVER_CERT_SELECT_HOOK": false, "ESP_TLS_SERVER_MIN_AUTH_MODE_OPTIONAL": false, "ESP_TLS_SERVER_SESSION_TICKETS": false, "ESP_TLS_USE_DS_PERIPHERAL": true, "ESP_TLS_USE_SECURE_ELEMENT": false, "ESP_TLS_USING_MBEDTLS": true, "ESP_VBAT_INIT_AUTO": false, "ESP_VBAT_WAKEUP_CHIP_ON_VBAT_BROWNOUT": false, "ETH_DMA_BUFFER_SIZE": 512, "ETH_DMA_RX_BUFFER_NUM": 20, "ETH_DMA_TX_BUFFER_NUM": 10, "ETH_ENABLED": true, "ETH_IRAM_OPTIMIZATION": false, "ETH_PHY_INTERFACE_RMII": true, "ETH_SOFT_FLOW_CONTROL": false, "ETH_SPI_ETHERNET_DM9051": false, "ETH_SPI_ETHERNET_KSZ8851SNL": false, "ETH_SPI_ETHERNET_W5500": false, "ETH_TRANSMIT_MUTEX": false, "ETH_USE_ESP32_EMAC": true, "ETH_USE_OPENETH": false, "ETH_USE_SPI_ETHERNET": true, "ETM_ENABLE_DEBUG_LOG": false, "FATFS_CODEPAGE": 437, "FATFS_CODEPAGE_437": true, "FATFS_CODEPAGE_720": false, "FATFS_CODEPAGE_737": false, "FATFS_CODEPAGE_771": false, "FATFS_CODEPAGE_775": false, "FATFS_CODEPAGE_850": false, "FATFS_CODEPAGE_852": false, "FATFS_CODEPAGE_855": false, "FATFS_CODEPAGE_857": false, "FATFS_CODEPAGE_860": false, "FATFS_CODEPAGE_861": false, "FATFS_CODEPAGE_862": false, "FATFS_CODEPAGE_863": false, "FATFS_CODEPAGE_864": false, "FATFS_CODEPAGE_865": false, "FATFS_CODEPAGE_866": false, "FATFS_CODEPAGE_869": false, "FATFS_CODEPAGE_932": false, "FATFS_CODEPAGE_936": false, "FATFS_CODEPAGE_949": false, "FATFS_CODEPAGE_950": false, "FATFS_CODEPAGE_DYNAMIC": false, "FATFS_DONT_TRUST_FREE_CLUSTER_CNT": 0, "FATFS_DONT_TRUST_LAST_ALLOC": 0, "FATFS_FS_LOCK": 0, "FATFS_IMMEDIATE_FSYNC": false, "FATFS_LFN_HEAP": false, "FATFS_LFN_NONE": true, "FATFS_LFN_STACK": false, "FATFS_LINK_LOCK": true, "FATFS_PER_FILE_CACHE": true, "FATFS_SECTOR_4096": true, "FATFS_SECTOR_512": false, "FATFS_TIMEOUT_MS": 10000, "FATFS_USE_DYN_BUFFERS": false, "FATFS_USE_FASTSEEK": false, "FATFS_USE_LABEL": false, "FATFS_USE_STRFUNC_NONE": true, "FATFS_USE_STRFUNC_WITHOUT_CRLF_CONV": false, "FATFS_USE_STRFUNC_WITH_CRLF_CONV": false, "FATFS_VFS_FSTAT_BLKSIZE": 0, "FATFS_VOLUME_COUNT": 2, "FREERTOS_CHECK_MUTEX_GIVEN_BY_OWNER": true, "FREERTOS_CHECK_PORT_CRITICAL_COMPLIANCE": false, "FREERTOS_CHECK_STACKOVERFLOW_CANARY": true, "FREERTOS_CHECK_STACKOVERFLOW_NONE": false, "FREERTOS_CHECK_STACKOVERFLOW_PTRVAL": false, "FREERTOS_CORETIMER_SYSTIMER_LVL1": true, "FREERTOS_CORETIMER_SYSTIMER_LVL3": false, "FREERTOS_DEBUG_OCDAWARE": true, "FREERTOS_ENABLE_BACKWARD_COMPATIBILITY": false, "FREERTOS_ENABLE_STATIC_TASK_CLEAN_UP": false, "FREERTOS_ENABLE_TASK_SNAPSHOT": true, "FREERTOS_GENERATE_RUN_TIME_STATS": false, "FREERTOS_HZ": 100, "FREERTOS_IDLE_TASK_STACKSIZE": 1536, "FREERTOS_INTERRUPT_BACKTRACE": true, "FREERTOS_IN_IRAM": true, "FREERTOS_ISR_STACKSIZE": 1536, "FREERTOS_MAX_TASK_NAME_LEN": 16, "FREERTOS_NO_AFFINITY": 2147483647, "FREERTOS_NUMBER_OF_CORES": 2, "FREERTOS_PLACE_FUNCTIONS_INTO_FLASH": false, "FREERTOS_PLACE_SNAPSHOT_FUNS_INTO_FLASH": true, "FREERTOS_PORT": true, "FREERTOS_QUEUE_REGISTRY_SIZE": 0, "FREERTOS_SUPPORT_STATIC_ALLOCATION": true, "FREERTOS_SYSTICK_USES_SYSTIMER": true, "FREERTOS_TASK_FUNCTION_WRAPPER": true, "FREERTOS_TASK_NOTIFICATION_ARRAY_ENTRIES": 1, "FREERTOS_TASK_PRE_DELETION_HOOK": false, "FREERTOS_THREAD_LOCAL_STORAGE_POINTERS": 1, "FREERTOS_TICK_SUPPORT_SYSTIMER": true, "FREERTOS_TIMER_QUEUE_LENGTH": 10, "FREERTOS_TIMER_SERVICE_TASK_CORE_AFFINITY": 2147483647, "FREERTOS_TIMER_SERVICE_TASK_NAME": "Tmr Svc", "FREERTOS_TIMER_TASK_AFFINITY_CPU0": false, "FREERTOS_TIMER_TASK_AFFINITY_CPU1": false, "FREERTOS_TIMER_TASK_NO_AFFINITY": true, "FREERTOS_TIMER_TASK_PRIORITY": 1, "FREERTOS_TIMER_TASK_STACK_DEPTH": 2048, "FREERTOS_TLSP_DELETION_CALLBACKS": true, "FREERTOS_UNICORE": false, "FREERTOS_USE_APPLICATION_TASK_TAG": false, "FREERTOS_USE_IDLE_HOOK": false, "FREERTOS_USE_LIST_DATA_INTEGRITY_CHECK_BYTES": false, "FREERTOS_USE_TICK_HOOK": false, "FREERTOS_USE_TIMERS": true, "FREERTOS_USE_TRACE_FACILITY": false, "FREERTOS_WATCHPOINT_END_OF_STACK": false, "GDMA_CTRL_FUNC_IN_IRAM": true, "GDMA_ENABLE_DEBUG_LOG": false, "GDMA_ISR_HANDLER_IN_IRAM": true, "GDMA_ISR_IRAM_SAFE": false, "GDMA_OBJ_DRAM_SAFE": true, "GPIO_CTRL_FUNC_IN_IRAM": false, "GPTIMER_CTRL_FUNC_IN_IRAM": false, "GPTIMER_ENABLE_DEBUG_LOG": false, "GPTIMER_ISR_CACHE_SAFE": false, "GPTIMER_ISR_HANDLER_IN_IRAM": true, "GPTIMER_OBJ_CACHE_SAFE": true, "GPTIMER_SKIP_LEGACY_CONFLICT_CHECK": false, "GPTIMER_SUPPRESS_DEPRECATE_WARN": false, "HAL_ASSERTION_DISABLE": false, "HAL_ASSERTION_ENABLE": false, "HAL_ASSERTION_EQUALS_SYSTEM": true, "HAL_ASSERTION_SILENT": false, "HAL_DEFAULT_ASSERTION_LEVEL": 2, "HAL_SYSTIMER_USE_ROM_IMPL": true, "HAL_WDT_USE_ROM_IMPL": true, "HEAP_ABORT_WHEN_ALLOCATION_FAILS": false, "HEAP_PLACE_FUNCTION_INTO_FLASH": false, "HEAP_POISONING_COMPREHENSIVE": false, "HEAP_POISONING_DISABLED": true, "HEAP_POISONING_LIGHT": false, "HEAP_TASK_TRACKING": false, "HEAP_TRACING_OFF": true, "HEAP_TRACING_STANDALONE": false, "HEAP_TRACING_TOHOST": false, "HEAP_USE_HOOKS": false, "HTTPD_ERR_RESP_NO_DELAY": true, "HTTPD_LOG_PURGE_DATA": false, "HTTPD_MAX_REQ_HDR_LEN": 1024, "HTTPD_MAX_URI_LEN": 512, "HTTPD_PURGE_BUF_LEN": 32, "HTTPD_QUEUE_WORK_BLOCKING": false, "HTTPD_SERVER_EVENT_POST_TIMEOUT": 2000, "HTTPD_WS_SUPPORT": false, "I2C_ENABLE_DEBUG_LOG": false, "I2C_ENABLE_SLAVE_DRIVER_VERSION_2": false, "I2C_ISR_IRAM_SAFE": false, "I2C_MASTER_ISR_HANDLER_IN_IRAM": true, "I2C_SKIP_LEGACY_CONFLICT_CHECK": false, "I2S_ENABLE_DEBUG_LOG": false, "I2S_ISR_IRAM_SAFE": false, "I2S_SKIP_LEGACY_CONFLICT_CHECK": false, "I2S_SUPPRESS_DEPRECATE_WARN": false, "IDF_CMAKE": true, "IDF_EXPERIMENTAL_FEATURES": false, "IDF_FIRMWARE_CHIP_ID": 18, "IDF_INIT_VERSION": "5.5.0", "IDF_TARGET": "esp32p4", "IDF_TARGET_ARCH": "riscv", "IDF_TARGET_ARCH_RISCV": true, "IDF_TARGET_ESP32P4": true, "IDF_TOOLCHAIN": "gcc", "IDF_TOOLCHAIN_GCC": true, "ISP_CTRL_FUNC_IN_IRAM": false, "ISP_ISR_IRAM_SAFE": false, "JPEG_ENABLE_DEBUG_LOG": false, "LCD_DSI_ISR_IRAM_SAFE": false, "LCD_ENABLE_DEBUG_LOG": false, "LCD_RGB_ISR_IRAM_SAFE": false, "LCD_RGB_RESTART_IN_VSYNC": false, "LEDC_CTRL_FUNC_IN_IRAM": false, "LIBC_LOCKS_PLACE_IN_IRAM": true, "LIBC_MISC_IN_IRAM": true, "LIBC_NEWLIB": true, "LIBC_NEWLIB_NANO_FORMAT": false, "LIBC_OPTIMIZED_MISALIGNED_ACCESS": false, "LIBC_STDIN_LINE_ENDING_CR": true, "LIBC_STDIN_LINE_ENDING_CRLF": false, "LIBC_STDIN_LINE_ENDING_LF": false, "LIBC_STDOUT_LINE_ENDING_CR": false, "LIBC_STDOUT_LINE_ENDING_CRLF": true, "LIBC_STDOUT_LINE_ENDING_LF": false, "LIBC_TIME_SYSCALL_USE_HRT": false, "LIBC_TIME_SYSCALL_USE_NONE": false, "LIBC_TIME_SYSCALL_USE_RTC": false, "LIBC_TIME_SYSCALL_USE_RTC_HRT": true, "LOG_COLORS": false, "LOG_DEFAULT_LEVEL": 3, "LOG_DEFAULT_LEVEL_DEBUG": false, "LOG_DEFAULT_LEVEL_ERROR": false, "LOG_DEFAULT_LEVEL_INFO": true, "LOG_DEFAULT_LEVEL_NONE": false, "LOG_DEFAULT_LEVEL_VERBOSE": false, "LOG_DEFAULT_LEVEL_WARN": false, "LOG_DYNAMIC_LEVEL_CONTROL": true, "LOG_IN_IRAM": true, "LOG_MASTER_LEVEL": false, "LOG_MAXIMUM_EQUALS_DEFAULT": true, "LOG_MAXIMUM_LEVEL": 3, "LOG_MAXIMUM_LEVEL_DEBUG": false, "LOG_MAXIMUM_LEVEL_VERBOSE": false, "LOG_MODE_TEXT": true, "LOG_MODE_TEXT_EN": true, "LOG_TAG_LEVEL_CACHE_ARRAY": false, "LOG_TAG_LEVEL_CACHE_BINARY_MIN_HEAP": true, "LOG_TAG_LEVEL_IMPL_CACHE_AND_LINKED_LIST": true, "LOG_TAG_LEVEL_IMPL_CACHE_SIZE": 31, "LOG_TAG_LEVEL_IMPL_LINKED_LIST": false, "LOG_TAG_LEVEL_IMPL_NONE": false, "LOG_TIMESTAMP_SOURCE_RTOS": true, "LOG_TIMESTAMP_SOURCE_SYSTEM": false, "LOG_VERSION": 1, "LOG_VERSION_1": true, "LOG_VERSION_2": false, "LWIP_AUTOIP": false, "LWIP_BRIDGEIF_MAX_PORTS": 7, "LWIP_BROADCAST_PING": false, "LWIP_CHECKSUM_CHECK_ICMP": true, "LWIP_CHECKSUM_CHECK_IP": false, "LWIP_CHECKSUM_CHECK_UDP": false, "LWIP_CHECK_THREAD_SAFETY": false, "LWIP_DEBUG": false, "LWIP_DHCPS": true, "LWIP_DHCPS_ADD_DNS": true, "LWIP_DHCPS_LEASE_UNIT": 60, "LWIP_DHCPS_MAX_STATION_NUM": 8, "LWIP_DHCPS_STATIC_ENTRIES": true, "LWIP_DHCP_COARSE_TIMER_SECS": 1, "LWIP_DHCP_DISABLE_CLIENT_ID": false, "LWIP_DHCP_DISABLE_VENDOR_CLASS_ID": true, "LWIP_DHCP_DOES_ACD_CHECK": false, "LWIP_DHCP_DOES_ARP_CHECK": true, "LWIP_DHCP_DOES_NOT_CHECK_OFFERED_IP": false, "LWIP_DHCP_GET_NTP_SRV": false, "LWIP_DHCP_OPTIONS_LEN": 68, "LWIP_DHCP_RESTORE_LAST_IP": false, "LWIP_DNS_MAX_HOST_IP": 1, "LWIP_DNS_MAX_SERVERS": 3, "LWIP_DNS_SETSERVER_WITH_NETIF": false, "LWIP_DNS_SUPPORT_MDNS_QUERIES": true, "LWIP_ENABLE": true, "LWIP_ESP_GRATUITOUS_ARP": true, "LWIP_ESP_LWIP_ASSERT": true, "LWIP_ESP_MLDV6_REPORT": true, "LWIP_EXTRA_IRAM_OPTIMIZATION": false, "LWIP_FALLBACK_DNS_SERVER_SUPPORT": false, "LWIP_FORCE_ROUTER_FORWARDING": false, "LWIP_GARP_TMR_INTERVAL": 60, "LWIP_HOOK_DHCP_EXTRA_OPTION_CUSTOM": false, "LWIP_HOOK_DHCP_EXTRA_OPTION_DEFAULT": false, "LWIP_HOOK_DHCP_EXTRA_OPTION_NONE": true, "LWIP_HOOK_DNS_EXT_RESOLVE_CUSTOM": false, "LWIP_HOOK_DNS_EXT_RESOLVE_NONE": true, "LWIP_HOOK_IP6_INPUT_CUSTOM": false, "LWIP_HOOK_IP6_INPUT_DEFAULT": true, "LWIP_HOOK_IP6_INPUT_NONE": false, "LWIP_HOOK_IP6_ROUTE_CUSTOM": false, "LWIP_HOOK_IP6_ROUTE_DEFAULT": false, "LWIP_HOOK_IP6_ROUTE_NONE": true, "LWIP_HOOK_IP6_SELECT_SRC_ADDR_CUSTOM": false, "LWIP_HOOK_IP6_SELECT_SRC_ADDR_DEFAULT": false, "LWIP_HOOK_IP6_SELECT_SRC_ADDR_NONE": true, "LWIP_HOOK_ND6_GET_GW_CUSTOM": false, "LWIP_HOOK_ND6_GET_GW_DEFAULT": false, "LWIP_HOOK_ND6_GET_GW_NONE": true, "LWIP_HOOK_NETCONN_EXT_RESOLVE_CUSTOM": false, "LWIP_HOOK_NETCONN_EXT_RESOLVE_DEFAULT": false, "LWIP_HOOK_NETCONN_EXT_RESOLVE_NONE": true, "LWIP_HOOK_TCP_ISN_CUSTOM": false, "LWIP_HOOK_TCP_ISN_DEFAULT": true, "LWIP_HOOK_TCP_ISN_NONE": false, "LWIP_ICMP": true, "LWIP_IP4_FRAG": true, "LWIP_IP4_REASSEMBLY": false, "LWIP_IP6_FRAG": true, "LWIP_IP6_REASSEMBLY": false, "LWIP_IPV4": true, "LWIP_IPV6": true, "LWIP_IPV6_AUTOCONFIG": false, "LWIP_IPV6_FORWARD": false, "LWIP_IPV6_MEMP_NUM_ND6_QUEUE": 3, "LWIP_IPV6_ND6_NUM_DESTINATIONS": 10, "LWIP_IPV6_ND6_NUM_NEIGHBORS": 5, "LWIP_IPV6_ND6_NUM_PREFIXES": 5, "LWIP_IPV6_ND6_NUM_ROUTERS": 3, "LWIP_IPV6_NUM_ADDRESSES": 3, "LWIP_IP_DEFAULT_TTL": 64, "LWIP_IP_FORWARD": false, "LWIP_IP_REASS_MAX_PBUFS": 10, "LWIP_IRAM_OPTIMIZATION": false, "LWIP_L2_TO_L3_COPY": false, "LWIP_LOCAL_HOSTNAME": "espressif", "LWIP_LOOPBACK_MAX_PBUFS": 8, "LWIP_MAX_ACTIVE_TCP": 16, "LWIP_MAX_LISTENING_TCP": 16, "LWIP_MAX_RAW_PCBS": 16, "LWIP_MAX_SOCKETS": 10, "LWIP_MAX_UDP_PCBS": 16, "LWIP_MLDV6_TMR_INTERVAL": 40, "LWIP_MULTICAST_PING": false, "LWIP_ND6": true, "LWIP_NETBUF_RECVINFO": false, "LWIP_NETIF_LOOPBACK": true, "LWIP_NETIF_STATUS_CALLBACK": false, "LWIP_NUM_NETIF_CLIENT_DATA": 0, "LWIP_PPP_SUPPORT": false, "LWIP_SLIP_SUPPORT": false, "LWIP_SNTP_MAXIMUM_STARTUP_DELAY": 5000, "LWIP_SNTP_MAX_SERVERS": 1, "LWIP_SNTP_STARTUP_DELAY": true, "LWIP_SNTP_UPDATE_DELAY": 3600000, "LWIP_SO_LINGER": false, "LWIP_SO_RCVBUF": false, "LWIP_SO_REUSE": true, "LWIP_SO_REUSE_RXTOALL": true, "LWIP_STATS": false, "LWIP_TCPIP_CORE_LOCKING": false, "LWIP_TCPIP_RECVMBOX_SIZE": 32, "LWIP_TCPIP_TASK_AFFINITY": 2147483647, "LWIP_TCPIP_TASK_AFFINITY_CPU0": false, "LWIP_TCPIP_TASK_AFFINITY_CPU1": false, "LWIP_TCPIP_TASK_AFFINITY_NO_AFFINITY": true, "LWIP_TCPIP_TASK_PRIO": 18, "LWIP_TCPIP_TASK_STACK_SIZE": 3072, "LWIP_TCP_ACCEPTMBOX_SIZE": 6, "LWIP_TCP_FIN_WAIT_TIMEOUT": 20000, "LWIP_TCP_HIGH_SPEED_RETRANSMISSION": true, "LWIP_TCP_MAXRTX": 12, "LWIP_TCP_MSL": 60000, "LWIP_TCP_MSS": 1440, "LWIP_TCP_OOSEQ_MAX_PBUFS": 4, "LWIP_TCP_OOSEQ_TIMEOUT": 6, "LWIP_TCP_OVERSIZE_DISABLE": false, "LWIP_TCP_OVERSIZE_MSS": true, "LWIP_TCP_OVERSIZE_QUARTER_MSS": false, "LWIP_TCP_QUEUE_OOSEQ": true, "LWIP_TCP_RECVMBOX_SIZE": 6, "LWIP_TCP_RTO_TIME": 1500, "LWIP_TCP_SACK_OUT": false, "LWIP_TCP_SND_BUF_DEFAULT": 5760, "LWIP_TCP_SYNMAXRTX": 12, "LWIP_TCP_TMR_INTERVAL": 250, "LWIP_TCP_WND_DEFAULT": 5760, "LWIP_TIMERS_ONDEMAND": true, "LWIP_UDP_RECVMBOX_SIZE": 6, "LWIP_USE_ESP_GETADDRINFO": false, "LWIP_USE_ONLY_LWIP_SELECT": false, "MBEDTLS_AES_C": true, "MBEDTLS_AES_INTERRUPT_LEVEL": 0, "MBEDTLS_AES_USE_INTERRUPT": true, "MBEDTLS_ALLOW_WEAK_CERTIFICATE_VERIFICATION": false, "MBEDTLS_ASYMMETRIC_CONTENT_LEN": true, "MBEDTLS_ATCA_HW_ECDSA_SIGN": false, "MBEDTLS_ATCA_HW_ECDSA_VERIFY": false, "MBEDTLS_BLOWFISH_C": false, "MBEDTLS_CAMELLIA_C": false, "MBEDTLS_CCM_C": true, "MBEDTLS_CERTIFICATE_BUNDLE": true, "MBEDTLS_CERTIFICATE_BUNDLE_DEFAULT_CMN": false, "MBEDTLS_CERTIFICATE_BUNDLE_DEFAULT_FULL": true, "MBEDTLS_CERTIFICATE_BUNDLE_DEFAULT_NONE": false, "MBEDTLS_CERTIFICATE_BUNDLE_DEPRECATED_LIST": false, "MBEDTLS_CERTIFICATE_BUNDLE_MAX_CERTS": 200, "MBEDTLS_CHACHA20_C": false, "MBEDTLS_CLIENT_SSL_SESSION_TICKETS": true, "MBEDTLS_CMAC_C": false, "MBEDTLS_CUSTOM_CERTIFICATE_BUNDLE": false, "MBEDTLS_CUSTOM_MEM_ALLOC": false, "MBEDTLS_DEBUG": false, "MBEDTLS_DEFAULT_MEM_ALLOC": false, "MBEDTLS_DES_C": false, "MBEDTLS_DHM_C": false, "MBEDTLS_DYNAMIC_BUFFER": false, "MBEDTLS_ECC_OTHER_CURVES_SOFT_FALLBACK": true, "MBEDTLS_ECDH_C": true, "MBEDTLS_ECDSA_C": true, "MBEDTLS_ECDSA_DETERMINISTIC": true, "MBEDTLS_ECJPAKE_C": false, "MBEDTLS_ECP_C": true, "MBEDTLS_ECP_DP_BP256R1_ENABLED": true, "MBEDTLS_ECP_DP_BP384R1_ENABLED": true, "MBEDTLS_ECP_DP_BP512R1_ENABLED": true, "MBEDTLS_ECP_DP_CURVE25519_ENABLED": true, "MBEDTLS_ECP_DP_SECP192K1_ENABLED": true, "MBEDTLS_ECP_DP_SECP192R1_ENABLED": true, "MBEDTLS_ECP_DP_SECP224K1_ENABLED": true, "MBEDTLS_ECP_DP_SECP224R1_ENABLED": true, "MBEDTLS_ECP_DP_SECP256K1_ENABLED": true, "MBEDTLS_ECP_DP_SECP256R1_ENABLED": true, "MBEDTLS_ECP_DP_SECP384R1_ENABLED": true, "MBEDTLS_ECP_DP_SECP521R1_ENABLED": true, "MBEDTLS_ECP_FIXED_POINT_OPTIM": false, "MBEDTLS_ECP_NIST_OPTIM": true, "MBEDTLS_ECP_RESTARTABLE": false, "MBEDTLS_ERROR_STRINGS": true, "MBEDTLS_FS_IO": true, "MBEDTLS_GCM_C": true, "MBEDTLS_GCM_SUPPORT_NON_AES_CIPHER": true, "MBEDTLS_HARDWARE_AES": true, "MBEDTLS_HARDWARE_ECC": true, "MBEDTLS_HARDWARE_GCM": true, "MBEDTLS_HARDWARE_MPI": true, "MBEDTLS_HARDWARE_SHA": true, "MBEDTLS_HAVE_TIME": true, "MBEDTLS_HAVE_TIME_DATE": false, "MBEDTLS_HKDF_C": false, "MBEDTLS_INTERNAL_MEM_ALLOC": true, "MBEDTLS_KEY_EXCHANGE_ECDHE_ECDSA": true, "MBEDTLS_KEY_EXCHANGE_ECDHE_RSA": true, "MBEDTLS_KEY_EXCHANGE_ECDH_ECDSA": true, "MBEDTLS_KEY_EXCHANGE_ECDH_RSA": true, "MBEDTLS_KEY_EXCHANGE_ELLIPTIC_CURVE": true, "MBEDTLS_KEY_EXCHANGE_RSA": true, "MBEDTLS_LARGE_KEY_SOFTWARE_MPI": false, "MBEDTLS_MPI_INTERRUPT_LEVEL": 0, "MBEDTLS_MPI_USE_INTERRUPT": true, "MBEDTLS_NIST_KW_C": false, "MBEDTLS_PEM_PARSE_C": true, "MBEDTLS_PEM_WRITE_C": true, "MBEDTLS_PKCS7_C": true, "MBEDTLS_PK_PARSE_EC_COMPRESSED": true, "MBEDTLS_PK_PARSE_EC_EXTENDED": true, "MBEDTLS_PLATFORM_TIME_ALT": false, "MBEDTLS_POLY1305_C": false, "MBEDTLS_PSK_MODES": false, "MBEDTLS_RIPEMD160_C": false, "MBEDTLS_ROM_MD5": true, "MBEDTLS_SERVER_SSL_SESSION_TICKETS": true, "MBEDTLS_SHA1_C": true, "MBEDTLS_SHA3_C": false, "MBEDTLS_SHA512_C": true, "MBEDTLS_SSL_ALPN": true, "MBEDTLS_SSL_CONTEXT_SERIALIZATION": false, "MBEDTLS_SSL_IN_CONTENT_LEN": 16384, "MBEDTLS_SSL_KEEP_PEER_CERTIFICATE": true, "MBEDTLS_SSL_OUT_CONTENT_LEN": 4096, "MBEDTLS_SSL_PROTO_DTLS": false, "MBEDTLS_SSL_PROTO_GMTSSL1_1": false, "MBEDTLS_SSL_PROTO_TLS1_2": true, "MBEDTLS_SSL_PROTO_TLS1_3": false, "MBEDTLS_SSL_RENEGOTIATION": true, "MBEDTLS_SSL_VARIABLE_BUFFER_LENGTH": false, "MBEDTLS_THREADING_C": false, "MBEDTLS_TLS_CLIENT": true, "MBEDTLS_TLS_CLIENT_ONLY": false, "MBEDTLS_TLS_DISABLED": false, "MBEDTLS_TLS_ENABLED": true, "MBEDTLS_TLS_SERVER": true, "MBEDTLS_TLS_SERVER_AND_CLIENT": true, "MBEDTLS_TLS_SERVER_ONLY": false, "MBEDTLS_X509_CRL_PARSE_C": true, "MBEDTLS_X509_CSR_PARSE_C": true, "MBEDTLS_X509_TRUSTED_CERT_CALLBACK": false, "MBEDTLS_XTEA_C": false, "MCPWM_CTRL_FUNC_IN_IRAM": false, "MCPWM_ENABLE_DEBUG_LOG": false, "MCPWM_ISR_CACHE_SAFE": false, "MCPWM_ISR_HANDLER_IN_IRAM": true, "MCPWM_OBJ_CACHE_SAFE": true, "MCPWM_SKIP_LEGACY_CONFLICT_CHECK": false, "MCPWM_SUPPRESS_DEPRECATE_WARN": false, "MMU_PAGE_MODE": "64KB", "MMU_PAGE_SIZE": 65536, "MMU_PAGE_SIZE_64KB": true, "MQTT_CUSTOM_OUTBOX": false, "MQTT_MSG_ID_INCREMENTAL": false, "MQTT_PROTOCOL_311": true, "MQTT_PROTOCOL_5": false, "MQTT_REPORT_DELETED_MESSAGES": false, "MQTT_SKIP_PUBLISH_IF_DISCONNECTED": false, "MQTT_TASK_CORE_SELECTION_ENABLED": false, "MQTT_TRANSPORT_SSL": true, "MQTT_TRANSPORT_WEBSOCKET": true, "MQTT_TRANSPORT_WEBSOCKET_SECURE": true, "MQTT_USE_CUSTOM_CONFIG": false, "NVS_ASSERT_ERROR_CHECK": false, "NVS_ENCRYPTION": false, "NVS_LEGACY_DUP_KEYS_COMPATIBILITY": false, "OPENTHREAD_ENABLED": false, "OPENTHREAD_SPINEL_ONLY": false, "PARLIO_ENABLE_DEBUG_LOG": false, "PARLIO_ISR_IRAM_SAFE": false, "PARLIO_OBJ_CACHE_SAFE": true, "PARLIO_RX_ISR_CACHE_SAFE": false, "PARLIO_RX_ISR_HANDLER_IN_IRAM": true, "PARLIO_TX_ISR_CACHE_SAFE": false, "PARLIO_TX_ISR_HANDLER_IN_IRAM": true, "PARTITION_TABLE_CUSTOM": true, "PARTITION_TABLE_CUSTOM_FILENAME": "partitions.csv", "PARTITION_TABLE_FILENAME": "partitions.csv", "PARTITION_TABLE_MD5": true, "PARTITION_TABLE_OFFSET": 32768, "PARTITION_TABLE_SINGLE_APP": false, "PARTITION_TABLE_SINGLE_APP_LARGE": false, "PARTITION_TABLE_TWO_OTA": false, "PARTITION_TABLE_TWO_OTA_LARGE": false, "PCNT_CTRL_FUNC_IN_IRAM": false, "PCNT_ENABLE_DEBUG_LOG": false, "PCNT_ISR_IRAM_SAFE": false, "PCNT_SKIP_LEGACY_CONFLICT_CHECK": false, "PCNT_SUPPRESS_DEPRECATE_WARN": false, "PM_ENABLE": false, "PM_POWER_DOWN_PERIPHERAL_IN_LIGHT_SLEEP": false, "PM_SLEEP_FUNC_IN_IRAM": true, "PM_SLP_IRAM_OPT": true, "PTHREAD_DEFAULT_CORE_0": false, "PTHREAD_DEFAULT_CORE_1": false, "PTHREAD_DEFAULT_CORE_NO_AFFINITY": true, "PTHREAD_STACK_MIN": 768, "PTHREAD_TASK_CORE_DEFAULT": -1, "PTHREAD_TASK_NAME_DEFAULT": "pthread", "PTHREAD_TASK_PRIO_DEFAULT": 5, "PTHREAD_TASK_STACK_SIZE_DEFAULT": 3072, "RINGBUF_PLACE_FUNCTIONS_INTO_FLASH": false, "RMT_ENABLE_DEBUG_LOG": false, "RMT_ENCODER_FUNC_IN_IRAM": true, "RMT_ISR_IRAM_SAFE": false, "RMT_OBJ_CACHE_SAFE": true, "RMT_RECV_FUNC_IN_IRAM": false, "RMT_RX_ISR_CACHE_SAFE": false, "RMT_RX_ISR_HANDLER_IN_IRAM": true, "RMT_SKIP_LEGACY_CONFLICT_CHECK": false, "RMT_SUPPRESS_DEPRECATE_WARN": false, "RMT_TX_ISR_CACHE_SAFE": false, "RMT_TX_ISR_HANDLER_IN_IRAM": true, "RTC_CLK_CAL_CYCLES": 1024, "RTC_CLK_SRC_EXT_CRYS": false, "RTC_CLK_SRC_INT_RC": true, "RTC_FAST_CLK_SRC_RC_FAST": true, "RTC_FAST_CLK_SRC_XTAL": false, "SDM_CTRL_FUNC_IN_IRAM": false, "SDM_ENABLE_DEBUG_LOG": false, "SDM_SKIP_LEGACY_CONFLICT_CHECK": false, "SDM_SUPPRESS_DEPRECATE_WARN": false, "SECURE_BOOT": false, "SECURE_BOOT_V2_ECC_SUPPORTED": true, "SECURE_BOOT_V2_PREFERRED": true, "SECURE_BOOT_V2_RSA_SUPPORTED": true, "SECURE_FLASH_ENC_ENABLED": false, "SECURE_ROM_DL_MODE_ENABLED": true, "SECURE_SIGNED_APPS_NO_SECURE_BOOT": false, "SOC_ADC_ATTEN_NUM": 4, "SOC_ADC_CALIBRATION_V1_SUPPORTED": true, "SOC_ADC_CALIB_CHAN_COMPENS_SUPPORTED": true, "SOC_ADC_DIGI_CONTROLLER_NUM": 2, "SOC_ADC_DIGI_DATA_BYTES_PER_CONV": 4, "SOC_ADC_DIGI_IIR_FILTER_NUM": 2, "SOC_ADC_DIGI_MAX_BITWIDTH": 12, "SOC_ADC_DIGI_MIN_BITWIDTH": 12, "SOC_ADC_DIGI_MONITOR_NUM": 2, "SOC_ADC_DIGI_RESULT_BYTES": 4, "SOC_ADC_DIG_CTRL_SUPPORTED": true, "SOC_ADC_DMA_SUPPORTED": true, "SOC_ADC_MAX_CHANNEL_NUM": 8, "SOC_ADC_PATT_LEN_MAX": 16, "SOC_ADC_PERIPH_NUM": 2, "SOC_ADC_RTC_CTRL_SUPPORTED": true, "SOC_ADC_RTC_MAX_BITWIDTH": 12, "SOC_ADC_RTC_MIN_BITWIDTH": 12, "SOC_ADC_SAMPLE_FREQ_THRES_HIGH": 83333, "SOC_ADC_SAMPLE_FREQ_THRES_LOW": 611, "SOC_ADC_SELF_HW_CALI_SUPPORTED": true, "SOC_ADC_SHARED_POWER": true, "SOC_ADC_SUPPORTED": true, "SOC_AES_GDMA": true, "SOC_AES_SUPPORTED": true, "SOC_AES_SUPPORT_AES_128": true, "SOC_AES_SUPPORT_AES_256": true, "SOC_AES_SUPPORT_DMA": true, "SOC_AES_SUPPORT_GCM": true, "SOC_AHB_GDMA_SUPPORTED": true, "SOC_AHB_GDMA_VERSION": 2, "SOC_ANA_CMPR_CAN_DISTINGUISH_EDGE": true, "SOC_ANA_CMPR_NUM": 2, "SOC_ANA_CMPR_SUPPORTED": true, "SOC_ANA_CMPR_SUPPORT_ETM": true, "SOC_APM_SUPPORTED": true, "SOC_ASSIST_DEBUG_SUPPORTED": true, "SOC_ASYNCHRONOUS_BUS_ERROR_MODE": true, "SOC_ASYNC_MEMCPY_SUPPORTED": true, "SOC_AXI_DMA_EXT_MEM_ENC_ALIGNMENT": 16, "SOC_AXI_GDMA_SUPPORTED": true, "SOC_AXI_GDMA_SUPPORT_PSRAM": true, "SOC_BITSCRAMBLER_SUPPORTED": true, "SOC_BOD_SUPPORTED": true, "SOC_BRANCH_PREDICTOR_SUPPORTED": true, "SOC_BROWNOUT_RESET_SUPPORTED": true, "SOC_CACHE_FREEZE_SUPPORTED": true, "SOC_CACHE_INTERNAL_MEM_VIA_L1CACHE": true, "SOC_CACHE_WRITEBACK_SUPPORTED": true, "SOC_CLK_ANA_I2C_MST_HAS_ROOT_GATE": true, "SOC_CLK_APLL_SUPPORTED": true, "SOC_CLK_LP_FAST_SUPPORT_LP_PLL": true, "SOC_CLK_LP_FAST_SUPPORT_XTAL": true, "SOC_CLK_MPLL_SUPPORTED": true, "SOC_CLK_RC32K_SUPPORTED": true, "SOC_CLK_RC_FAST_SUPPORT_CALIBRATION": true, "SOC_CLK_SDIO_PLL_SUPPORTED": true, "SOC_CLK_TREE_SUPPORTED": true, "SOC_CLK_XTAL32K_SUPPORTED": true, "SOC_CLOCKOUT_SUPPORT_CHANNEL_DIVIDER": true, "SOC_COEX_HW_PTI": true, "SOC_CPU_BREAKPOINTS_NUM": 3, "SOC_CPU_COPROC_NUM": 3, "SOC_CPU_CORES_NUM": 2, "SOC_CPU_HAS_FLEXIBLE_INTC": true, "SOC_CPU_HAS_FPU": true, "SOC_CPU_HAS_FPU_EXT_ILL_BUG": true, "SOC_CPU_HAS_HWLOOP": true, "SOC_CPU_HAS_HWLOOP_STATE_BUG": true, "SOC_CPU_HAS_LOCKUP_RESET": true, "SOC_CPU_HAS_PIE": true, "SOC_CPU_HAS_PMA": true, "SOC_CPU_IDRAM_SPLIT_USING_PMP": true, "SOC_CPU_INTR_NUM": 32, "SOC_CPU_IN_TOP_DOMAIN": true, "SOC_CPU_PMP_REGION_GRANULARITY": 128, "SOC_CPU_WATCHPOINTS_NUM": 3, "SOC_CPU_WATCHPOINT_MAX_REGION_SIZE": 256, "SOC_DCDC_SUPPORTED": true, "SOC_DEBUG_PROBE_MAX_OUTPUT_WIDTH": 16, "SOC_DEBUG_PROBE_NUM_UNIT": 1, "SOC_DEBUG_PROBE_SUPPORTED": true, "SOC_DEDICATED_GPIO_SUPPORTED": true, "SOC_DEDIC_GPIO_IN_CHANNELS_NUM": 8, "SOC_DEDIC_GPIO_OUT_CHANNELS_NUM": 8, "SOC_DEDIC_PERIPH_ALWAYS_ENABLE": true, "SOC_DEEP_SLEEP_SUPPORTED": true, "SOC_DIG_SIGN_SUPPORTED": true, "SOC_DMA2D_GROUPS": 1, "SOC_DMA2D_RX_CHANNELS_PER_GROUP": 2, "SOC_DMA2D_SUPPORTED": true, "SOC_DMA2D_TX_CHANNELS_PER_GROUP": 3, "SOC_DMA_CAN_ACCESS_FLASH": true, "SOC_DS_KEY_CHECK_MAX_WAIT_US": 1100, "SOC_DS_KEY_PARAM_MD_IV_LENGTH": 16, "SOC_DS_SIGNATURE_MAX_BIT_LEN": 4096, "SOC_DW_GDMA_SUPPORTED": true, "SOC_ECC_EXTENDED_MODES_SUPPORTED": true, "SOC_ECC_SUPPORTED": true, "SOC_ECDSA_SUPPORT_DETERMINISTIC_MODE": true, "SOC_ECDSA_SUPPORT_EXPORT_PUBKEY": true, "SOC_ECDSA_USES_MPI": true, "SOC_EFUSE_DIS_DIRECT_BOOT": true, "SOC_EFUSE_DIS_DOWNLOAD_MSPI": true, "SOC_EFUSE_DIS_PAD_JTAG": true, "SOC_EFUSE_DIS_USB_JTAG": true, "SOC_EFUSE_ECDSA_KEY": true, "SOC_EFUSE_KEY_PURPOSE_FIELD": true, "SOC_EFUSE_REVOKE_BOOT_KEY_DIGESTS": true, "SOC_EFUSE_SECURE_BOOT_KEY_DIGESTS": 3, "SOC_EFUSE_SOFT_DIS_JTAG": true, "SOC_EFUSE_SUPPORTED": true, "SOC_EMAC_IEEE1588V2_SUPPORTED": true, "SOC_EMAC_MII_USE_GPIO_MATRIX": true, "SOC_EMAC_SUPPORTED": true, "SOC_EMAC_USE_MULTI_IO_MUX": true, "SOC_ETM_CHANNELS_PER_GROUP": 50, "SOC_ETM_GROUPS": 1, "SOC_ETM_SUPPORTED": true, "SOC_ETM_SUPPORT_SLEEP_RETENTION": true, "SOC_FLASH_ENCRYPTED_XTS_AES_BLOCK_MAX": 64, "SOC_FLASH_ENCRYPTION_XTS_AES": true, "SOC_FLASH_ENCRYPTION_XTS_AES_128": true, "SOC_FLASH_ENCRYPTION_XTS_AES_256": true, "SOC_FLASH_ENCRYPTION_XTS_AES_OPTIONS": true, "SOC_FLASH_ENC_SUPPORTED": true, "SOC_GDMA_NUM_GROUPS_MAX": 2, "SOC_GDMA_PAIRS_PER_GROUP_MAX": 3, "SOC_GDMA_SUPPORTED": true, "SOC_GDMA_SUPPORT_CRC": true, "SOC_GDMA_SUPPORT_ETM": true, "SOC_GDMA_SUPPORT_SLEEP_RETENTION": true, "SOC_GPIO_CLOCKOUT_BY_GPIO_MATRIX": true, "SOC_GPIO_CLOCKOUT_CHANNEL_NUM": 2, "SOC_GPIO_DEEP_SLEEP_WAKE_SUPPORTED_PIN_CNT": 16, "SOC_GPIO_DEEP_SLEEP_WAKE_VALID_GPIO_MASK": 0, "SOC_GPIO_FLEX_GLITCH_FILTER_NUM": 8, "SOC_GPIO_IN_RANGE_MAX": 54, "SOC_GPIO_OUT_RANGE_MAX": 54, "SOC_GPIO_PIN_COUNT": 55, "SOC_GPIO_PORT": 1, "SOC_GPIO_SUPPORT_DEEPSLEEP_WAKEUP": true, "SOC_GPIO_SUPPORT_ETM": true, "SOC_GPIO_SUPPORT_FORCE_HOLD": true, "SOC_GPIO_SUPPORT_PIN_GLITCH_FILTER": true, "SOC_GPIO_SUPPORT_PIN_HYS_FILTER": true, "SOC_GPIO_SUPPORT_RTC_INDEPENDENT": true, "SOC_GPIO_VALID_DIGITAL_IO_PAD_MASK": 36028797018898432, "SOC_GPIO_VALID_GPIO_MASK": 36028797018963967, "SOC_GPSPI_SUPPORTED": true, "SOC_GPTIMER_SUPPORTED": true, "SOC_GP_LDO_SUPPORTED": true, "SOC_HMAC_SUPPORTED": true, "SOC_HP_CPU_HAS_MULTIPLE_CORES": true, "SOC_HP_I2C_NUM": 2, "SOC_I2C_CMD_REG_NUM": 8, "SOC_I2C_FIFO_LEN": 32, "SOC_I2C_NUM": 3, "SOC_I2C_SLAVE_CAN_GET_STRETCH_CAUSE": true, "SOC_I2C_SLAVE_SUPPORT_BROADCAST": true, "SOC_I2C_SLAVE_SUPPORT_I2CRAM_ACCESS": true, "SOC_I2C_SLAVE_SUPPORT_SLAVE_UNMATCH": true, "SOC_I2C_SUPPORTED": true, "SOC_I2C_SUPPORT_10BIT_ADDR": true, "SOC_I2C_SUPPORT_HW_CLR_BUS": true, "SOC_I2C_SUPPORT_HW_FSM_RST": true, "SOC_I2C_SUPPORT_RTC": true, "SOC_I2C_SUPPORT_SLAVE": true, "SOC_I2C_SUPPORT_SLEEP_RETENTION": true, "SOC_I2C_SUPPORT_XTAL": true, "SOC_I2S_HW_VERSION_2": true, "SOC_I2S_NUM": 3, "SOC_I2S_PDM_MAX_RX_LINES": 4, "SOC_I2S_PDM_MAX_TX_LINES": 2, "SOC_I2S_SUPPORTED": true, "SOC_I2S_SUPPORTS_APLL": true, "SOC_I2S_SUPPORTS_ETM": true, "SOC_I2S_SUPPORTS_PCM": true, "SOC_I2S_SUPPORTS_PCM2PDM": true, "SOC_I2S_SUPPORTS_PDM": true, "SOC_I2S_SUPPORTS_PDM2PCM": true, "SOC_I2S_SUPPORTS_PDM_RX": true, "SOC_I2S_SUPPORTS_PDM_RX_HP_FILTER": true, "SOC_I2S_SUPPORTS_PDM_TX": true, "SOC_I2S_SUPPORTS_TDM": true, "SOC_I2S_SUPPORTS_TX_SYNC_CNT": true, "SOC_I2S_SUPPORTS_XTAL": true, "SOC_I2S_SUPPORT_SLEEP_RETENTION": true, "SOC_I2S_TDM_FULL_DATA_WIDTH": true, "SOC_I3C_MASTER_ADDRESS_TABLE_NUM": 12, "SOC_I3C_MASTER_COMMAND_TABLE_NUM": 12, "SOC_I3C_MASTER_PERIPH_NUM": true, "SOC_I3C_MASTER_SUPPORTED": true, "SOC_INT_CLIC_SUPPORTED": true, "SOC_INT_HW_NESTED_SUPPORTED": true, "SOC_ISP_AE_BLOCK_X_NUMS": 5, "SOC_ISP_AE_BLOCK_Y_NUMS": 5, "SOC_ISP_AE_CTLR_NUMS": 1, "SOC_ISP_AF_CTLR_NUMS": 1, "SOC_ISP_AF_WINDOW_NUMS": 3, "SOC_ISP_BF_SUPPORTED": true, "SOC_ISP_BF_TEMPLATE_X_NUMS": 3, "SOC_ISP_BF_TEMPLATE_Y_NUMS": 3, "SOC_ISP_CCM_DIMENSION": 3, "SOC_ISP_CCM_SUPPORTED": true, "SOC_ISP_COLOR_SUPPORTED": true, "SOC_ISP_DEMOSAIC_GRAD_RATIO_DEC_BITS": 4, "SOC_ISP_DEMOSAIC_GRAD_RATIO_INT_BITS": 2, "SOC_ISP_DEMOSAIC_GRAD_RATIO_RES_BITS": 26, "SOC_ISP_DEMOSAIC_SUPPORTED": true, "SOC_ISP_DVP_CTLR_NUMS": 1, "SOC_ISP_DVP_DATA_WIDTH_MAX": 16, "SOC_ISP_DVP_SUPPORTED": true, "SOC_ISP_HIST_BLOCK_X_NUMS": 5, "SOC_ISP_HIST_BLOCK_Y_NUMS": 5, "SOC_ISP_HIST_CTLR_NUMS": 1, "SOC_ISP_HIST_INTERVAL_NUMS": 15, "SOC_ISP_HIST_SEGMENT_NUMS": 16, "SOC_ISP_LSC_GRAD_RATIO_DEC_BITS": 8, "SOC_ISP_LSC_GRAD_RATIO_INT_BITS": 2, "SOC_ISP_LSC_GRAD_RATIO_RES_BITS": 22, "SOC_ISP_LSC_SUPPORTED": true, "SOC_ISP_NUMS": 1, "SOC_ISP_SHARE_CSI_BRG": true, "SOC_ISP_SHARPEN_H_FREQ_COEF_DEC_BITS": 5, "SOC_ISP_SHARPEN_H_FREQ_COEF_INT_BITS": 3, "SOC_ISP_SHARPEN_H_FREQ_COEF_RES_BITS": 24, "SOC_ISP_SHARPEN_M_FREQ_COEF_DEC_BITS": 5, "SOC_ISP_SHARPEN_M_FREQ_COEF_INT_BITS": 3, "SOC_ISP_SHARPEN_M_FREQ_COEF_RES_BITS": 24, "SOC_ISP_SHARPEN_SUPPORTED": true, "SOC_ISP_SHARPEN_TEMPLATE_X_NUMS": 3, "SOC_ISP_SHARPEN_TEMPLATE_Y_NUMS": 3, "SOC_ISP_SUPPORTED": true, "SOC_JPEG_CODEC_SUPPORTED": true, "SOC_JPEG_DECODE_SUPPORTED": true, "SOC_JPEG_ENCODE_SUPPORTED": true, "SOC_KEY_MANAGER_ECDSA_KEY_DEPLOY": true, "SOC_KEY_MANAGER_FE_KEY_DEPLOY": true, "SOC_LCDCAM_CAM_DATA_WIDTH_MAX": 16, "SOC_LCDCAM_CAM_PERIPH_NUM": 1, "SOC_LCDCAM_CAM_SUPPORTED": true, "SOC_LCDCAM_CAM_SUPPORT_RGB_YUV_CONV": true, "SOC_LCDCAM_I80_BUS_WIDTH": 24, "SOC_LCDCAM_I80_LCD_SUPPORTED": true, "SOC_LCDCAM_I80_NUM_BUSES": 1, "SOC_LCDCAM_RGB_DATA_WIDTH": 24, "SOC_LCDCAM_RGB_LCD_SUPPORTED": true, "SOC_LCDCAM_RGB_NUM_PANELS": 1, "SOC_LCDCAM_SUPPORTED": true, "SOC_LCD_I80_SUPPORTED": true, "SOC_LCD_RGB_SUPPORTED": true, "SOC_LCD_SUPPORT_RGB_YUV_CONV": true, "SOC_LEDC_CHANNEL_NUM": 8, "SOC_LEDC_FADE_PARAMS_BIT_WIDTH": 10, "SOC_LEDC_GAMMA_CURVE_FADE_RANGE_MAX": 16, "SOC_LEDC_GAMMA_CURVE_FADE_SUPPORTED": true, "SOC_LEDC_SUPPORTED": true, "SOC_LEDC_SUPPORT_FADE_STOP": true, "SOC_LEDC_SUPPORT_PLL_DIV_CLOCK": true, "SOC_LEDC_SUPPORT_SLEEP_RETENTION": true, "SOC_LEDC_SUPPORT_XTAL_CLOCK": true, "SOC_LEDC_TIMER_BIT_WIDTH": 20, "SOC_LEDC_TIMER_NUM": 4, "SOC_LIGHT_SLEEP_SUPPORTED": true, "SOC_LP_ADC_SUPPORTED": true, "SOC_LP_CORE_SUPPORTED": true, "SOC_LP_CORE_SUPPORT_ETM": true, "SOC_LP_CORE_SUPPORT_LP_ADC": true, "SOC_LP_CORE_SUPPORT_LP_VAD": true, "SOC_LP_CORE_SUPPORT_STORE_LOAD_EXCEPTIONS": true, "SOC_LP_GPIO_MATRIX_SUPPORTED": true, "SOC_LP_I2C_FIFO_LEN": 16, "SOC_LP_I2C_NUM": 1, "SOC_LP_I2C_SUPPORTED": true, "SOC_LP_I2S_NUM": 1, "SOC_LP_I2S_SUPPORTED": true, "SOC_LP_I2S_SUPPORT_VAD": true, "SOC_LP_IO_CLOCK_IS_INDEPENDENT": true, "SOC_LP_IO_HAS_INDEPENDENT_WAKEUP_SOURCE": true, "SOC_LP_PERIPHERALS_SUPPORTED": true, "SOC_LP_SPI_MAXIMUM_BUFFER_SIZE": 64, "SOC_LP_SPI_PERIPH_NUM": true, "SOC_LP_SPI_SUPPORTED": true, "SOC_LP_TIMER_BIT_WIDTH_HI": 16, "SOC_LP_TIMER_BIT_WIDTH_LO": 32, "SOC_LP_TIMER_SUPPORTED": true, "SOC_LP_UART_FIFO_LEN": 16, "SOC_LP_VAD_SUPPORTED": true, "SOC_MCPWM_CAPTURE_CHANNELS_PER_TIMER": 3, "SOC_MCPWM_CAPTURE_CLK_FROM_GROUP": true, "SOC_MCPWM_CAPTURE_TIMERS_PER_GROUP": true, "SOC_MCPWM_COMPARATORS_PER_OPERATOR": 2, "SOC_MCPWM_EVENT_COMPARATORS_PER_OPERATOR": 2, "SOC_MCPWM_GENERATORS_PER_OPERATOR": 2, "SOC_MCPWM_GPIO_FAULTS_PER_GROUP": 3, "SOC_MCPWM_GPIO_SYNCHROS_PER_GROUP": 3, "SOC_MCPWM_GROUPS": 2, "SOC_MCPWM_OPERATORS_PER_GROUP": 3, "SOC_MCPWM_SUPPORTED": true, "SOC_MCPWM_SUPPORT_ETM": true, "SOC_MCPWM_SUPPORT_EVENT_COMPARATOR": true, "SOC_MCPWM_SUPPORT_SLEEP_RETENTION": true, "SOC_MCPWM_SWSYNC_CAN_PROPAGATE": true, "SOC_MCPWM_TIMERS_PER_GROUP": 3, "SOC_MCPWM_TRIGGERS_PER_OPERATOR": 2, "SOC_MEMSPI_FLASH_PSRAM_INDEPENDENT": true, "SOC_MEMSPI_IS_INDEPENDENT": true, "SOC_MEMSPI_SRC_FREQ_120M_SUPPORTED": true, "SOC_MEMSPI_SRC_FREQ_20M_SUPPORTED": true, "SOC_MEMSPI_SRC_FREQ_40M_SUPPORTED": true, "SOC_MEMSPI_SRC_FREQ_80M_SUPPORTED": true, "SOC_MEMSPI_TIMING_TUNING_BY_DQS": true, "SOC_MEMSPI_TIMING_TUNING_BY_FLASH_DELAY": true, "SOC_MEM_NON_CONTIGUOUS_SRAM": true, "SOC_MEM_TCM_SUPPORTED": true, "SOC_MIPI_CSI_SUPPORTED": true, "SOC_MIPI_DSI_SUPPORTED": true, "SOC_MMU_DI_VADDR_SHARED": true, "SOC_MMU_LINEAR_ADDRESS_REGION_NUM": 2, "SOC_MMU_PERIPH_NUM": 2, "SOC_MMU_PER_EXT_MEM_TARGET": true, "SOC_MPI_MEM_BLOCKS_NUM": 4, "SOC_MPI_OPERATIONS_NUM": 3, "SOC_MPI_SUPPORTED": true, "SOC_MPU_MIN_REGION_SIZE": 536870912, "SOC_MPU_REGIONS_MAX_NUM": 8, "SOC_MSPI_HAS_INDEPENT_IOMUX": true, "SOC_MWDT_SUPPORT_SLEEP_RETENTION": true, "SOC_MWDT_SUPPORT_XTAL": true, "SOC_PARLIO_GROUPS": 1, "SOC_PARLIO_RX_CLK_SUPPORT_GATING": true, "SOC_PARLIO_RX_CLK_SUPPORT_OUTPUT": true, "SOC_PARLIO_RX_UNITS_PER_GROUP": 1, "SOC_PARLIO_RX_UNIT_MAX_DATA_WIDTH": 16, "SOC_PARLIO_SUPPORTED": true, "SOC_PARLIO_SUPPORT_I80_LCD": true, "SOC_PARLIO_SUPPORT_SLEEP_RETENTION": true, "SOC_PARLIO_SUPPORT_SPI_LCD": true, "SOC_PARLIO_TRANS_BIT_ALIGN": true, "SOC_PARLIO_TX_CLK_SUPPORT_GATING": true, "SOC_PARLIO_TX_SUPPORT_LOOP_TRANSMISSION": true, "SOC_PARLIO_TX_UNITS_PER_GROUP": 1, "SOC_PARLIO_TX_UNIT_MAX_DATA_WIDTH": 16, "SOC_PAU_IN_TOP_DOMAIN": true, "SOC_PAU_SUPPORTED": true, "SOC_PCNT_CHANNELS_PER_UNIT": 2, "SOC_PCNT_GROUPS": 1, "SOC_PCNT_SUPPORTED": true, "SOC_PCNT_SUPPORT_CLEAR_SIGNAL": true, "SOC_PCNT_SUPPORT_RUNTIME_THRES_UPDATE": true, "SOC_PCNT_SUPPORT_SLEEP_RETENTION": true, "SOC_PCNT_THRES_POINT_PER_UNIT": 2, "SOC_PCNT_UNITS_PER_GROUP": 4, "SOC_PERIPH_CLK_CTRL_SHARED": true, "SOC_PHY_DIG_REGS_MEM_SIZE": 21, "SOC_PMU_SUPPORTED": true, "SOC_PM_CACHE_RETENTION_BY_PAU": true, "SOC_PM_CPU_RETENTION_BY_SW": true, "SOC_PM_EXT1_WAKEUP_BY_PMU": true, "SOC_PM_PAU_LINK_NUM": 4, "SOC_PM_PAU_REGDMA_LINK_MULTI_ADDR": true, "SOC_PM_PAU_REGDMA_UPDATE_CACHE_BEFORE_WAIT_COMPARE": true, "SOC_PM_RETENTION_MODULE_NUM": 64, "SOC_PM_SUPPORTED": true, "SOC_PM_SUPPORT_CNNT_PD": true, "SOC_PM_SUPPORT_DEEPSLEEP_CHECK_STUB_ONLY": true, "SOC_PM_SUPPORT_EXT1_WAKEUP": true, "SOC_PM_SUPPORT_EXT1_WAKEUP_MODE_PER_PIN": true, "SOC_PM_SUPPORT_RC32K_PD": true, "SOC_PM_SUPPORT_RC_FAST_PD": true, "SOC_PM_SUPPORT_RTC_PERIPH_PD": true, "SOC_PM_SUPPORT_TOP_PD": true, "SOC_PM_SUPPORT_TOUCH_SENSOR_WAKEUP": true, "SOC_PM_SUPPORT_VDDSDIO_PD": true, "SOC_PM_SUPPORT_WIFI_WAKEUP": true, "SOC_PM_SUPPORT_XTAL32K_PD": true, "SOC_PPA_SUPPORTED": true, "SOC_PSRAM_DMA_CAPABLE": true, "SOC_PSRAM_VDD_POWER_MPLL": true, "SOC_RMT_CHANNELS_PER_GROUP": 8, "SOC_RMT_GROUPS": 1, "SOC_RMT_MEM_WORDS_PER_CHANNEL": 48, "SOC_RMT_RX_CANDIDATES_PER_GROUP": 4, "SOC_RMT_SUPPORTED": true, "SOC_RMT_SUPPORT_DMA": true, "SOC_RMT_SUPPORT_RC_FAST": true, "SOC_RMT_SUPPORT_RX_DEMODULATION": true, "SOC_RMT_SUPPORT_RX_PINGPONG": true, "SOC_RMT_SUPPORT_SLEEP_RETENTION": true, "SOC_RMT_SUPPORT_TX_ASYNC_STOP": true, "SOC_RMT_SUPPORT_TX_CARRIER_DATA_ONLY": true, "SOC_RMT_SUPPORT_TX_LOOP_AUTO_STOP": true, "SOC_RMT_SUPPORT_TX_LOOP_COUNT": true, "SOC_RMT_SUPPORT_TX_SYNCHRO": true, "SOC_RMT_SUPPORT_XTAL": true, "SOC_RMT_TX_CANDIDATES_PER_GROUP": 4, "SOC_RNG_SUPPORTED": true, "SOC_RSA_MAX_BIT_LEN": 4096, "SOC_RTCIO_EDGE_WAKE_SUPPORTED": true, "SOC_RTCIO_HOLD_SUPPORTED": true, "SOC_RTCIO_INPUT_OUTPUT_SUPPORTED": true, "SOC_RTCIO_PIN_COUNT": 16, "SOC_RTCIO_WAKE_SUPPORTED": true, "SOC_RTC_FAST_MEM_SUPPORTED": true, "SOC_RTC_MEM_SUPPORTED": true, "SOC_SDMMC_DELAY_PHASE_NUM": 4, "SOC_SDMMC_HOST_SUPPORTED": true, "SOC_SDMMC_IO_POWER_EXTERNAL": true, "SOC_SDMMC_NUM_SLOTS": 2, "SOC_SDMMC_PSRAM_DMA_CAPABLE": true, "SOC_SDMMC_UHS_I_SUPPORTED": true, "SOC_SDMMC_USE_GPIO_MATRIX": true, "SOC_SDMMC_USE_IOMUX": true, "SOC_SDM_CHANNELS_PER_GROUP": 8, "SOC_SDM_CLK_SUPPORT_PLL_F80M": true, "SOC_SDM_CLK_SUPPORT_XTAL": true, "SOC_SDM_GROUPS": 1, "SOC_SDM_SUPPORTED": true, "SOC_SECURE_BOOT_SUPPORTED": true, "SOC_SECURE_BOOT_V2_ECC": true, "SOC_SECURE_BOOT_V2_RSA": true, "SOC_SHARED_IDCACHE_SUPPORTED": true, "SOC_SHA_DMA_MAX_BUFFER_SIZE": 3968, "SOC_SHA_GDMA": true, "SOC_SHA_SUPPORTED": true, "SOC_SHA_SUPPORT_DMA": true, "SOC_SHA_SUPPORT_RESUME": true, "SOC_SHA_SUPPORT_SHA1": true, "SOC_SHA_SUPPORT_SHA224": true, "SOC_SHA_SUPPORT_SHA256": true, "SOC_SHA_SUPPORT_SHA384": true, "SOC_SHA_SUPPORT_SHA512": true, "SOC_SHA_SUPPORT_SHA512_224": true, "SOC_SHA_SUPPORT_SHA512_256": true, "SOC_SHA_SUPPORT_SHA512_T": true, "SOC_SIMD_INSTRUCTION_SUPPORTED": true, "SOC_SIMD_PREFERRED_DATA_ALIGNMENT": 16, "SOC_SLEEP_SYSTIMER_STALL_WORKAROUND": true, "SOC_SLEEP_TGWDT_STOP_WORKAROUND": true, "SOC_SPIRAM_SUPPORTED": true, "SOC_SPIRAM_XIP_SUPPORTED": true, "SOC_SPI_FLASH_SUPPORTED": true, "SOC_SPI_MAXIMUM_BUFFER_SIZE": 64, "SOC_SPI_MAX_CS_NUM": 6, "SOC_SPI_MAX_PRE_DIVIDER": 16, "SOC_SPI_MEM_SUPPORT_AUTO_RESUME": true, "SOC_SPI_MEM_SUPPORT_AUTO_SUSPEND": true, "SOC_SPI_MEM_SUPPORT_AUTO_WAIT_IDLE": true, "SOC_SPI_MEM_SUPPORT_CACHE_32BIT_ADDR_MAP": true, "SOC_SPI_MEM_SUPPORT_CHECK_SUS": true, "SOC_SPI_MEM_SUPPORT_IDLE_INTR": true, "SOC_SPI_MEM_SUPPORT_SW_SUSPEND": true, "SOC_SPI_MEM_SUPPORT_TIMING_TUNING": true, "SOC_SPI_PERIPH_NUM": 3, "SOC_SPI_PERIPH_SUPPORT_CONTROL_DUMMY_OUT": true, "SOC_SPI_SLAVE_SUPPORT_SEG_TRANS": true, "SOC_SPI_SUPPORT_CD_SIG": true, "SOC_SPI_SUPPORT_CLK_RC_FAST": true, "SOC_SPI_SUPPORT_CLK_SPLL": true, "SOC_SPI_SUPPORT_CLK_XTAL": true, "SOC_SPI_SUPPORT_DDRCLK": true, "SOC_SPI_SUPPORT_OCT": true, "SOC_SPI_SUPPORT_SLAVE_HD_VER2": true, "SOC_SPI_SUPPORT_SLEEP_RETENTION": true, "SOC_SUPPORTS_SECURE_DL_MODE": true, "SOC_SUPPORT_SECURE_BOOT_REVOKE_KEY": true, "SOC_SYSTIMER_ALARM_MISS_COMPENSATE": true, "SOC_SYSTIMER_ALARM_NUM": 3, "SOC_SYSTIMER_BIT_WIDTH_HI": 20, "SOC_SYSTIMER_BIT_WIDTH_LO": 32, "SOC_SYSTIMER_COUNTER_NUM": 2, "SOC_SYSTIMER_FIXED_DIVIDER": true, "SOC_SYSTIMER_INT_LEVEL": true, "SOC_SYSTIMER_SUPPORTED": true, "SOC_SYSTIMER_SUPPORT_ETM": true, "SOC_SYSTIMER_SUPPORT_RC_FAST": true, "SOC_TEMPERATURE_SENSOR_INTR_SUPPORT": true, "SOC_TEMPERATURE_SENSOR_LP_PLL_SUPPORT": true, "SOC_TEMPERATURE_SENSOR_SUPPORT_ETM": true, "SOC_TEMPERATURE_SENSOR_SUPPORT_SLEEP_RETENTION": true, "SOC_TEMP_SENSOR_SUPPORTED": true, "SOC_TIMER_GROUPS": 2, "SOC_TIMER_GROUP_COUNTER_BIT_WIDTH": 54, "SOC_TIMER_GROUP_SUPPORT_RC_FAST": true, "SOC_TIMER_GROUP_SUPPORT_XTAL": true, "SOC_TIMER_GROUP_TIMERS_PER_GROUP": 2, "SOC_TIMER_GROUP_TOTAL_TIMERS": 4, "SOC_TIMER_SUPPORT_ETM": true, "SOC_TIMER_SUPPORT_SLEEP_RETENTION": true, "SOC_TOUCH_MAX_CHAN_ID": 14, "SOC_TOUCH_MIN_CHAN_ID": 1, "SOC_TOUCH_PROXIMITY_CHANNEL_NUM": 3, "SOC_TOUCH_PROXIMITY_MEAS_DONE_SUPPORTED": true, "SOC_TOUCH_SAMPLE_CFG_NUM": 3, "SOC_TOUCH_SENSOR_NUM": 14, "SOC_TOUCH_SENSOR_SUPPORTED": true, "SOC_TOUCH_SENSOR_VERSION": 3, "SOC_TOUCH_SUPPORT_BENCHMARK": true, "SOC_TOUCH_SUPPORT_FREQ_HOP": true, "SOC_TOUCH_SUPPORT_PROX_SENSING": true, "SOC_TOUCH_SUPPORT_SLEEP_WAKEUP": true, "SOC_TOUCH_SUPPORT_WATERPROOF": true, "SOC_TSENS_IS_INDEPENDENT_FROM_ADC": true, "SOC_TWAI_BRP_MAX": 32768, "SOC_TWAI_BRP_MIN": 2, "SOC_TWAI_CLK_SUPPORT_XTAL": true, "SOC_TWAI_CONTROLLER_NUM": 3, "SOC_TWAI_MASK_FILTER_NUM": 1, "SOC_TWAI_SUPPORTED": true, "SOC_TWAI_SUPPORTS_RX_STATUS": true, "SOC_TWAI_SUPPORT_SLEEP_RETENTION": true, "SOC_UART_BITRATE_MAX": 5000000, "SOC_UART_FIFO_LEN": 128, "SOC_UART_HAS_LP_UART": true, "SOC_UART_HP_NUM": 5, "SOC_UART_LP_NUM": 1, "SOC_UART_NUM": 6, "SOC_UART_SUPPORTED": true, "SOC_UART_SUPPORT_FSM_TX_WAIT_SEND": true, "SOC_UART_SUPPORT_PLL_F80M_CLK": true, "SOC_UART_SUPPORT_RTC_CLK": true, "SOC_UART_SUPPORT_SLEEP_RETENTION": true, "SOC_UART_SUPPORT_WAKEUP_INT": true, "SOC_UART_SUPPORT_XTAL_CLK": true, "SOC_UART_WAKEUP_CHARS_SEQ_MAX_LEN": 5, "SOC_UART_WAKEUP_SUPPORT_ACTIVE_THRESH_MODE": true, "SOC_UART_WAKEUP_SUPPORT_CHAR_SEQ_MODE": true, "SOC_UART_WAKEUP_SUPPORT_FIFO_THRESH_MODE": true, "SOC_UART_WAKEUP_SUPPORT_START_BIT_MODE": true, "SOC_UHCI_NUM": 1, "SOC_UHCI_SUPPORTED": true, "SOC_ULP_LP_UART_SUPPORTED": true, "SOC_ULP_SUPPORTED": true, "SOC_USB_OTG_PERIPH_NUM": 2, "SOC_USB_OTG_SUPPORTED": true, "SOC_USB_SERIAL_JTAG_SUPPORTED": true, "SOC_USB_UTMI_PHY_NO_POWER_OFF_ISO": true, "SOC_USB_UTMI_PHY_NUM": 1, "SOC_VBAT_SUPPORTED": true, "SOC_WDT_SUPPORTED": true, "SOC_WIFI_LIGHT_SLEEP_CLK_WIDTH": 12, "SOC_WIRELESS_HOST_SUPPORTED": true, "SOC_XTAL_SUPPORT_40M": true, "SPIFFS_API_DBG": false, "SPIFFS_CACHE": true, "SPIFFS_CACHE_DBG": false, "SPIFFS_CACHE_STATS": false, "SPIFFS_CACHE_WR": true, "SPIFFS_CHECK_DBG": false, "SPIFFS_DBG": false, "SPIFFS_FOLLOW_SYMLINKS": false, "SPIFFS_GC_DBG": false, "SPIFFS_GC_MAX_RUNS": 10, "SPIFFS_GC_STATS": false, "SPIFFS_MAX_PARTITIONS": 3, "SPIFFS_META_LENGTH": 4, "SPIFFS_OBJ_NAME_LEN": 32, "SPIFFS_PAGE_CHECK": true, "SPIFFS_PAGE_SIZE": 256, "SPIFFS_TEST_VISUALISATION": false, "SPIFFS_USE_MAGIC": true, "SPIFFS_USE_MAGIC_LENGTH": true, "SPIFFS_USE_MTIME": true, "SPIRAM": false, "SPI_FLASH_AUTO_SUSPEND": false, "SPI_FLASH_BROWNOUT_RESET": true, "SPI_FLASH_BROWNOUT_RESET_XMC": true, "SPI_FLASH_BYPASS_BLOCK_ERASE": false, "SPI_FLASH_CHECK_ERASE_TIMEOUT_DISABLED": false, "SPI_FLASH_DANGEROUS_WRITE_ABORTS": true, "SPI_FLASH_DANGEROUS_WRITE_ALLOWED": false, "SPI_FLASH_DANGEROUS_WRITE_FAILS": false, "SPI_FLASH_ENABLE_COUNTERS": false, "SPI_FLASH_ENABLE_ENCRYPTED_READ_WRITE": true, "SPI_FLASH_ERASE_YIELD_DURATION_MS": 20, "SPI_FLASH_ERASE_YIELD_TICKS": 1, "SPI_FLASH_FORCE_ENABLE_C6_H2_SUSPEND": false, "SPI_FLASH_FORCE_ENABLE_XMC_C_SUSPEND": false, "SPI_FLASH_HPM_AUTO": true, "SPI_FLASH_HPM_DC_AUTO": true, "SPI_FLASH_HPM_DC_DISABLE": false, "SPI_FLASH_HPM_DIS": false, "SPI_FLASH_HPM_ENA": false, "SPI_FLASH_HPM_ON": true, "SPI_FLASH_OVERRIDE_CHIP_DRIVER_LIST": false, "SPI_FLASH_PLACE_FUNCTIONS_IN_IRAM": true, "SPI_FLASH_ROM_DRIVER_PATCH": true, "SPI_FLASH_SIZE_OVERRIDE": false, "SPI_FLASH_SUPPORT_BOYA_CHIP": false, "SPI_FLASH_SUPPORT_GD_CHIP": false, "SPI_FLASH_SUPPORT_ISSI_CHIP": false, "SPI_FLASH_SUPPORT_MXIC_CHIP": false, "SPI_FLASH_SUPPORT_TH_CHIP": false, "SPI_FLASH_SUPPORT_WINBOND_CHIP": false, "SPI_FLASH_SUSPEND_TSUS_VAL_US": 50, "SPI_FLASH_VENDOR_XMC_SUPPORTED": true, "SPI_FLASH_VERIFY_WRITE": false, "SPI_FLASH_WRITE_CHUNK_SIZE": 8192, "SPI_FLASH_YIELD_DURING_ERASE": true, "SPI_MASTER_IN_IRAM": false, "SPI_MASTER_ISR_IN_IRAM": true, "SPI_SLAVE_IN_IRAM": false, "SPI_SLAVE_ISR_IN_IRAM": true, "TEMP_SENSOR_ENABLE_DEBUG_LOG": false, "TEMP_SENSOR_ISR_IRAM_SAFE": false, "TEMP_SENSOR_SKIP_LEGACY_CONFLICT_CHECK": false, "TEMP_SENSOR_SUPPRESS_DEPRECATE_WARN": false, "TOUCH_CTRL_FUNC_IN_IRAM": false, "TOUCH_ENABLE_DEBUG_LOG": false, "TOUCH_ISR_IRAM_SAFE": false, "TOUCH_SKIP_FSM_CHECK": false, "TOUCH_SKIP_LEGACY_CONFLICT_CHECK": false, "TOUCH_SUPPRESS_DEPRECATE_WARN": false, "TWAI_ENABLE_DEBUG_LOG": false, "TWAI_ISR_CACHE_SAFE": false, "TWAI_ISR_IN_IRAM": false, "TWAI_SKIP_LEGACY_CONFLICT_CHECK": false, "UART_ISR_IN_IRAM": false, "UHCI_ENABLE_DEBUG_LOG": false, "UHCI_ISR_CACHE_SAFE": false, "UHCI_ISR_HANDLER_IN_IRAM": false, "ULP_COPROC_ENABLED": false, "UNITY_ENABLE_64BIT": false, "UNITY_ENABLE_BACKTRACE_ON_FAIL": false, "UNITY_ENABLE_COLOR": false, "UNITY_ENABLE_DOUBLE": true, "UNITY_ENABLE_FIXTURE": false, "UNITY_ENABLE_FLOAT": true, "UNITY_ENABLE_IDF_TEST_RUNNER": true, "UNITY_TEST_ORDER_BY_FILE_PATH_AND_LINE": false, "USB_HOST_CONTROL_TRANSFER_MAX_SIZE": 256, "USB_HOST_DEBOUNCE_DELAY_MS": 250, "USB_HOST_ENABLE_ENUM_FILTER_CALLBACK": false, "USB_HOST_HUBS_SUPPORTED": false, "USB_HOST_HW_BUFFER_BIAS_BALANCED": true, "USB_HOST_HW_BUFFER_BIAS_IN": false, "USB_HOST_HW_BUFFER_BIAS_PERIODIC_OUT": false, "USB_HOST_RESET_HOLD_MS": 30, "USB_HOST_RESET_RECOVERY_MS": 30, "USB_HOST_SET_ADDR_RECOVERY_MS": 10, "USB_OTG_SUPPORTED": true, "USJ_ENABLE_USB_SERIAL_JTAG": true, "VFS_INITIALIZE_DEV_NULL": true, "VFS_MAX_COUNT": 8, "VFS_SELECT_IN_RAM": false, "VFS_SEMIHOSTFS_MAX_MOUNT_POINTS": 1, "VFS_SUPPORT_DIR": true, "VFS_SUPPORT_IO": true, "VFS_SUPPORT_SELECT": true, "VFS_SUPPORT_TERMIOS": true, "VFS_SUPPRESS_SELECT_DEBUG_OUTPUT": true, "WIFI_PROV_AUTOSTOP_TIMEOUT": 30, "WIFI_PROV_SCAN_MAX_ENTRIES": 16, "WIFI_PROV_STA_ALL_CHANNEL_SCAN": true, "WIFI_PROV_STA_FAST_SCAN": false, "WL_SECTOR_SIZE": 4096, "WL_SECTOR_SIZE_4096": true, "WL_SECTOR_SIZE_512": false, "WS_BUFFER_SIZE": 1024, "WS_DYNAMIC_BUFFER": false, "WS_TRANSPORT": true, "XTAL_FREQ": 40, "XTAL_FREQ_40": true}