#ifndef M_DEVICE_INFO_DATA_H
#define M_DEVICE_INFO_DATA_H

/*
 * ================================================================
 * 设备信息数据模型 - 跨平台统一版本
 * ================================================================
 *
 * 本文件定义了ESP32和Flutter项目共用的数据结构，确保两个平台的数据结构一致性。
 *
 * 平台标注说明：
 * [共用]        - ESP32和Flutter都使用的字段
 * [ESP32专用]   - 仅ESP32项目使用的字段
 * [Flutter专用] - 仅Flutter项目使用的字段
 *
 * 设计原则：
 * 1. 保持数据结构的完整性和一致性
 * 2. 允许适度的冗余以确保兼容性
 * 3. 通过注释明确标识各字段的使用范围
 * 4. 提供跨平台的辅助函数
 *
 * 主要结构：
 * - SensorCommand: 传感器命令结构体
 * - SensorData: 传感器数据结构体（核心）
 * - WaterQualityParameter: 水质参数类
 *
 * ================================================================
 */

#ifdef ESP_PLATFORM
#include "m_serial_compat.h"
#else
#include <Arduino.h>
#endif
#include "m_globalDefines.h"

// 传感器参数类型枚举已在global_defines.h中定义

// 别名定义，让旧代码可以兼容新的枚举类型
// using ParameterType = SensorParameterType; // 暂时注释掉，避免编译冲突

// 为了兼容，定义一些旧枚举值，但不在switch语句中使用
const SensorParameterType calcium_compat = calcium;
const SensorParameterType carbonate_compat = carbonate;
const SensorParameterType potassium_compat = potassium;
const SensorParameterType magnesium_compat = magnesium;
const SensorParameterType ammonia_compat = ammonia;
const SensorParameterType nitrite_compat = nitrite;
const SensorParameterType nitrate_compat = nitrate;
const SensorParameterType salinity_compat = conductivity;

// ==================== ESP32-P4专用辅助函数 ====================

// ESP32-P4专用函数声明
const char* getParameterChineseName(SensorParameterType type);
const char* getParameterDefaultUnit(SensorParameterType type);
const char* getSensorName(SensorParameterType type);
String getParameterTypeName(SensorParameterType type);
bool needsTemperatureCompensation(SensorParameterType type);

// [共用] 判断是否需要缓冲服务
bool isBufferingNeeded(const String& tradeName);

// 传感器命令结构体 - 跨平台统一模型
struct SensorCommand {
  String name;                          // [共用] 命令名称/说明
  uint8_t command[MAX_COMMAND_LENGTH];  // [共用] 命令字节序列
  uint8_t commandLength;                // [共用] 命令长度
  String description;                   // [共用] 命令描述

  // 构造函数
  SensorCommand() : commandLength(0) {}

  SensorCommand(const String& cmdName, const uint8_t* cmdBytes, uint8_t length, const String& desc = "")
    : name(cmdName), commandLength(length), description(desc) {
    if (length <= MAX_COMMAND_LENGTH) {
      memcpy(command, cmdBytes, length);
    }
  }
};

// 传感器数据结构体 - 统一模型（包含所有平台字段）
struct SensorData {
  // ==================== 核心标识字段 ====================
  String id;                           // [共用] 唯一标识符
  String uuid;                         // [共用] 设备UUID
  const char* name;                    // [共用] 传感器名称
  const char* tradeName;               // [共用] 型号名称/商品名称

  // ==================== 参数配置字段 ====================
  SensorParameterType parameterType;   // [共用] 参数类型(pH,ORP等)
  uint8_t address;                     // [共用] 从站地址 (1-247)
  float value;                         // [共用] 当前读数
  const char* unit;                    // [共用] 单位
  float ratio;                         // [共用] 比例值（用于溶解氧等传感器）

  // ==================== 温度补偿字段 ====================
  bool hasTemperature;                 // [共用] 是否有温度传感
  float manualTemp;                    // [共用] 手动温度补偿值
  bool autoTempComp;                   // [共用] 是否自动温度补偿

  // ==================== 时间和连接状态字段 ====================
  uint32_t updatedAt;                  // [共用] 最近更新时间(秒)
  uint32_t createdAt;                  // [Flutter专用] 创建时间(秒)
  bool isConnected;                    // [共用] 是否连接正常

  // ==================== 通信和命令字段 ====================
  int registerCount;                   // [共用] 需要读取的寄存器数量
  SensorCommand commands[MAX_COMMANDS]; // [共用] 命令列表
  uint8_t commandCount;                // [共用] 命令数量

  // ==================== 网络连接字段 ====================
  const char* serverIp;                // [共用] Modbus服务器IP (ESP32中对应tcp_modbus_serverIp)
  int fixedPort;                       // [共用] Modbus服务器端口 (ESP32中对应tcp_modbus_fixedPort)
  int serverBasePort;                  // [Flutter专用] 串口服务器基础端口号
  int serialChannel;                   // [Flutter专用] 串口通道号

  // ==================== 功能配置字段 ====================
  bool useBufferedAverage;             // [共用] 是否使用缓存平均值 (true: 去极值平均值, false: 缓存最新值)
  uint32_t responseTimeMs;             // [共用] 响应时间(毫秒)
  int baudRate;                        // [Flutter专用] 波特率配置

  // ==================== 用户信息字段 ====================
  String userName;                     // [共用] 用户名，兼容旧代码
  String userId;                       // [共用] 用户ID，兼容旧代码

  // ==================== UI相关字段 ====================
  // 注意：ESP32中不使用这些字段，但保持结构一致性
  String iconName;                     // [Flutter专用] 图标名称 (代替IconData)
  String statusIconName;               // [Flutter专用] 状态图标名称
  uint32_t statusIconColor;            // [Flutter专用] 状态图标颜色 (ARGB格式)
  String status;                       // [Flutter专用] 状态文字
  bool enabled;                        // [Flutter专用] 是否启用
};

// ==================== 全局变量和函数声明 ====================

// [共用] 全局变量声明
extern SensorData devices[];
extern int deviceCount;

// [共用] 初始化设备命令
void initializeDeviceCommands();

// [共用] 初始化设备默认值
void initializeDeviceDefaults();

// ==================== 预定义传感器数据 ====================

// [共用] 预定义模拟传感器数据
// MAX_DEVICES_INIT 已在 m_globalDefines.h 中定义
extern SensorData devices[MAX_DEVICES_INIT];

// [共用] 设备数量
extern int deviceCount;

// ==================== 设备命令初始化函数 ====================

// [共用] 初始化设备命令
void initializeDeviceCommands();

// [共用] 初始化设备默认值
void initializeDeviceDefaults();

// 水质参数类 - 跨平台统一模型
class WaterQualityParameter {
public:
  SensorParameterType parameterType;  // [共用] 参数类型
  float minValue;                     // [共用] 最小值
  float maxValue;                     // [共用] 最大值
  String unit;                        // [共用] 单位

  WaterQualityParameter() {
    parameterType = ph; // 默认为pH
    minValue = 0.0;
    maxValue = 14.0;
    unit = "pH";
  }

  // 检查值是否在安全范围内
  bool isSafeRange(float value) {
    return value >= minValue && value <= maxValue;
  }

  // 获取温度补偿显示文本
  String getTemperatureCompensationText() {
    return "";
  }

  // 构造函数
  WaterQualityParameter(
    SensorParameterType type,
    float min,
    float max,
    String unitStr = ""
  ) {
    parameterType = type;
    minValue = min;
    maxValue = max;
    unit = unitStr.isEmpty() ? getParameterDefaultUnit(type) : unitStr;
  }
};

// ==================== 水质参数辅助函数 ====================

// [共用] 创建默认参数
void createDefaultParameters(WaterQualityParameter* parameters, int maxCount, int* count);

#endif // M_DEVICE_INFO_DATA_H
