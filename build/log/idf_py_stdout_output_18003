Command: /Users/<USER>/.espressif/python_env/idf5.5_py3.9_env/bin/python /Users/<USER>/esp/v5.5/esp-idf/tools/idf_monitor.py -p /dev/cu.wchusbserial5A7A0449951 -b 115200 --toolchain-prefix riscv32-esp-elf- --target esp32p4 --revision 1 --decode-panic backtrace /Users/<USER>/Documents/Arduino/libraries/ESP32-S3-Touch-LCD-4.3-Demo/Arduino/examples/esp32P4/build/HelloWorld.elf /Users/<USER>/Documents/Arduino/libraries/ESP32-S3-Touch-LCD-4.3-Demo/Arduino/examples/esp32P4/build/bootloader/bootloader.elf -m '/Users/<USER>/.espressif/python_env/idf5.5_py3.9_env/bin/python' '/Users/<USER>/esp/v5.5/esp-idf/tools/idf.py'
