#ifndef M_BLUETOOTH_COMPAT_H
#define M_BLUETOOTH_COMPAT_H

// ESP32-P4 蓝牙兼容层
// 包装ESP-IDF蓝牙API以提供Arduino BluetoothSerial兼容接口

// ESP32-P4暂时不支持蓝牙经典模式，使用空实现
#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "m_serial_compat.h"

#ifdef CONFIG_IDF_TARGET_ESP32P4

// 蓝牙SPP事件队列大小
#define BT_SPP_QUEUE_SIZE 10

// 蓝牙数据缓冲区大小
#define BT_BUFFER_SIZE 1024

class BluetoothSerial {
private:
    static BluetoothSerial* instance;
    bool initialized;
    bool connected;
    String device_name;
    String pin_code;
    uint32_t spp_handle;
    
    // 数据缓冲区
    uint8_t rx_buffer[BT_BUFFER_SIZE];
    size_t rx_buffer_head;
    size_t rx_buffer_tail;
    size_t rx_buffer_count;
    
    // FreeRTOS对象
    QueueHandle_t spp_event_queue;
    SemaphoreHandle_t buffer_mutex;
    TaskHandle_t bt_task_handle;
    
    // 静态回调函数
    static void gap_cb(int event, void *param);
    static void spp_cb(int event, void *param);
    static void bt_task(void *pvParameters);
    
    // 内部方法
    esp_err_t bt_init();
    void handle_spp_event(int event, void *param);
    void buffer_put(uint8_t c);
    int buffer_get();
    void update_connection_status(bool status);
    
public:
    BluetoothSerial();
    ~BluetoothSerial();
    
    // 基本方法
    bool begin(String localName = String(), bool isMaster = false);
    bool begin(const char* localName, bool isMaster = false);
    void end();
    
    // 连接状态
    bool hasClient();
    bool isConnected();
    bool isReady(bool checkMaster = false, int timeout = 0);
    
    // 数据传输
    int available();
    int peek();
    int read();
    size_t read(uint8_t *buffer, size_t size);
    String readString();
    String readStringUntil(char terminator);
    
    size_t write(uint8_t c);
    size_t write(const uint8_t *buffer, size_t size);
    size_t write(const char* str);
    size_t print(const String& s);
    size_t print(const char* str);
    size_t println(const String& s);
    size_t println(const char* str);
    
    void flush();
    
    // 配置方法
    bool setPin(const char* pin, int len);
    bool setPin(const String& pin);
    bool unpairDevice(uint8_t* address);
    bool connect(String remoteName);
    bool connect(uint8_t* address);
    bool disconnect();
    
    // 设备信息
    String readStringUntil(char terminator, unsigned long timeout);
    esp_err_t register_callback(void * callback);
    
    // 静态实例获取
    static BluetoothSerial& getInstance();
};

// 全局蓝牙对象
extern BluetoothSerial SerialBT;

#endif // CONFIG_IDF_TARGET_ESP32P4

#endif // M_BLUETOOTH_COMPAT_H
