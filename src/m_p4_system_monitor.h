/**
 * ESP32-P4 水质监测系统 - 系统监控模块
 * 
 * 提供系统运行状态监控、性能统计、错误检测等功能
 * 基于Arduino版本移植，保持功能一致性
 */

#ifndef M_P4_SYSTEM_MONITOR_H
#define M_P4_SYSTEM_MONITOR_H

#include "m_p4_system_config.h"
#include "esp_timer.h"
#include "esp_task_wdt.h"

// ==================== 监控配置 ====================
#define MONITOR_TASK_NAME         "system_monitor"
#define MONITOR_TASK_STACK_SIZE   STACK_SIZE_MEDIUM
#define MONITOR_TASK_PRIORITY     TASK_PRIORITY_CRITICAL

// 监控间隔定义
#define MONITOR_FAST_INTERVAL     1000   // 快速监控间隔(1秒)
#define MONITOR_NORMAL_INTERVAL   5000   // 普通监控间隔(5秒)
#define MONITOR_SLOW_INTERVAL     30000  // 慢速监控间隔(30秒)

// 阈值定义
#define MEMORY_WARNING_THRESHOLD  10240  // 内存警告阈值(10KB)
#define MEMORY_CRITICAL_THRESHOLD 5120   // 内存严重警告阈值(5KB)
#define CPU_WARNING_THRESHOLD     80     // CPU使用率警告阈值(80%)
#define TASK_STACK_WARNING_RATIO  0.1    // 任务栈警告比例(10%剩余)

// ==================== 任务监控结构 ====================
typedef struct {
    TaskHandle_t task_handle;    // 任务句柄
    char task_name[16];          // 任务名称
    uint32_t stack_size;         // 栈大小
    uint32_t stack_high_water;   // 栈高水位标记
    uint32_t run_time_counter;   // 运行时间计数器
    uint8_t cpu_usage_percent;   // CPU使用率
    bool is_running;             // 运行状态
    uint32_t last_heartbeat;     // 最后心跳时间
} task_monitor_info_t;

// ==================== 系统性能统计 ====================
typedef struct {
    // 内存统计
    uint32_t total_heap_size;        // 总堆内存大小
    uint32_t free_heap_size;         // 可用堆内存大小
    uint32_t min_free_heap_size;     // 历史最小可用堆内存
    uint32_t largest_free_block;     // 最大可用内存块
    
    // CPU统计
    uint32_t total_runtime;          // 总运行时间
    uint32_t idle_runtime;           // 空闲时间
    uint8_t cpu_usage_percent;       // CPU使用率
    
    // 任务统计
    uint8_t task_count;              // 任务数量
    task_monitor_info_t tasks[10];   // 任务监控信息
    
    // 系统统计
    uint32_t uptime_seconds;         // 系统运行时间
    uint32_t reset_count;            // 重启次数
    uint32_t error_count;            // 错误计数
    uint32_t warning_count;          // 警告计数
} system_performance_t;

// ==================== 全局变量声明 ====================
extern system_performance_t g_system_performance;
extern bool g_monitor_enabled;

// ==================== 函数声明 ====================

/**
 * 初始化系统监控
 * @return ESP_OK 成功，其他值表示失败
 */
esp_err_t system_monitor_init(void);

/**
 * 启动系统监控任务
 * @return ESP_OK 成功，其他值表示失败
 */
esp_err_t system_monitor_start(void);

/**
 * 停止系统监控任务
 * @return ESP_OK 成功，其他值表示失败
 */
esp_err_t system_monitor_stop(void);

/**
 * 系统监控任务主函数
 * @param pvParameters 任务参数
 */
void system_monitor_task(void *pvParameters);

/**
 * 更新系统性能统计
 */
void update_system_performance(void);

/**
 * 获取系统性能统计
 * @return 系统性能统计结构
 */
system_performance_t get_system_performance(void);

/**
 * 监控任务栈使用情况
 */
void monitor_task_stacks(void);

/**
 * 监控内存使用情况
 */
void monitor_memory_usage(void);

/**
 * 监控CPU使用情况
 */
void monitor_cpu_usage(void);

/**
 * 检查系统健康状态
 * @return true 系统健康，false 系统异常
 */
bool check_system_health(void);

/**
 * 打印系统状态报告
 */
void print_system_status_report(void);

/**
 * 打印任务运行时统计
 */
void print_task_runtime_stats(void);

/**
 * 打印内存使用统计
 */
void print_memory_stats(void);

/**
 * 注册任务监控
 * @param task_handle 任务句柄
 * @param task_name 任务名称
 * @param stack_size 栈大小
 * @return ESP_OK 成功，其他值表示失败
 */
esp_err_t register_task_monitor(TaskHandle_t task_handle, const char* task_name, uint32_t stack_size);

/**
 * 任务心跳更新
 * @param task_handle 任务句柄
 */
void task_heartbeat_update(TaskHandle_t task_handle);

/**
 * 检查任务心跳
 * @return 异常任务数量
 */
uint8_t check_task_heartbeats(void);

/**
 * 系统错误记录
 * @param error_msg 错误消息
 * @param error_code 错误代码
 */
void log_system_error(const char* error_msg, esp_err_t error_code);

/**
 * 系统警告记录
 * @param warning_msg 警告消息
 */
void log_system_warning(const char* warning_msg);

/**
 * 获取格式化的系统状态字符串
 * @param buffer 输出缓冲区
 * @param buffer_size 缓冲区大小
 * @return 实际写入的字符数
 */
int get_system_status_string(char* buffer, size_t buffer_size);

/**
 * 重置系统统计
 */
void reset_system_stats(void);

#endif // M_P4_SYSTEM_MONITOR_H
