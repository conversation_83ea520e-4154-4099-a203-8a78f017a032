#ifndef M_P4_HARDWARE_CONFIG_H
#define M_P4_HARDWARE_CONFIG_H

#ifdef CONFIG_IDF_TARGET_ESP32P4
#include "m_serial_compat.h"
#else
#include <Arduino.h>
#endif

// ==================== ESP32-P4 硬件配置 ====================

// 系统配置
#define SYSTEM_CPU_FREQ_MHZ         360  // 当前最高支持360MHz
#define SYSTEM_XTAL_FREQ_MHZ        40   // 晶振频率

// ==================== 串口配置 ====================
#define UART0_TX_PIN                43   // USB-UART
#define UART0_RX_PIN                44
#define UART1_TX_PIN                17   // 用户串口1
#define UART1_RX_PIN                18
#define UART2_TX_PIN                20   // 用户串口2 (可用于RS485) - 与RS485配置统一
#define UART2_RX_PIN                21   // 与RS485配置统一

// ==================== I2C配置 ====================
#define I2C0_SDA_PIN                7    // 默认I2C
#define I2C0_SCL_PIN                8
#define I2C1_SDA_PIN                15   // 备用I2C
#define I2C1_SCL_PIN                16

// ==================== SPI配置 ====================
#define SPI2_MOSI_PIN               11
#define SPI2_MISO_PIN               13
#define SPI2_CLK_PIN                12
#define SPI2_CS_PIN                 10

// ==================== 以太网配置 (RMII接口) ====================
#ifndef ETH_PHY_TYPE
#define ETH_PHY_TYPE                ETH_PHY_IP101
#endif
#ifndef ETH_PHY_ADDR
#define ETH_PHY_ADDR                1
#endif
#define ETH_PHY_MDC_PIN             31   // 管理时钟
#define ETH_PHY_MDIO_PIN            52   // 管理数据
#define ETH_PHY_RESET_PIN           51   // PHY复位
#define ETH_PHY_POWER_PIN           -1   // 无电源控制引脚

// RMII接口引脚
#define ETH_RMII_TX_EN_PIN          49   // 发送使能
#define ETH_RMII_TXD0_PIN           34   // 发送数据0
#define ETH_RMII_TXD1_PIN           35   // 发送数据1
#define ETH_RMII_RXD0_PIN           29   // 接收数据0
#define ETH_RMII_RXD1_PIN           30   // 接收数据1
#define ETH_RMII_CRS_DV_PIN         28   // 载波检测/数据有效
#define ETH_RMII_REF_CLK_PIN        50   // 参考时钟

// ==================== WiFi配置 (通过ESP32-C6) ====================
// WiFi通过SDIO接口连接ESP32-C6，无需额外引脚配置
#define WIFI_ENABLE                 true
#define WIFI_SDIO_INTERFACE         true

// ==================== 音频配置 (I2S + ES8311) ====================
#define I2S_NUM                     I2S_NUM_0
#define I2S_MCLK_PIN                13   // 主时钟
#define I2S_SCLK_PIN                12   // 串行时钟
#define I2S_LRCK_PIN                10   // 左右声道时钟
#define I2S_DOUT_PIN                11   // 数据输出
#define I2S_DIN_PIN                 9    // 数据输入
#define AUDIO_PA_CTRL_PIN           53   // 功放控制引脚

// ES8311 Codec配置
#define ES8311_I2C_ADDR             0x18
#define ES8311_I2C_PORT             I2C_NUM_0

// ==================== SDMMC配置 ====================
#define SDMMC_SLOT                  SDMMC_HOST_SLOT_1
#define SDMMC_CLK_PIN               43   // 时钟
#define SDMMC_CMD_PIN               44   // 命令
#define SDMMC_D0_PIN                39   // 数据0
#define SDMMC_D1_PIN                40   // 数据1
#define SDMMC_D2_PIN                41   // 数据2
#define SDMMC_D3_PIN                42   // 数据3
#define SDMMC_CD_PIN                -1   // 卡检测 (无)
#define SDMMC_WP_PIN                -1   // 写保护 (无)

// ==================== 显示配置 (MIPI-DSI) ====================
#define DISPLAY_MIPI_DSI            true
#define DISPLAY_WIDTH               1024
#define DISPLAY_HEIGHT              600
#define DISPLAY_LANES               2    // 2-lane MIPI

// ==================== 摄像头配置 (MIPI-CSI) ====================
#define CAMERA_MIPI_CSI             true
#define CAMERA_I2C_SDA_PIN          I2C0_SDA_PIN
#define CAMERA_I2C_SCL_PIN          I2C0_SCL_PIN

// ==================== GPIO分配 ====================
// 传感器相关GPIO (根据实际连接调整)
#define SENSOR_POWER_PIN            21   // 传感器电源控制
#define SENSOR_RS485_DE_PIN         22   // RS485方向控制
#define SENSOR_RS485_RE_PIN         23   // RS485接收使能

// LED指示灯
#define STATUS_LED_PIN              54   // 状态LED
#define ERROR_LED_PIN               55   // 错误LED
#define NETWORK_LED_PIN             56   // 网络LED

// 按键
#define USER_BUTTON_PIN             57   // 用户按键
#define RESET_BUTTON_PIN            0    // 复位按键 (BOOT)

// ==================== 电源管理 ====================
#define POWER_3V3_CTRL_PIN          -1   // 3.3V控制 (无)
#define POWER_5V_CTRL_PIN           -1   // 5V控制 (无)
#define BATTERY_ADC_PIN             -1   // 电池电压检测 (无)

// ==================== 调试配置 ====================
#define DEBUG_UART_NUM              UART_NUM_0
#define DEBUG_UART_BAUD             115200

// ==================== 任务配置 ====================
// 任务栈大小 (ESP32-P4有更多内存，可以适当增加)
#define SENSOR_TASK_STACK_SIZE      8192   // 增加到8KB
#define WIFI_TASK_STACK_SIZE        8192
#define BLUETOOTH_TASK_STACK_SIZE   6144
#define MONITOR_TASK_STACK_SIZE     4096
#define DATA_TASK_STACK_SIZE        6144

// 任务优先级
#define SENSOR_TASK_PRIORITY        5
#define WIFI_TASK_PRIORITY          4
#define BLUETOOTH_TASK_PRIORITY     3
#define MONITOR_TASK_PRIORITY       2
#define DATA_TASK_PRIORITY          4

// 任务核心分配 (ESP32-P4双核)
#define SENSOR_TASK_CORE            1    // 数据采集在核心1
#define WIFI_TASK_CORE              0    // 通信在核心0
#define BLUETOOTH_TASK_CORE         0
#define MONITOR_TASK_CORE           0
#define DATA_TASK_CORE              1

// ==================== 内存配置 ====================
// ESP32-P4有更多内存资源
#define SENSOR_DATA_BUFFER_SIZE     256  // 传感器数据缓冲区
#define WIFI_BUFFER_SIZE            2048 // WiFi缓冲区
#define LOG_BUFFER_SIZE             1024 // 日志缓冲区

// ==================== 定时器配置 ====================
#define SENSOR_READ_INTERVAL_MS     5000  // 传感器读取间隔
#define STATUS_REPORT_INTERVAL_MS   30000 // 状态报告间隔
#define WATCHDOG_TIMEOUT_MS         60000 // 看门狗超时

// ==================== 网络配置 ====================
#define DEFAULT_ETH_ENABLE          true  // 默认启用以太网
#define DEFAULT_WIFI_ENABLE         true  // 默认启用WiFi
#define DEFAULT_BLUETOOTH_ENABLE    false // 默认禁用蓝牙

// ==================== 功能特性开关 ====================
#define FEATURE_ETHERNET            1     // 以太网功能
#define FEATURE_WIFI                1     // WiFi功能
#define FEATURE_BLUETOOTH           0     // 蓝牙功能 (暂时禁用)
#define FEATURE_AUDIO               1     // 音频功能
#define FEATURE_SDCARD              1     // SD卡功能
#define FEATURE_DISPLAY             0     // 显示功能 (暂时禁用)
#define FEATURE_CAMERA              0     // 摄像头功能 (暂时禁用)
#define FEATURE_CRASH_LOGGER        1     // 崩溃日志功能

// ==================== 兼容性定义 ====================
// 为了保持与原有代码的兼容性
#define CONFIG_IDF_TARGET_ESP32P4   1
#define ESP32P4                     1

// 芯片特性
#ifndef SOC_UART_NUM
#define SOC_UART_NUM                5     // UART数量
#endif
#ifndef SOC_I2C_NUM
#define SOC_I2C_NUM                 2     // I2C数量
#endif
#define SOC_SPI_PERIPH_NUM          3     // SPI数量
#define SOC_TIMER_GROUP_TOTAL_NUM   2     // 定时器组数量

#endif // M_P4_HARDWARE_CONFIG_H
