/**
 * ESP32-P4 水质监测系统 - RS485通信模块实现
 */

#include "m_p4_rs485.h"
#include "m_p4_rs485_expansion.h"
#include "esp_log.h"
#include "esp_timer.h"
#include <sys/time.h>

// 统一日志输出函数（带时间戳）
static void log_with_timestamp(const char* level, const char* format, ...) {
    struct timeval tv;
    gettimeofday(&tv, NULL);
    struct tm* timeinfo = localtime(&tv.tv_sec);

    printf("[%02d:%02d:%02d.%03ld] %s: ",
           timeinfo->tm_hour, timeinfo->tm_min, timeinfo->tm_sec,
           tv.tv_usec / 1000, level);

    va_list args;
    va_start(args, format);
    vprintf(format, args);
    va_end(args);
    printf("\n");
}

// ==================== 全局变量定义 ====================
rs485_stats_t g_rs485_stats = {0};
SemaphoreHandle_t g_rs485_mutex = NULL;
static bool rs485_initialized = false;
static uint32_t rs485_timeout_ms = MODBUS_TIMEOUT_MS;

// ==================== 私有函数声明 ====================
static esp_err_t rs485_uart_init(void);
static modbus_error_t rs485_send_request(const uint8_t* request, uint16_t length);
static modbus_error_t rs485_receive_response(uint8_t* response, uint16_t* length, uint32_t timeout_ms);
static uint16_t rs485_build_request(const modbus_request_t* request, uint8_t* buffer);
static modbus_error_t rs485_parse_response(const uint8_t* buffer, uint16_t length, modbus_response_t* response);
static void rs485_update_stats(modbus_error_t error);

// ==================== 公共函数实现 ====================

esp_err_t rs485_init(void) {
    log_with_timestamp("RS485", "初始化RS485通信模块（使用TTL转RS485扩展板）...");

    if (rs485_initialized) {
        log_with_timestamp("RS485", "⚠️ RS485已经初始化，检查UART驱动状态...");
        // 检查UART驱动是否真的安装了
        if (uart_is_driver_installed(RS485_UART_NUM)) {
            log_with_timestamp("RS485", "✅ UART%d驱动正常，跳过重新初始化", RS485_UART_NUM);
            return ESP_OK;
        } else {
            log_with_timestamp("RS485", "❌ UART%d驱动未安装，需要重新初始化", RS485_UART_NUM);
            // 重置初始化标志，强制重新初始化
            rs485_initialized = false;
        }
    }

    // 创建互斥锁
    g_rs485_mutex = xSemaphoreCreateMutex();
    if (g_rs485_mutex == NULL) {
        log_with_timestamp("RS485", "❌ 创建RS485互斥锁失败");
        return ESP_FAIL;
    }

    // 初始化TTL转RS485扩展板
    rs485_expansion_config_t expansion_config;
    rs485_expansion_get_default_config(&expansion_config);

    esp_err_t ret = rs485_expansion_init(&expansion_config);
    if (ret != ESP_OK) {
        log_with_timestamp("RS485", "❌ TTL转RS485扩展板初始化失败 (错误码: %s)", esp_err_to_name(ret));
        vSemaphoreDelete(g_rs485_mutex);
        g_rs485_mutex = NULL;
        return ret;
    }

    // 初始化UART（现在通过扩展板配置完成）
    ret = rs485_uart_init();
    if (ret != ESP_OK) {
        log_with_timestamp("RS485", "❌ RS485 UART初始化失败 (错误码: %s)", esp_err_to_name(ret));
        rs485_expansion_deinit();
        vSemaphoreDelete(g_rs485_mutex);
        g_rs485_mutex = NULL;
        return ret;
    }

    // 初始化统计信息
    memset(&g_rs485_stats, 0, sizeof(rs485_stats_t));
    
    rs485_initialized = true;
    log_with_timestamp("RS485", "✅ RS485通信模块初始化完成");
    log_with_timestamp("RS485", "   波特率: %d", RS485_BAUDRATE);
    log_with_timestamp("RS485", "   RX引脚: GPIO%d (40PIN右侧第8针)", RS485_RX_PIN);
    log_with_timestamp("RS485", "   TX引脚: GPIO%d (40PIN右侧第6针)", RS485_TX_PIN);
    log_with_timestamp("RS485", "   RTS引脚: %d (扩展板自动换向)", RS485_RTS_PIN);
    log_with_timestamp("RS485", "   供电方式: 3.3V (40PIN右侧第1针)");
    
    return ESP_OK;
}

esp_err_t rs485_deinit(void) {
    if (!rs485_initialized) {
        return ESP_OK;
    }
    
    // 删除UART驱动
    uart_driver_delete(RS485_UART_NUM);
    
    // 删除互斥锁
    if (g_rs485_mutex != NULL) {
        vSemaphoreDelete(g_rs485_mutex);
        g_rs485_mutex = NULL;
    }
    
    rs485_initialized = false;
    ESP_LOGI(LOG_TAG_SENSOR, "RS485通信模块已反初始化");
    
    return ESP_OK;
}

modbus_error_t rs485_modbus_transaction(const modbus_request_t* request, 
                                       modbus_response_t* response, 
                                       uint32_t timeout_ms) {
    if (!rs485_initialized || request == NULL || response == NULL) {
        return MODBUS_ERROR_INVALID_PARAM;
    }
    
    // 获取互斥锁
    if (xSemaphoreTake(g_rs485_mutex, pdMS_TO_TICKS(timeout_ms)) != pdTRUE) {
        ESP_LOGW(LOG_TAG_SENSOR, "获取RS485互斥锁超时");
        return MODBUS_ERROR_TIMEOUT;
    }
    
    modbus_error_t result = MODBUS_OK;
    uint8_t request_buffer[RS485_BUFFER_SIZE];
    uint8_t response_buffer[RS485_BUFFER_SIZE];
    uint16_t request_length, response_length;
    
    // 构建请求
    request_length = rs485_build_request(request, request_buffer);
    if (request_length == 0) {
        result = MODBUS_ERROR_INVALID_PARAM;
        goto cleanup;
    }
    
    // 清空接收缓冲区
    rs485_flush_rx_buffer();
    
    // 发送请求
    result = rs485_send_request(request_buffer, request_length);
    if (result != MODBUS_OK) {
        goto cleanup;
    }
    
    // 接收响应
    result = rs485_receive_response(response_buffer, &response_length, timeout_ms);
    if (result != MODBUS_OK) {
        goto cleanup;
    }
    
    // 解析响应
    result = rs485_parse_response(response_buffer, response_length, response);

    // 验证响应的完整性
    if (result == MODBUS_OK) {
        // 验证从站地址
        if (response->slave_addr != request->slave_addr) {
            log_with_timestamp("RS485", "❌ 从站地址不匹配: 期望=0x%02X, 实际=0x%02X",
                             request->slave_addr, response->slave_addr);
            result = MODBUS_ERROR_INVALID_RESPONSE;
        }
        // 验证功能码
        else if (response->function_code != request->function_code) {
            log_with_timestamp("RS485", "❌ 功能码不匹配: 期望=0x%02X, 实际=0x%02X",
                             request->function_code, response->function_code);
            result = MODBUS_ERROR_INVALID_RESPONSE;
        }
        // 验证数据长度（对于读取寄存器命令）
        else if ((request->function_code == MODBUS_READ_HOLDING_REGISTERS ||
                  request->function_code == MODBUS_READ_INPUT_REGISTERS) &&
                 response->data_length != request->quantity * 2) {
            log_with_timestamp("RS485", "❌ 数据长度不匹配: 期望=%d字节, 实际=%d字节",
                             request->quantity * 2, response->data_length);
            result = MODBUS_ERROR_INVALID_RESPONSE;
        }

        if (result == MODBUS_OK) {
            log_with_timestamp("RS485", "✅ Modbus响应验证通过");
        }
    }
    
cleanup:
    // 更新统计信息
    rs485_update_stats(result);
    
    // 释放互斥锁
    xSemaphoreGive(g_rs485_mutex);
    
    return result;
}

modbus_error_t rs485_read_holding_registers(uint8_t slave_addr, 
                                           uint16_t start_addr, 
                                           uint16_t quantity, 
                                           uint16_t* data, 
                                           uint32_t timeout_ms) {
    if (data == NULL || quantity == 0) {
        return MODBUS_ERROR_INVALID_PARAM;
    }
    
    modbus_request_t request = {
        .slave_addr = slave_addr,
        .function_code = MODBUS_READ_HOLDING_REGISTERS,
        .start_addr = start_addr,
        .quantity = quantity,
        .data = NULL,
        .data_length = 0
    };
    
    modbus_response_t response = {0};
    modbus_error_t result = rs485_modbus_transaction(&request, &response, timeout_ms);
    
    if (result == MODBUS_OK && response.data != NULL) {
        // 将响应数据转换为16位寄存器值
        for (int i = 0; i < quantity && i * 2 < response.data_length; i++) {
            data[i] = (response.data[i * 2] << 8) | response.data[i * 2 + 1];
        }
    }
    
    return result;
}

modbus_error_t rs485_read_input_registers(uint8_t slave_addr, 
                                         uint16_t start_addr, 
                                         uint16_t quantity, 
                                         uint16_t* data, 
                                         uint32_t timeout_ms) {
    if (data == NULL || quantity == 0) {
        return MODBUS_ERROR_INVALID_PARAM;
    }
    
    modbus_request_t request = {
        .slave_addr = slave_addr,
        .function_code = MODBUS_READ_INPUT_REGISTERS,
        .start_addr = start_addr,
        .quantity = quantity,
        .data = NULL,
        .data_length = 0
    };
    
    modbus_response_t response = {0};
    modbus_error_t result = rs485_modbus_transaction(&request, &response, timeout_ms);
    
    if (result == MODBUS_OK && response.data != NULL) {
        // 将响应数据转换为16位寄存器值
        for (int i = 0; i < quantity && i * 2 < response.data_length; i++) {
            data[i] = (response.data[i * 2] << 8) | response.data[i * 2 + 1];
        }
    }
    
    return result;
}

modbus_error_t rs485_write_single_register(uint8_t slave_addr, 
                                          uint16_t register_addr, 
                                          uint16_t value, 
                                          uint32_t timeout_ms) {
    uint8_t write_data[2] = {(uint8_t)((value >> 8) & 0xFF), (uint8_t)(value & 0xFF)};
    
    modbus_request_t request = {
        .slave_addr = slave_addr,
        .function_code = MODBUS_WRITE_SINGLE_REGISTER,
        .start_addr = register_addr,
        .quantity = 1,
        .data = write_data,
        .data_length = 2
    };
    
    modbus_response_t response;
    memset(&response, 0, sizeof(modbus_response_t));
    return rs485_modbus_transaction(&request, &response, timeout_ms);
}

uint16_t rs485_calculate_crc16(const uint8_t* buffer, uint16_t length) {
    uint16_t crc = 0xFFFF;
    
    for (uint16_t pos = 0; pos < length; pos++) {
        crc ^= (uint16_t)buffer[pos];
        
        for (int i = 8; i != 0; i--) {
            if ((crc & 0x0001) != 0) {
                crc >>= 1;
                crc ^= 0xA001;
            } else {
                crc >>= 1;
            }
        }
    }
    
    return crc;
}

void rs485_flush_rx_buffer(void) {
    if (rs485_initialized) {
        uart_flush_input(RS485_UART_NUM);
    }
}

rs485_stats_t rs485_get_stats(void) {
    return g_rs485_stats;
}

void rs485_reset_stats(void) {
    if (xSemaphoreTake(g_rs485_mutex, pdMS_TO_TICKS(1000)) == pdTRUE) {
        memset(&g_rs485_stats, 0, sizeof(rs485_stats_t));
        xSemaphoreGive(g_rs485_mutex);
    }
}

void rs485_print_stats(void) {
    ESP_LOGI(LOG_TAG_SENSOR, "=== RS485通信统计 ===");
    ESP_LOGI(LOG_TAG_SENSOR, "总请求数: %lu", g_rs485_stats.total_requests);
    ESP_LOGI(LOG_TAG_SENSOR, "成功请求数: %lu", g_rs485_stats.successful_requests);
    ESP_LOGI(LOG_TAG_SENSOR, "成功率: %.1f%%", 
             g_rs485_stats.total_requests > 0 ? 
             (100.0 * g_rs485_stats.successful_requests / g_rs485_stats.total_requests) : 0.0);
    ESP_LOGI(LOG_TAG_SENSOR, "超时错误: %lu", g_rs485_stats.timeout_errors);
    ESP_LOGI(LOG_TAG_SENSOR, "CRC错误: %lu", g_rs485_stats.crc_errors);
    ESP_LOGI(LOG_TAG_SENSOR, "异常错误: %lu", g_rs485_stats.exception_errors);
    ESP_LOGI(LOG_TAG_SENSOR, "UART错误: %lu", g_rs485_stats.uart_errors);
    ESP_LOGI(LOG_TAG_SENSOR, "最后错误: %s", rs485_get_error_string(g_rs485_stats.last_error));
    ESP_LOGI(LOG_TAG_SENSOR, "==================");
}

bool rs485_is_healthy(void) {
    if (!rs485_initialized) {
        return false;
    }
    
    // 如果总请求数少于10，认为是健康的（刚启动）
    if (g_rs485_stats.total_requests < 10) {
        return true;
    }
    
    // 计算成功率
    float success_rate = (float)g_rs485_stats.successful_requests / g_rs485_stats.total_requests;
    
    // 成功率大于80%认为是健康的
    return success_rate > 0.8f;
}

void rs485_set_timeout(uint32_t timeout_ms) {
    rs485_timeout_ms = timeout_ms;
}

const char* rs485_get_error_string(modbus_error_t error) {
    switch (error) {
        case MODBUS_OK: return "成功";
        case MODBUS_ERROR_TIMEOUT: return "超时";
        case MODBUS_ERROR_CRC: return "CRC错误";
        case MODBUS_ERROR_EXCEPTION: return "异常响应";
        case MODBUS_ERROR_INVALID_RESPONSE: return "无效响应";
        case MODBUS_ERROR_UART_FAIL: return "UART错误";
        case MODBUS_ERROR_INVALID_PARAM: return "无效参数";
        default: return "未知错误";
    }
}

// ==================== 私有函数实现 ====================

static esp_err_t rs485_uart_init(void) {
    ESP_LOGI(LOG_TAG_SENSOR, "配置UART用于TTL转RS485扩展板...");

    // 注意：扩展板已经在rs485_expansion_init()中配置了UART
    // 这里只需要验证配置是否正确

    // 检查扩展板是否已初始化
    if (!rs485_expansion_is_healthy()) {
        ESP_LOGW(LOG_TAG_SENSOR, "扩展板状态异常，尝试测试通信");
        esp_err_t ret = rs485_expansion_test_communication();
        if (ret != ESP_OK) {
            ESP_LOGE(LOG_TAG_SENSOR, "扩展板通信测试失败");
            return ret;
        }
    }

    // 打印扩展板信息
    rs485_expansion_print_info();

    ESP_LOGI(LOG_TAG_SENSOR, "✅ UART配置完成（通过TTL转RS485扩展板）");
    ESP_LOGI(LOG_TAG_SENSOR, "   特性: 自动换向, 电气隔离, 高速传输");
    ESP_LOGI(LOG_TAG_SENSOR, "   无需RTS控制引脚");
    ESP_LOGI(LOG_TAG_SENSOR, "   注意: 扩展板自动处理方向控制，无需手动设置RS485模式");

    return ESP_OK;
}

static modbus_error_t rs485_send_request(const uint8_t* request, uint16_t length) {
    if (request == NULL || length == 0) {
        return MODBUS_ERROR_INVALID_PARAM;
    }

    // 调试：打印发送的数据
    char hex_str[256] = {0};
    for (int i = 0; i < length && i < 32; i++) {
        char temp[8];
        snprintf(temp, sizeof(temp), "%02X ", request[i]);
        strcat(hex_str, temp);
    }
    log_with_timestamp("RS485", "📤 准备发送数据: %s(%d字节)", hex_str, length);

    // 🎯 ESP32-P4最佳UART配置确认
    log_with_timestamp("RS485", "🎯 ESP32-P4最佳UART配置:");
    log_with_timestamp("RS485", "   UART端口: UART_NUM_%d (避开UART0/UART1)", RS485_UART_NUM);
    log_with_timestamp("RS485", "   TX引脚: GPIO%d (纯通用IO，无功能冲突)", RS485_TX_PIN);
    log_with_timestamp("RS485", "   RX引脚: GPIO%d (纯通用IO，无功能冲突)", RS485_RX_PIN);

    // 验证是否使用最佳GPIO配置
    if (RS485_TX_PIN == 20 && RS485_RX_PIN == 21) {
        log_with_timestamp("RS485", "✅ 使用ESP32-P4最佳GPIO配置：GPIO20/21");
    } else {
        log_with_timestamp("RS485", "⚠️  非最佳GPIO配置，推荐GPIO20/21");
    }

    // 检查UART驱动是否已安装
    if (!uart_is_driver_installed(RS485_UART_NUM)) {
        log_with_timestamp("RS485", "❌ UART%d驱动未安装！", RS485_UART_NUM);
        log_with_timestamp("RS485", "🔧 尝试重新初始化RS485系统...");

        // 尝试重新初始化
        esp_err_t init_ret = rs485_init();
        if (init_ret != ESP_OK) {
            log_with_timestamp("RS485", "❌ RS485系统重新初始化失败: %s", esp_err_to_name(init_ret));
            return MODBUS_ERROR_UART_FAIL;
        }

        // 再次检查
        if (!uart_is_driver_installed(RS485_UART_NUM)) {
            log_with_timestamp("RS485", "❌ UART%d驱动仍未安装，初始化失败", RS485_UART_NUM);
            return MODBUS_ERROR_UART_FAIL;
        }
        log_with_timestamp("RS485", "✅ UART%d驱动重新安装成功", RS485_UART_NUM);
    } else {
        log_with_timestamp("RS485", "✅ UART%d驱动已安装", RS485_UART_NUM);
    }

    // 清空TX缓冲区确保干净发送
    uart_flush(RS485_UART_NUM);

    // 发送数据并记录详细信息
    log_with_timestamp("RS485", "🚀 开始UART写入...");
    int bytes_written = uart_write_bytes(RS485_UART_NUM, request, length);
    log_with_timestamp("RS485", "📊 UART写入结果: %d/%d字节", bytes_written, length);

    if (bytes_written != length) {
        log_with_timestamp("RS485", "❌ UART写入失败，期望: %d字节, 实际: %d字节", length, bytes_written);
        log_with_timestamp("RS485", "   检查GPIO%d(TX)和GPIO%d(RX)连接", RS485_TX_PIN, RS485_RX_PIN);
        log_with_timestamp("RS485", "   ESP32-P4注意：避开UART0(GPIO37/38)和UART1(GPIO10/11)");
        log_with_timestamp("RS485", "   HVD75扩展板检查：");
        log_with_timestamp("RS485", "     - 3.3V供电是否正常");
        log_with_timestamp("RS485", "     - TX/RX引脚是否交叉连接正确");
        log_with_timestamp("RS485", "     - A+/B-是否连接到RS485设备");
        return MODBUS_ERROR_UART_FAIL;
    }

    log_with_timestamp("RS485", "✅ UART写入成功，等待发送完成...");

    // 等待发送完成
    esp_err_t ret = uart_wait_tx_done(RS485_UART_NUM, pdMS_TO_TICKS(1000));
    if (ret != ESP_OK) {
        log_with_timestamp("RS485", "❌ UART发送等待失败: %s", esp_err_to_name(ret));
        log_with_timestamp("RS485", "   可能原因: 扩展板供电异常或连接松动");
        return MODBUS_ERROR_UART_FAIL;
    }

    log_with_timestamp("RS485", "✅ 数据发送完成，已通过GPIO%d(最佳TX引脚)输出到RS485总线", RS485_TX_PIN);

    // 发送后短暂延时，让RS485总线稳定，特别是对于自动换向的扩展板
    vTaskDelay(pdMS_TO_TICKS(10));
    log_with_timestamp("RS485", "⏳ 发送后延时完成，RS485总线已稳定");

    return MODBUS_OK;
}

static modbus_error_t rs485_receive_response(uint8_t* response, uint16_t* length, uint32_t timeout_ms) {
    if (response == NULL || length == NULL) {
        return MODBUS_ERROR_INVALID_PARAM;
    }

    *length = 0;
    uint32_t start_time = esp_timer_get_time() / 1000;  // 转换为毫秒
    uint32_t last_receive_time = start_time;
    const uint32_t inter_frame_timeout = 50; // 帧间超时50ms

    log_with_timestamp("RS485", "🔍 开始接收响应，超时时间: %lu ms", timeout_ms);

    while ((esp_timer_get_time() / 1000 - start_time) < timeout_ms) {
        int bytes_available = uart_read_bytes(RS485_UART_NUM,
                                            response + *length,
                                            RS485_BUFFER_SIZE - *length,
                                            pdMS_TO_TICKS(10));

        if (bytes_available > 0) {
            *length += bytes_available;
            last_receive_time = esp_timer_get_time() / 1000;

            log_with_timestamp("RS485", "📥 接收到 %d 字节，总计 %d 字节", bytes_available, *length);

            // 如果接收到足够的数据，检查是否为完整帧
            if (*length >= 5) {  // 最小Modbus响应长度
                // 等待帧间间隔，确保没有更多数据
                uint32_t wait_start = esp_timer_get_time() / 1000;
                bool frame_complete = false;

                while ((esp_timer_get_time() / 1000 - wait_start) < inter_frame_timeout) {
                    int additional_bytes = uart_read_bytes(RS485_UART_NUM,
                                                         response + *length,
                                                         RS485_BUFFER_SIZE - *length,
                                                         pdMS_TO_TICKS(5));
                    if (additional_bytes > 0) {
                        *length += additional_bytes;
                        log_with_timestamp("RS485", "📥 追加接收 %d 字节，总计 %d 字节", additional_bytes, *length);
                        wait_start = esp_timer_get_time() / 1000; // 重置等待时间
                    } else {
                        frame_complete = true;
                        break;
                    }
                }

                if (frame_complete) {
                    log_with_timestamp("RS485", "✅ 帧接收完成，总长度: %d 字节", *length);
                    break;
                }
            }
        } else {
            // 如果已经接收到数据，检查是否超过帧间超时
            if (*length > 0 && (esp_timer_get_time() / 1000 - last_receive_time) > inter_frame_timeout) {
                log_with_timestamp("RS485", "⏰ 帧间超时，接收完成，总长度: %d 字节", *length);
                break;
            }
        }

        vTaskDelay(pdMS_TO_TICKS(1));  // 短暂延时
    }

    if (*length == 0) {
        log_with_timestamp("RS485", "❌ 接收超时，未收到任何数据");
        return MODBUS_ERROR_TIMEOUT;
    }

    // 调试：打印接收的数据
    char hex_str[256] = {0};
    for (int i = 0; i < *length && i < 32; i++) {
        char temp[8];
        snprintf(temp, sizeof(temp), "%02X ", response[i]);
        strcat(hex_str, temp);
    }
    log_with_timestamp("RS485", "📥 接收数据: %s(%d字节)", hex_str, *length);

    return MODBUS_OK;
}

static uint16_t rs485_build_request(const modbus_request_t* request, uint8_t* buffer) {
    if (request == NULL || buffer == NULL) {
        return 0;
    }

    uint16_t length = 0;

    // 从机地址
    buffer[length++] = request->slave_addr;

    // 功能码
    buffer[length++] = request->function_code;

    // 根据功能码构建请求
    switch (request->function_code) {
        case MODBUS_READ_HOLDING_REGISTERS:
        case MODBUS_READ_INPUT_REGISTERS:
            // 起始地址（高字节在前）
            buffer[length++] = (request->start_addr >> 8) & 0xFF;
            buffer[length++] = request->start_addr & 0xFF;
            // 寄存器数量（高字节在前）
            buffer[length++] = (request->quantity >> 8) & 0xFF;
            buffer[length++] = request->quantity & 0xFF;
            break;

        case MODBUS_WRITE_SINGLE_REGISTER:
            // 寄存器地址（高字节在前）
            buffer[length++] = (request->start_addr >> 8) & 0xFF;
            buffer[length++] = request->start_addr & 0xFF;
            // 寄存器值（从data中获取）
            if (request->data != NULL && request->data_length >= 2) {
                buffer[length++] = request->data[0];
                buffer[length++] = request->data[1];
            } else {
                return 0;  // 数据不足
            }
            break;

        default:
            ESP_LOGE(LOG_TAG_SENSOR, "不支持的Modbus功能码: 0x%02X", request->function_code);
            return 0;
    }

    // 计算并添加CRC16校验码
    uint16_t crc = rs485_calculate_crc16(buffer, length);
    buffer[length++] = crc & 0xFF;        // CRC低字节
    buffer[length++] = (crc >> 8) & 0xFF; // CRC高字节

    return length;
}

static modbus_error_t rs485_parse_response(const uint8_t* buffer, uint16_t length, modbus_response_t* response) {
    if (buffer == NULL || response == NULL || length < 5) {
        return MODBUS_ERROR_INVALID_RESPONSE;
    }

    // 验证CRC
    uint16_t received_crc = buffer[length - 2] | (buffer[length - 1] << 8);
    uint16_t calculated_crc = rs485_calculate_crc16(buffer, length - 2);

    if (received_crc != calculated_crc) {
        log_with_timestamp("RS485", "❌ CRC校验失败，接收: 0x%04X, 计算: 0x%04X", received_crc, calculated_crc);
        log_with_timestamp("RS485", "   可能原因: 通信干扰、波特率不匹配或扩展板故障");
        return MODBUS_ERROR_CRC;
    }

    // 解析响应
    response->slave_addr = buffer[0];
    response->function_code = buffer[1];

    // 检查是否为异常响应
    if (response->function_code & 0x80) {
        log_with_timestamp("RS485", "❌ Modbus异常响应，异常码: 0x%02X", buffer[2]);
        const char* exception_desc = "";
        switch(buffer[2]) {
            case 0x01: exception_desc = "非法功能码"; break;
            case 0x02: exception_desc = "非法数据地址"; break;
            case 0x03: exception_desc = "非法数据值"; break;
            case 0x04: exception_desc = "从站设备故障"; break;
            default: exception_desc = "未知异常"; break;
        }
        log_with_timestamp("RS485", "   异常描述: %s", exception_desc);
        return MODBUS_ERROR_EXCEPTION;
    }

    // 根据功能码解析数据
    switch (response->function_code) {
        case MODBUS_READ_HOLDING_REGISTERS:
        case MODBUS_READ_INPUT_REGISTERS:
            if (length < 5) {
                return MODBUS_ERROR_INVALID_RESPONSE;
            }
            response->byte_count = buffer[2];
            response->data = (uint8_t*)&buffer[3];
            response->data_length = response->byte_count;
            break;

        case MODBUS_WRITE_SINGLE_REGISTER:
            // 写单个寄存器的响应包含地址和值的回显
            if (length < 8) {
                return MODBUS_ERROR_INVALID_RESPONSE;
            }
            response->data = (uint8_t*)&buffer[2];
            response->data_length = 4;
            break;

        default:
            ESP_LOGE(LOG_TAG_SENSOR, "不支持的响应功能码: 0x%02X", response->function_code);
            return MODBUS_ERROR_INVALID_RESPONSE;
    }

    response->error = MODBUS_OK;
    return MODBUS_OK;
}

static void rs485_update_stats(modbus_error_t error) {
    g_rs485_stats.total_requests++;
    g_rs485_stats.last_error = error;
    g_rs485_stats.last_error_time = esp_timer_get_time() / 1000;

    switch (error) {
        case MODBUS_OK:
            g_rs485_stats.successful_requests++;
            break;
        case MODBUS_ERROR_TIMEOUT:
            g_rs485_stats.timeout_errors++;
            break;
        case MODBUS_ERROR_CRC:
            g_rs485_stats.crc_errors++;
            break;
        case MODBUS_ERROR_EXCEPTION:
            g_rs485_stats.exception_errors++;
            break;
        case MODBUS_ERROR_UART_FAIL:
            g_rs485_stats.uart_errors++;
            break;
        default:
            break;
    }
}
