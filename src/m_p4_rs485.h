/**
 * ESP32-P4 水质监测系统 - RS485通信模块
 * 
 * 基于Arduino版本的m_RS485.h移植到ESP-IDF平台
 * 提供Modbus RTU协议的RS485通信功能
 */

#ifndef M_P4_RS485_H
#define M_P4_RS485_H

#include "m_p4_system_config.h"
#include "driver/uart.h"
#include "driver/gpio.h"
#include <string.h>

// ==================== RS485配置 ====================
// 使用TTL转RS485扩展板（ICASH B0505XT-1WRS）配置
// 供电方案：3.3V供电（40PIN排针右侧第1针）
#define RS485_UART_NUM          UART_NUM_2      // 使用UART2 (与扩展板配置一致)

// 🎯 ESP32-P4最佳GPIO配置 (基于数据手册详细分析)
// ================================================================================
// 经过全面分析ESP32-P4数据手册，选择最优GPIO组合：
//
// ❌ 避开的GPIO：
// - GPIO37/38: UART0 (烧录串口，不可用)
// - GPIO10/11: UART1默认引脚 (避免冲突)
// - GPIO24/25: USB FS_PHY1 (USB串口/JTAG控制器)
// - GPIO26/27: USB FS_PHY2 (OTG_FS控制器)
// - GPIO14/15: UART功能占用
// - GPIO3/45/46: Strapping引脚 (影响启动)
// - GPIO4-19: ADC功能重叠 (虽然可用但不是最佳)
//
// ✅ 最佳选择：GPIO20/21 (用户指定)
// - TX=GPIO20: 纯通用IO，无任何功能冲突 ✅
// - RX=GPIO21: 纯通用IO，无任何功能冲突 ✅
// - 优势: 完全没有ADC、SPI、USB等功能重叠
// - 通过GPIO交换矩阵配置为UART2功能
// ================================================================================
#define RS485_TX_PIN            20              // ESP32-P4最佳TX引脚 (GPIO20 - 纯通用IO)
#define RS485_RX_PIN            21              // ESP32-P4最佳RX引脚 (GPIO21 - 纯通用IO)
#define RS485_RTS_PIN           -1              // 扩展板自动换向，无需RTS控制
#define RS485_BAUDRATE          9600            // 波特率（扩展板支持最高2Mbps）
#define RS485_BUFFER_SIZE       256             // 收发缓冲区大小
#define MODBUS_TIMEOUT_MS       1500            // Modbus超时时间(毫秒) - 与S3项目一致，支持42寄存器读取
#define MODBUS_MAX_RETRIES      3               // 最大重试次数

// ==================== 扩展板特性配置 ====================
#define RS485_MODULE_AUTO_DIRECTION     true    // 扩展板支持自动换向
#define RS485_MODULE_ISOLATION          true    // 扩展板支持电气隔离
#define RS485_MODULE_MAX_BAUDRATE       2000000 // 扩展板最大波特率2Mbps
#define RS485_MODULE_VOLTAGE_3V3        true    // 兼容3.3V电平
#define RS485_MODULE_VOLTAGE_5V         true    // 兼容5V电平

// ==================== Modbus功能码 ====================
typedef enum {
    MODBUS_READ_COILS = 0x01,
    MODBUS_READ_DISCRETE_INPUTS = 0x02,
    MODBUS_READ_HOLDING_REGISTERS = 0x03,
    MODBUS_READ_INPUT_REGISTERS = 0x04,
    MODBUS_WRITE_SINGLE_COIL = 0x05,
    MODBUS_WRITE_SINGLE_REGISTER = 0x06,
    MODBUS_WRITE_MULTIPLE_COILS = 0x0F,
    MODBUS_WRITE_MULTIPLE_REGISTERS = 0x10
} modbus_function_code_t;

// ==================== Modbus错误码 ====================
typedef enum {
    MODBUS_OK = 0,
    MODBUS_ERROR_TIMEOUT,
    MODBUS_ERROR_CRC,
    MODBUS_ERROR_EXCEPTION,
    MODBUS_ERROR_INVALID_RESPONSE,
    MODBUS_ERROR_UART_FAIL,
    MODBUS_ERROR_INVALID_PARAM
} modbus_error_t;

// ==================== Modbus请求结构 ====================
typedef struct {
    uint8_t slave_addr;         // 从机地址
    uint8_t function_code;      // 功能码
    uint16_t start_addr;        // 起始地址
    uint16_t quantity;          // 数量
    uint8_t* data;              // 数据指针（用于写操作）
    uint16_t data_length;       // 数据长度
} modbus_request_t;

// ==================== Modbus响应结构 ====================
typedef struct {
    uint8_t slave_addr;         // 从机地址
    uint8_t function_code;      // 功能码
    uint8_t byte_count;         // 字节数
    uint8_t* data;              // 响应数据
    uint16_t data_length;       // 数据长度
    modbus_error_t error;       // 错误码
} modbus_response_t;

// ==================== RS485统计信息 ====================
typedef struct {
    uint32_t total_requests;    // 总请求数
    uint32_t successful_requests; // 成功请求数
    uint32_t timeout_errors;    // 超时错误数
    uint32_t crc_errors;        // CRC错误数
    uint32_t exception_errors;  // 异常错误数
    uint32_t uart_errors;       // UART错误数
    uint32_t last_error_time;   // 最后错误时间
    modbus_error_t last_error;  // 最后错误码
} rs485_stats_t;

// ==================== 全局变量声明 ====================
extern rs485_stats_t g_rs485_stats;
extern SemaphoreHandle_t g_rs485_mutex;

// ==================== 函数声明 ====================

/**
 * 初始化RS485通信
 * @return ESP_OK 成功，其他值表示失败
 */
esp_err_t rs485_init(void);

/**
 * 反初始化RS485通信
 * @return ESP_OK 成功，其他值表示失败
 */
esp_err_t rs485_deinit(void);

/**
 * 发送Modbus请求并接收响应
 * @param request Modbus请求结构
 * @param response Modbus响应结构
 * @param timeout_ms 超时时间(毫秒)
 * @return modbus_error_t 错误码
 */
modbus_error_t rs485_modbus_transaction(const modbus_request_t* request, 
                                       modbus_response_t* response, 
                                       uint32_t timeout_ms);

/**
 * 读取保持寄存器
 * @param slave_addr 从机地址
 * @param start_addr 起始地址
 * @param quantity 寄存器数量
 * @param data 输出数据缓冲区
 * @param timeout_ms 超时时间(毫秒)
 * @return modbus_error_t 错误码
 */
modbus_error_t rs485_read_holding_registers(uint8_t slave_addr, 
                                           uint16_t start_addr, 
                                           uint16_t quantity, 
                                           uint16_t* data, 
                                           uint32_t timeout_ms);

/**
 * 读取输入寄存器
 * @param slave_addr 从机地址
 * @param start_addr 起始地址
 * @param quantity 寄存器数量
 * @param data 输出数据缓冲区
 * @param timeout_ms 超时时间(毫秒)
 * @return modbus_error_t 错误码
 */
modbus_error_t rs485_read_input_registers(uint8_t slave_addr, 
                                         uint16_t start_addr, 
                                         uint16_t quantity, 
                                         uint16_t* data, 
                                         uint32_t timeout_ms);

/**
 * 写单个保持寄存器
 * @param slave_addr 从机地址
 * @param register_addr 寄存器地址
 * @param value 写入值
 * @param timeout_ms 超时时间(毫秒)
 * @return modbus_error_t 错误码
 */
modbus_error_t rs485_write_single_register(uint8_t slave_addr, 
                                          uint16_t register_addr, 
                                          uint16_t value, 
                                          uint32_t timeout_ms);

/**
 * 计算Modbus CRC16校验码
 * @param buffer 数据缓冲区
 * @param length 数据长度
 * @return uint16_t CRC16校验码
 */
uint16_t rs485_calculate_crc16(const uint8_t* buffer, uint16_t length);

/**
 * 清空接收缓冲区
 */
void rs485_flush_rx_buffer(void);

/**
 * 获取RS485统计信息
 * @return rs485_stats_t 统计信息结构
 */
rs485_stats_t rs485_get_stats(void);

/**
 * 重置RS485统计信息
 */
void rs485_reset_stats(void);

/**
 * 打印RS485状态和统计信息
 */
void rs485_print_stats(void);

/**
 * 检查RS485通信是否正常
 * @return true 正常，false 异常
 */
bool rs485_is_healthy(void);

/**
 * 设置RS485超时时间
 * @param timeout_ms 超时时间(毫秒)
 */
void rs485_set_timeout(uint32_t timeout_ms);

/**
 * 获取错误描述字符串
 * @param error 错误码
 * @return const char* 错误描述
 */
const char* rs485_get_error_string(modbus_error_t error);

// ==================== 便利宏定义 ====================

/**
 * 读取pH传感器数据的便利宏
 */
#define RS485_READ_PH_SENSOR(slave_addr, ph_value, temp_value) \
    rs485_read_sensor_data(slave_addr, 0x0000, 2, (uint16_t[]){ph_value, temp_value})

/**
 * 读取ORP传感器数据的便利宏
 */
#define RS485_READ_ORP_SENSOR(slave_addr, orp_value) \
    rs485_read_sensor_data(slave_addr, 0x0000, 1, &orp_value)

/**
 * 读取溶解氧传感器数据的便利宏
 */
#define RS485_READ_DO_SENSOR(slave_addr, do_value, temp_value) \
    rs485_read_sensor_data(slave_addr, 0x0000, 2, (uint16_t[]){do_value, temp_value})

#endif // M_P4_RS485_H
