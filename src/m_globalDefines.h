#ifndef M_GLOBAL_DEFINES_H
#define M_GLOBAL_DEFINES_H

// ESP32-P4项目的全局定义文件
// 从原始esp32Touch43项目移植而来

// 系统参数
#define SYSTEM_TITLE "水质监测系统"  // 系统标题
#define SERIAL_BAUDRATE 115200       // 串口波特率

// 传感器相关参数
#define MAX_COMMAND_LENGTH 16  // 单个命令最大长度
#define MAX_COMMANDS 10        // 每个传感器最多支持的命令数量
#define MAX_DEVICES 10         // 最大设备数量
#define MAX_DEVICES_INIT 3     // 初始化设备数量
#define SENSOR_READ_INTERVAL 3000  // 传感器读取间隔(毫秒)
#define MAX_SENSORS 10         // 最大传感器数量

// 字符串和缓冲区大小配置
#define MAX_STRING_LENGTH           512     // 最大字符串长度
#define LOG_BUFFER_SIZE             1024    // 日志缓冲区大小
#define SENSOR_NAME_MAX_LENGTH      64      // 传感器名称最大长度
#define MESSAGE_BUFFER_SIZE         256     // 消息缓冲区大小
#define DEFAULT_WINDOW_SIZE         120     // 默认数据窗口大小

// 传感器类型 - 与Flutter保持一致
enum SensorParameterType {
  ph = 0,         // pH值
  orp,            // ORP氧化还原电位
  oxygen,         // 溶解氧
  temperature,    // 温度
  conductivity,   // 电导率
  turbidity,      // 浊度
  salinity,       // 盐度
  calcium,        // 钙离子
  carbonate,      // 碳酸根
  potassium,      // 钾离子
  magnesium,      // 镁离子
  ammonia,        // 氨氮
  nitrite,        // 亚硝酸盐
  nitrate,        // 硝酸盐
  other           // 其他类型
};

// 调试选项
#define DEBUG_MODBUS true      // 启用Modbus调试输出
#define DEBUG_RESPONSE true    // 显示完整响应数据
#define PRINT_RAW_DATA true    // 打印原始数据

// ==================== RS485和Modbus配置 ====================
#define RS485_BAUDRATE      9600    // RS485波特率
#define MODBUS_TIMEOUT      1500    // Modbus超时时间(毫秒)

#endif // M_GLOBAL_DEFINES_H
