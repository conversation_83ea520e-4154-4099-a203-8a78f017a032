Command: cmake -G Ninja -DPYTHON_DEPS_CHECKED=1 -DPYTHON=/Users/<USER>/.espressif/python_env/idf5.5_py3.9_env/bin/python -DESP_PLATFORM=1 -DCCACHE_ENABLE=0 /Users/<USER>/Documents/Arduino/libraries/ESP32-S3-Touch-LCD-4.3-Demo/Arduino/examples/esp32P4
-- IDF_TARGET is not set, guessed 'esp32p4' from sdkconfig '/Users/<USER>/Documents/Arduino/libraries/ESP32-S3-Touch-LCD-4.3-Demo/Arduino/examples/esp32P4/sdkconfig'
-- Found Git: /usr/bin/git (found version "2.39.5 (Apple Git-154)")
-- Minimal build - OFF
-- The C compiler identification is GNU 14.2.0
-- The CXX compiler identification is GNU 14.2.0
-- The ASM compiler identification is GNU
-- Found assembler: /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/riscv32-esp-elf-gcc
-- Detecting C compiler ABI info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/riscv32-esp-elf-gcc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler ABI info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/riscv32-esp-elf-g++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- git rev-parse returned 'fatal: not a git repository (or any of the parent directories): .git'
-- Could not use 'git describe' to determine PROJECT_VER.
-- Building ESP-IDF components for target esp32p4
NOTICE: Processing 2 dependencies:
NOTICE: [1/2] bblanchon/ArduinoJson (7.4.2)
NOTICE: [2/2] idf (5.5.0)
-- Project sdkconfig file /Users/<USER>/Documents/Arduino/libraries/ESP32-S3-Touch-LCD-4.3-Demo/Arduino/examples/esp32P4/sdkconfig
Loading defaults file /Users/<USER>/Documents/Arduino/libraries/ESP32-S3-Touch-LCD-4.3-Demo/Arduino/examples/esp32P4/sdkconfig.defaults...
warning: unknown kconfig symbol 'ESP_TLS_SERVER_AND_CLIENT_CERT' assigned to 'y' in /Users/<USER>/Documents/Arduino/libraries/ESP32-S3-Touch-LCD-4.3-Demo/Arduino/examples/esp32P4/sdkconfig.defaults
warning: unknown kconfig symbol 'FREERTOS_ASSERT_ON_UNTESTED_FUNCTION' assigned to 'y' in /Users/<USER>/Documents/Arduino/libraries/ESP32-S3-Touch-LCD-4.3-Demo/Arduino/examples/esp32P4/sdkconfig.defaults
warning: unknown kconfig symbol 'ESP32P4_DEFAULT_CPU_FREQ_400' assigned to 'y' in /Users/<USER>/Documents/Arduino/libraries/ESP32-S3-Touch-LCD-4.3-Demo/Arduino/examples/esp32P4/sdkconfig.defaults
warning: unknown kconfig symbol 'ESP32P4_DEFAULT_CPU_FREQ_MHZ' assigned to '400' in /Users/<USER>/Documents/Arduino/libraries/ESP32-S3-Touch-LCD-4.3-Demo/Arduino/examples/esp32P4/sdkconfig.defaults
warning: unknown kconfig symbol 'ENABLE_ARDUINO_DEPENDS' assigned to 'y' in /Users/<USER>/Documents/Arduino/libraries/ESP32-S3-Touch-LCD-4.3-Demo/Arduino/examples/esp32P4/sdkconfig.defaults
warning: unknown kconfig symbol 'AUTOSTART_ARDUINO' assigned to 'y' in /Users/<USER>/Documents/Arduino/libraries/ESP32-S3-Touch-LCD-4.3-Demo/Arduino/examples/esp32P4/sdkconfig.defaults
warning: unknown kconfig symbol 'ARDUINO_RUNNING_CORE' assigned to '1' in /Users/<USER>/Documents/Arduino/libraries/ESP32-S3-Touch-LCD-4.3-Demo/Arduino/examples/esp32P4/sdkconfig.defaults
warning: unknown kconfig symbol 'ARDUINO_EVENT_RUNNING_CORE' assigned to '1' in /Users/<USER>/Documents/Arduino/libraries/ESP32-S3-Touch-LCD-4.3-Demo/Arduino/examples/esp32P4/sdkconfig.defaults
-- Adding linker script /Users/<USER>/esp/v5.5/esp-idf/components/riscv/ld/rom.api.ld
-- Found Python3: /Users/<USER>/.espressif/python_env/idf5.5_py3.9_env/bin/python (found version "3.9.6") found components: Interpreter
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE
-- Performing Test C_COMPILER_SUPPORTS_WFORMAT_SIGNEDNESS
-- Performing Test C_COMPILER_SUPPORTS_WFORMAT_SIGNEDNESS - Success
-- USING O3
-- App "HelloWorld" version: 1
-- Adding linker script /Users/<USER>/Documents/Arduino/libraries/ESP32-S3-Touch-LCD-4.3-Demo/Arduino/examples/esp32P4/build/esp-idf/esp_system/ld/memory.ld
-- Adding linker script /Users/<USER>/Documents/Arduino/libraries/ESP32-S3-Touch-LCD-4.3-Demo/Arduino/examples/esp32P4/build/esp-idf/esp_system/ld/sections.ld.in
-- Adding linker script /Users/<USER>/esp/v5.5/esp-idf/components/esp_rom/esp32p4/ld/esp32p4.rom.ld
-- Adding linker script /Users/<USER>/esp/v5.5/esp-idf/components/esp_rom/esp32p4/ld/esp32p4.rom.api.ld
-- Adding linker script /Users/<USER>/esp/v5.5/esp-idf/components/esp_rom/esp32p4/ld/esp32p4.rom.rvfp.ld
-- Adding linker script /Users/<USER>/esp/v5.5/esp-idf/components/esp_rom/esp32p4/ld/esp32p4.rom.wdt.ld
-- Adding linker script /Users/<USER>/esp/v5.5/esp-idf/components/esp_rom/esp32p4/ld/esp32p4.rom.systimer.ld
-- Adding linker script /Users/<USER>/esp/v5.5/esp-idf/components/esp_rom/esp32p4/ld/esp32p4.rom.version.ld
-- Adding linker script /Users/<USER>/esp/v5.5/esp-idf/components/esp_rom/esp32p4/ld/esp32p4.rom.libc.ld
-- Adding linker script /Users/<USER>/esp/v5.5/esp-idf/components/esp_rom/esp32p4/ld/esp32p4.rom.libc-suboptimal_for_misaligned_mem.ld
-- Adding linker script /Users/<USER>/esp/v5.5/esp-idf/components/esp_rom/esp32p4/ld/esp32p4.rom.newlib.ld
-- Adding linker script /Users/<USER>/esp/v5.5/esp-idf/components/soc/esp32p4/ld/esp32p4.peripherals.ld
-- Components: app_trace app_update bblanchon__ArduinoJson bootloader bootloader_support bt cmock console cxx driver efuse esp-tls esp_adc esp_app_format esp_bootloader_format esp_coex esp_common esp_driver_ana_cmpr esp_driver_bitscrambler esp_driver_cam esp_driver_dac esp_driver_gpio esp_driver_gptimer esp_driver_i2c esp_driver_i2s esp_driver_isp esp_driver_jpeg esp_driver_ledc esp_driver_mcpwm esp_driver_parlio esp_driver_pcnt esp_driver_ppa esp_driver_rmt esp_driver_sdio esp_driver_sdm esp_driver_sdmmc esp_driver_sdspi esp_driver_spi esp_driver_touch_sens esp_driver_tsens esp_driver_twai esp_driver_uart esp_driver_usb_serial_jtag esp_eth esp_event esp_gdbstub esp_hid esp_http_client esp_http_server esp_https_ota esp_https_server esp_hw_support esp_lcd esp_local_ctrl esp_mm esp_netif esp_netif_stack esp_partition esp_phy esp_pm esp_psram esp_ringbuf esp_rom esp_security esp_system esp_timer esp_vfs_console esp_wifi espcoredump esptool_py fatfs freertos hal heap http_parser idf_test ieee802154 json log lwip main mbedtls mqtt newlib nvs_flash nvs_sec_provider openthread partition_table protobuf-c protocomm pthread riscv rt sdmmc soc spi_flash spiffs tcp_transport ulp unity usb vfs wear_levelling wifi_provisioning wpa_supplicant
-- Component paths: /Users/<USER>/esp/v5.5/esp-idf/components/app_trace /Users/<USER>/esp/v5.5/esp-idf/components/app_update /Users/<USER>/Documents/Arduino/libraries/ESP32-S3-Touch-LCD-4.3-Demo/Arduino/examples/esp32P4/managed_components/bblanchon__ArduinoJson /Users/<USER>/esp/v5.5/esp-idf/components/bootloader /Users/<USER>/esp/v5.5/esp-idf/components/bootloader_support /Users/<USER>/esp/v5.5/esp-idf/components/bt /Users/<USER>/esp/v5.5/esp-idf/components/cmock /Users/<USER>/esp/v5.5/esp-idf/components/console /Users/<USER>/esp/v5.5/esp-idf/components/cxx /Users/<USER>/esp/v5.5/esp-idf/components/driver /Users/<USER>/esp/v5.5/esp-idf/components/efuse /Users/<USER>/esp/v5.5/esp-idf/components/esp-tls /Users/<USER>/esp/v5.5/esp-idf/components/esp_adc /Users/<USER>/esp/v5.5/esp-idf/components/esp_app_format /Users/<USER>/esp/v5.5/esp-idf/components/esp_bootloader_format /Users/<USER>/esp/v5.5/esp-idf/components/esp_coex /Users/<USER>/esp/v5.5/esp-idf/components/esp_common /Users/<USER>/esp/v5.5/esp-idf/components/esp_driver_ana_cmpr /Users/<USER>/esp/v5.5/esp-idf/components/esp_driver_bitscrambler /Users/<USER>/esp/v5.5/esp-idf/components/esp_driver_cam /Users/<USER>/esp/v5.5/esp-idf/components/esp_driver_dac /Users/<USER>/esp/v5.5/esp-idf/components/esp_driver_gpio /Users/<USER>/esp/v5.5/esp-idf/components/esp_driver_gptimer /Users/<USER>/esp/v5.5/esp-idf/components/esp_driver_i2c /Users/<USER>/esp/v5.5/esp-idf/components/esp_driver_i2s /Users/<USER>/esp/v5.5/esp-idf/components/esp_driver_isp /Users/<USER>/esp/v5.5/esp-idf/components/esp_driver_jpeg /Users/<USER>/esp/v5.5/esp-idf/components/esp_driver_ledc /Users/<USER>/esp/v5.5/esp-idf/components/esp_driver_mcpwm /Users/<USER>/esp/v5.5/esp-idf/components/esp_driver_parlio /Users/<USER>/esp/v5.5/esp-idf/components/esp_driver_pcnt /Users/<USER>/esp/v5.5/esp-idf/components/esp_driver_ppa /Users/<USER>/esp/v5.5/esp-idf/components/esp_driver_rmt /Users/<USER>/esp/v5.5/esp-idf/components/esp_driver_sdio /Users/<USER>/esp/v5.5/esp-idf/components/esp_driver_sdm /Users/<USER>/esp/v5.5/esp-idf/components/esp_driver_sdmmc /Users/<USER>/esp/v5.5/esp-idf/components/esp_driver_sdspi /Users/<USER>/esp/v5.5/esp-idf/components/esp_driver_spi /Users/<USER>/esp/v5.5/esp-idf/components/esp_driver_touch_sens /Users/<USER>/esp/v5.5/esp-idf/components/esp_driver_tsens /Users/<USER>/esp/v5.5/esp-idf/components/esp_driver_twai /Users/<USER>/esp/v5.5/esp-idf/components/esp_driver_uart /Users/<USER>/esp/v5.5/esp-idf/components/esp_driver_usb_serial_jtag /Users/<USER>/esp/v5.5/esp-idf/components/esp_eth /Users/<USER>/esp/v5.5/esp-idf/components/esp_event /Users/<USER>/esp/v5.5/esp-idf/components/esp_gdbstub /Users/<USER>/esp/v5.5/esp-idf/components/esp_hid /Users/<USER>/esp/v5.5/esp-idf/components/esp_http_client /Users/<USER>/esp/v5.5/esp-idf/components/esp_http_server /Users/<USER>/esp/v5.5/esp-idf/components/esp_https_ota /Users/<USER>/esp/v5.5/esp-idf/components/esp_https_server /Users/<USER>/esp/v5.5/esp-idf/components/esp_hw_support /Users/<USER>/esp/v5.5/esp-idf/components/esp_lcd /Users/<USER>/esp/v5.5/esp-idf/components/esp_local_ctrl /Users/<USER>/esp/v5.5/esp-idf/components/esp_mm /Users/<USER>/esp/v5.5/esp-idf/components/esp_netif /Users/<USER>/esp/v5.5/esp-idf/components/esp_netif_stack /Users/<USER>/esp/v5.5/esp-idf/components/esp_partition /Users/<USER>/esp/v5.5/esp-idf/components/esp_phy /Users/<USER>/esp/v5.5/esp-idf/components/esp_pm /Users/<USER>/esp/v5.5/esp-idf/components/esp_psram /Users/<USER>/esp/v5.5/esp-idf/components/esp_ringbuf /Users/<USER>/esp/v5.5/esp-idf/components/esp_rom /Users/<USER>/esp/v5.5/esp-idf/components/esp_security /Users/<USER>/esp/v5.5/esp-idf/components/esp_system /Users/<USER>/esp/v5.5/esp-idf/components/esp_timer /Users/<USER>/esp/v5.5/esp-idf/components/esp_vfs_console /Users/<USER>/esp/v5.5/esp-idf/components/esp_wifi /Users/<USER>/esp/v5.5/esp-idf/components/espcoredump /Users/<USER>/esp/v5.5/esp-idf/components/esptool_py /Users/<USER>/esp/v5.5/esp-idf/components/fatfs /Users/<USER>/esp/v5.5/esp-idf/components/freertos /Users/<USER>/esp/v5.5/esp-idf/components/hal /Users/<USER>/esp/v5.5/esp-idf/components/heap /Users/<USER>/esp/v5.5/esp-idf/components/http_parser /Users/<USER>/esp/v5.5/esp-idf/components/idf_test /Users/<USER>/esp/v5.5/esp-idf/components/ieee802154 /Users/<USER>/esp/v5.5/esp-idf/components/json /Users/<USER>/esp/v5.5/esp-idf/components/log /Users/<USER>/esp/v5.5/esp-idf/components/lwip /Users/<USER>/Documents/Arduino/libraries/ESP32-S3-Touch-LCD-4.3-Demo/Arduino/examples/esp32P4/main /Users/<USER>/esp/v5.5/esp-idf/components/mbedtls /Users/<USER>/esp/v5.5/esp-idf/components/mqtt /Users/<USER>/esp/v5.5/esp-idf/components/newlib /Users/<USER>/esp/v5.5/esp-idf/components/nvs_flash /Users/<USER>/esp/v5.5/esp-idf/components/nvs_sec_provider /Users/<USER>/esp/v5.5/esp-idf/components/openthread /Users/<USER>/esp/v5.5/esp-idf/components/partition_table /Users/<USER>/esp/v5.5/esp-idf/components/protobuf-c /Users/<USER>/esp/v5.5/esp-idf/components/protocomm /Users/<USER>/esp/v5.5/esp-idf/components/pthread /Users/<USER>/esp/v5.5/esp-idf/components/riscv /Users/<USER>/esp/v5.5/esp-idf/components/rt /Users/<USER>/esp/v5.5/esp-idf/components/sdmmc /Users/<USER>/esp/v5.5/esp-idf/components/soc /Users/<USER>/esp/v5.5/esp-idf/components/spi_flash /Users/<USER>/esp/v5.5/esp-idf/components/spiffs /Users/<USER>/esp/v5.5/esp-idf/components/tcp_transport /Users/<USER>/esp/v5.5/esp-idf/components/ulp /Users/<USER>/esp/v5.5/esp-idf/components/unity /Users/<USER>/esp/v5.5/esp-idf/components/usb /Users/<USER>/esp/v5.5/esp-idf/components/vfs /Users/<USER>/esp/v5.5/esp-idf/components/wear_levelling /Users/<USER>/esp/v5.5/esp-idf/components/wifi_provisioning /Users/<USER>/esp/v5.5/esp-idf/components/wpa_supplicant
-- Configuring done (5.4s)
-- Generating done (0.4s)
-- Build files have been written to: /Users/<USER>/Documents/Arduino/libraries/ESP32-S3-Touch-LCD-4.3-Demo/Arduino/examples/esp32P4/build
