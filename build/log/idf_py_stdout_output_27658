Command: /Users/<USER>/.espressif/python_env/idf5.5_py3.9_env/bin/python /Users/<USER>/esp/v5.5/esp-idf/tools/idf_monitor.py -p /dev/cu.wchusbserial5A7A0449951 -b 115200 --toolchain-prefix riscv32-esp-elf- --target esp32p4 --revision 1 --decode-panic backtrace /Users/<USER>/Documents/Arduino/libraries/ESP32-S3-Touch-LCD-4.3-Demo/Arduino/examples/esp32P4/build/HelloWorld.elf /Users/<USER>/Documents/Arduino/libraries/ESP32-S3-Touch-LCD-4.3-Demo/Arduino/examples/esp32P4/build/bootloader/bootloader.elf -m '/Users/<USER>/.espressif/python_env/idf5.5_py3.9_env/bin/python' '/Users/<USER>/esp/v5.5/esp-idf/tools/idf.py'
ESP-ROM:esp32p4-eco2-20240710
Build:Jul 10 2024
rst:0x1 (POWERON),boot:0x30f (SPI_FAST_FLASH_BOOT)
SPI mode:DIO, clock div:1
load:0x4ff33ce0,len:0x164c
load:0x4ff29ed0,len:0xd64
load:0x4ff2cbd0,len:0x3364
entry 0x4ff29eda
[0;32mI (25) boot: ESP-IDF v5.5-dirty 2nd stage bootloader[0m
[0;32mI (26) boot: compile time Jul 29 2025 12:48:23[0m
[0;32mI (26) boot: Multicore bootloader[0m
[0;32mI (27) boot: chip revision: v1.0[0m
[0;32mI (29) boot: efuse block revision: v0.3[0m
[0;32mI (33) boot.esp32p4: SPI Speed      : 80MHz[0m
[0;32mI (36) boot.esp32p4: SPI Mode       : DIO[0m
[0;32mI (40) boot.esp32p4: SPI Flash Size : 16MB[0m
[0;32mI (44) boot: Enabling RNG early entropy source...[0m
[0;32mI (49) boot: Partition Table:[0m
[0;32mI (51) boot: ## Label            Usage          Type ST Offset   Length[0m
[0;32mI (57) boot:  0 nvs              WiFi data        01 02 00009000 00006000[0m
[0;32mI (64) boot:  1 phy_init         RF data          01 01 0000f000 00001000[0m
[0;32mI (70) boot:  2 factory          factory app      00 00 00010000 00800000[0m
[0;32mI (77) boot:  3 storage          Unknown data     01 82 00810000 00600000[0m
[0;32mI (83) boot:  4 coredump         Unknown data     01 03 00e10000 00010000[0m
[0;32mI (90) boot:  5 nvs_key          NVS keys         01 04 00e20000 00001000[0m
[0;32mI (96) boot:  6 data             Unknown data     01 81 00e30000 001d0000[0m
[0;32mI (104) boot: End of partition table[0m
[0;32mI (107) esp_image: segment 0: paddr=00010020 vaddr=40020020 size=0be58h ( 48728) map[0m
[0;32mI (123) esp_image: segment 1: paddr=0001be80 vaddr=30100000 size=00044h (    68) load[0m
[0;32mI (125) esp_image: segment 2: paddr=0001becc vaddr=4ff00000 size=0414ch ( 16716) load[0m
[0;32mI (133) esp_image: segment 3: paddr=00020020 vaddr=40000020 size=1dd40h (122176) map[0m
[0;32mI (158) esp_image: segment 4: paddr=0003dd68 vaddr=4ff0414c size=0b824h ( 47140) load[0m
[0;32mI (169) esp_image: segment 5: paddr=00049594 vaddr=4ff0f980 size=01da8h (  7592) load[0m
[0;32mI (175) boot: Loaded app from partition at offset 0x10000[0m
[0;32mI (175) boot: Disabling RNG early entropy source...[0m
[0;32mI (186) cpu_start: Multicore app[0m
[0;32mI (195) cpu_start: Pro cpu start user code[0m
[0;32mI (195) cpu_start: cpu freq: 360000000 Hz[0m
[0;32mI (196) app_init: Application information:[0m
[0;32mI (196) app_init: Project name:     HelloWorld[0m
[0;32mI (200) app_init: App version:      1[0m
[0;32mI (203) app_init: Compile time:     Jul 29 2025 12:48:17[0m
[0;32mI (208) app_init: ELF file SHA256:  3eeb394c4...[0m
[0;32mI (212) app_init: ESP-IDF:          v5.5-dirty[0m
[0;32mI (216) efuse_init: Min chip rev:     v0.1[0m
[0;32mI (220) efuse_init: Max chip rev:     v1.99 [0m
[0;32mI (224) efuse_init: Chip rev:         v1.0[0m
[0;32mI (228) heap_init: Initializing. RAM available for dynamic allocation:[0m
[0;32mI (234) heap_init: At 4FF12E90 len 00028130 (160 KiB): RAM[0m
[0;32mI (240) heap_init: At 4FF3AFC0 len 00004BF0 (18 KiB): RAM[0m
[0;32mI (245) heap_init: At 4FF40000 len 00060000 (384 KiB): RAM[0m
[0;32mI (250) heap_init: At 50108080 len 00007F80 (31 KiB): RTCRAM[0m
[0;32mI (255) heap_init: At 30100044 len 00001FBC (7 KiB): TCM[0m
[0;33mW (261) spi_flash: GigaDevice detected but related driver is not linked, please check option `SPI_FLASH_SUPPORT_GD_CHIP`[0m
[0;32mI (271) spi_flash: detected chip: generic[0m
[0;32mI (275) spi_flash: flash io: dio[0m
[0;32mI (278) main_task: Started on CPU0[0m
[0;32mI (318) main_task: Calling app_main()[0m
[0;32mI (318) RS485_LOOPBACK: === ESP32-P4 RS485回环测试程序 ===[0m
[0;32mI (318) RS485_LOOPBACK: 🎯 测试目标: 验证HVD75扩展板回环功能[0m
[0;32mI (318) RS485_LOOPBACK: 📋 测试条件:[0m
[0;32mI (328) RS485_LOOPBACK:    - HVD75扩展板A+和B-已短接[0m
[0;32mI (328) RS485_LOOPBACK:    - ESP32-P4 GPIO21 → 扩展板RX (TX_PIN)[0m
[0;32mI (338) RS485_LOOPBACK:    - ESP32-P4 GPIO20 ← 扩展板TX (RX_PIN)[0m
[0;32mI (348) RS485_LOOPBACK:    - 3.3V供电正常[0m
[0;32mI (348) RS485_LOOPBACK: 🚀 开始测试...[0m

[0;32mI (348) RS485_LOOPBACK: 🔧 RS485回环测试开始[0m
[0;32mI (358) RS485_LOOPBACK:    UART: 2, TX: GPIO21, RX: GPIO20, 波特率: 9600[0m
[0;32mI (368) RS485_LOOPBACK:    前提条件: HVD75扩展板A+和B-已短接[0m
[0;32mI (368) RS485_LOOPBACK: ✅ UART配置完成[0m
[0;32mI (378) RS485_LOOPBACK: === 第1次回环测试 ===[0m
[0;32mI (378) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (388) main_task: Returned from app_main()[0m
[0;32mI (448) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (448) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (448) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (468) RS485_LOOPBACK: 📥 接收到 5 字节，总计 5/5[0m
[0;32mI (468) RS485_LOOPBACK: 📥 接收完成: "HELLO" (5字节)[0m
[0;32mI (468) RS485_LOOPBACK: ⏱️  总耗时: 20 ms[0m
[0;32mI (468) RS485_LOOPBACK: ⚠️  数据匹配但时间过短(20.0ms)，可能是缓冲区残留[0m
[0;32mI (478) RS485_LOOPBACK: 📊 统计: 0/1 成功 (0.0%)[0m
[0;32mI (478) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (3488) RS485_LOOPBACK: === 第2次回环测试 ===[0m
[0;32mI (3488) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (3548) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (3548) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (3558) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (3578) RS485_LOOPBACK: 📥 接收到 10 字节，总计 10/10[0m
[0;32mI (3578) RS485_LOOPBACK: 📥 接收完成: "RS485_TEST" (10字节)[0m
[0;32mI (3578) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (3578) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (3588) RS485_LOOPBACK: 📊 统计: 1/2 成功 (50.0%)[0m
[0;32mI (3588) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (6598) RS485_LOOPBACK: === 第3次回环测试 ===[0m
[0;32mI (6598) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (6658) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (6658) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (6668) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (6688) RS485_LOOPBACK: 📥 接收到 11 字节，总计 11/11[0m
[0;32mI (6688) RS485_LOOPBACK: 📥 接收完成: "LOOPBACK_OK" (11字节)[0m
[0;32mI (6688) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (6688) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (6698) RS485_LOOPBACK: 📊 统计: 2/3 成功 (66.7%)[0m
[0;32mI (6698) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (9708) RS485_LOOPBACK: === 第4次回环测试 ===[0m
[0;32mI (9708) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (9768) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (9768) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (9778) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (9798) RS485_LOOPBACK: 📥 接收到 13 字节，总计 13/13[0m
[0;32mI (9798) RS485_LOOPBACK: 📥 接收完成: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (9798) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (9798) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (9808) RS485_LOOPBACK: 📊 统计: 3/4 成功 (75.0%)[0m
[0;32mI (9808) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (12818) RS485_LOOPBACK: === 第5次回环测试 ===[0m
[0;32mI (12818) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (12878) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (12878) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (12888) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (12908) RS485_LOOPBACK: 📥 接收到 16 字节，总计 16/16[0m
[0;32mI (12908) RS485_LOOPBACK: 📥 接收完成: "0123456789ABCDEF" (16字节)[0m
[0;32mI (12908) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (12908) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (12918) RS485_LOOPBACK: 📊 统计: 4/5 成功 (80.0%)[0m
[0;32mI (12918) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (15928) RS485_LOOPBACK: === 第6次回环测试 ===[0m
[0;32mI (15928) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (15988) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (15988) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (15988) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (16008) RS485_LOOPBACK: 📥 接收到 5 字节，总计 5/5[0m
[0;32mI (16008) RS485_LOOPBACK: 📥 接收完成: "HELLO" (5字节)[0m
[0;32mI (16008) RS485_LOOPBACK: ⏱️  总耗时: 20 ms[0m
[0;32mI (16008) RS485_LOOPBACK: ⚠️  数据匹配但时间过短(20.0ms)，可能是缓冲区残留[0m
[0;32mI (16018) RS485_LOOPBACK: 📊 统计: 4/6 成功 (66.7%)[0m
[0;32mI (16018) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (19028) RS485_LOOPBACK: === 第7次回环测试 ===[0m
[0;32mI (19028) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (19088) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (19088) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (19098) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (19118) RS485_LOOPBACK: 📥 接收到 10 字节，总计 10/10[0m
[0;32mI (19118) RS485_LOOPBACK: 📥 接收完成: "RS485_TEST" (10字节)[0m
[0;32mI (19118) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (19118) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (19128) RS485_LOOPBACK: 📊 统计: 5/7 成功 (71.4%)[0m
[0;32mI (19128) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (22138) RS485_LOOPBACK: === 第8次回环测试 ===[0m
[0;32mI (22138) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (22198) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (22198) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (22208) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (22228) RS485_LOOPBACK: 📥 接收到 11 字节，总计 11/11[0m
[0;32mI (22228) RS485_LOOPBACK: 📥 接收完成: "LOOPBACK_OK" (11字节)[0m
[0;32mI (22228) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (22228) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (22238) RS485_LOOPBACK: 📊 统计: 6/8 成功 (75.0%)[0m
[0;32mI (22238) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (25248) RS485_LOOPBACK: === 第9次回环测试 ===[0m
[0;32mI (25248) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (25308) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (25308) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (25318) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (25338) RS485_LOOPBACK: 📥 接收到 13 字节，总计 13/13[0m
[0;32mI (25338) RS485_LOOPBACK: 📥 接收完成: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (25338) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (25338) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (25348) RS485_LOOPBACK: 📊 统计: 7/9 成功 (77.8%)[0m
[0;32mI (25348) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (28358) RS485_LOOPBACK: === 第10次回环测试 ===[0m
[0;32mI (28358) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (28418) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (28418) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (28428) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (28448) RS485_LOOPBACK: 📥 接收到 16 字节，总计 16/16[0m
[0;32mI (28448) RS485_LOOPBACK: 📥 接收完成: "0123456789ABCDEF" (16字节)[0m
[0;32mI (28448) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (28448) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (28458) RS485_LOOPBACK: 📊 统计: 8/10 成功 (80.0%)[0m
[0;32mI (28458) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (31468) RS485_LOOPBACK: === 第11次回环测试 ===[0m
[0;32mI (31468) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (31528) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (31528) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (31528) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (31548) RS485_LOOPBACK: 📥 接收到 5 字节，总计 5/5[0m
[0;32mI (31548) RS485_LOOPBACK: 📥 接收完成: "HELLO" (5字节)[0m
[0;32mI (31548) RS485_LOOPBACK: ⏱️  总耗时: 20 ms[0m
[0;32mI (31548) RS485_LOOPBACK: ⚠️  数据匹配但时间过短(20.0ms)，可能是缓冲区残留[0m
[0;32mI (31558) RS485_LOOPBACK: 📊 统计: 8/11 成功 (72.7%)[0m
[0;32mI (31558) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (34568) RS485_LOOPBACK: === 第12次回环测试 ===[0m
[0;32mI (34568) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (34628) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (34628) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (34638) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (34658) RS485_LOOPBACK: 📥 接收到 10 字节，总计 10/10[0m
[0;32mI (34658) RS485_LOOPBACK: 📥 接收完成: "RS485_TEST" (10字节)[0m
[0;32mI (34658) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (34658) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (34668) RS485_LOOPBACK: 📊 统计: 9/12 成功 (75.0%)[0m
[0;32mI (34668) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (37678) RS485_LOOPBACK: === 第13次回环测试 ===[0m
[0;32mI (37678) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (37738) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (37738) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (37748) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (37768) RS485_LOOPBACK: 📥 接收到 11 字节，总计 11/11[0m
[0;32mI (37768) RS485_LOOPBACK: 📥 接收完成: "LOOPBACK_OK" (11字节)[0m
[0;32mI (37768) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (37768) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (37778) RS485_LOOPBACK: 📊 统计: 10/13 成功 (76.9%)[0m
[0;32mI (37778) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (40788) RS485_LOOPBACK: === 第14次回环测试 ===[0m
[0;32mI (40788) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (40848) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (40848) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (40858) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (40878) RS485_LOOPBACK: 📥 接收到 13 字节，总计 13/13[0m
[0;32mI (40878) RS485_LOOPBACK: 📥 接收完成: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (40878) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (40878) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (40888) RS485_LOOPBACK: 📊 统计: 11/14 成功 (78.6%)[0m
[0;32mI (40888) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (43898) RS485_LOOPBACK: === 第15次回环测试 ===[0m
[0;32mI (43898) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (43958) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (43958) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (43968) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (43988) RS485_LOOPBACK: 📥 接收到 16 字节，总计 16/16[0m
[0;32mI (43988) RS485_LOOPBACK: 📥 接收完成: "0123456789ABCDEF" (16字节)[0m
[0;32mI (43988) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (43988) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (43998) RS485_LOOPBACK: 📊 统计: 12/15 成功 (80.0%)[0m
[0;32mI (43998) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (47008) RS485_LOOPBACK: === 第16次回环测试 ===[0m
[0;32mI (47008) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (47068) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (47068) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (47068) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (47088) RS485_LOOPBACK: 📥 接收到 5 字节，总计 5/5[0m
[0;32mI (47088) RS485_LOOPBACK: 📥 接收完成: "HELLO" (5字节)[0m
[0;32mI (47088) RS485_LOOPBACK: ⏱️  总耗时: 20 ms[0m
[0;32mI (47088) RS485_LOOPBACK: ⚠️  数据匹配但时间过短(20.0ms)，可能是缓冲区残留[0m
[0;32mI (47098) RS485_LOOPBACK: 📊 统计: 12/16 成功 (75.0%)[0m
[0;32mI (47098) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (50108) RS485_LOOPBACK: === 第17次回环测试 ===[0m
[0;32mI (50108) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (50168) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (50168) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (50178) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (50198) RS485_LOOPBACK: 📥 接收到 10 字节，总计 10/10[0m
[0;32mI (50198) RS485_LOOPBACK: 📥 接收完成: "RS485_TEST" (10字节)[0m
[0;32mI (50198) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (50198) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (50208) RS485_LOOPBACK: 📊 统计: 13/17 成功 (76.5%)[0m
[0;32mI (50208) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (53218) RS485_LOOPBACK: === 第18次回环测试 ===[0m
[0;32mI (53218) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (53278) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (53278) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (53288) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (53308) RS485_LOOPBACK: 📥 接收到 11 字节，总计 11/11[0m
[0;32mI (53308) RS485_LOOPBACK: 📥 接收完成: "LOOPBACK_OK" (11字节)[0m
[0;32mI (53308) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (53308) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (53318) RS485_LOOPBACK: 📊 统计: 14/18 成功 (77.8%)[0m
[0;32mI (53318) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (56328) RS485_LOOPBACK: === 第19次回环测试 ===[0m
[0;32mI (56328) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (56388) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (56388) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (56398) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (56418) RS485_LOOPBACK: 📥 接收到 13 字节，总计 13/13[0m
[0;32mI (56418) RS485_LOOPBACK: 📥 接收完成: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (56418) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (56418) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (56428) RS485_LOOPBACK: 📊 统计: 15/19 成功 (78.9%)[0m
[0;32mI (56428) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (59438) RS485_LOOPBACK: === 第20次回环测试 ===[0m
[0;32mI (59438) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (59498) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (59498) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (59508) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (59528) RS485_LOOPBACK: 📥 接收到 16 字节，总计 16/16[0m
[0;32mI (59528) RS485_LOOPBACK: 📥 接收完成: "0123456789ABCDEF" (16字节)[0m
[0;32mI (59528) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (59528) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (59538) RS485_LOOPBACK: 📊 统计: 16/20 成功 (80.0%)[0m
[0;32mI (59538) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (62548) RS485_LOOPBACK: === 第21次回环测试 ===[0m
[0;32mI (62548) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (62608) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (62608) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (62608) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (62628) RS485_LOOPBACK: 📥 接收到 5 字节，总计 5/5[0m
[0;32mI (62628) RS485_LOOPBACK: 📥 接收完成: "HELLO" (5字节)[0m
[0;32mI (62628) RS485_LOOPBACK: ⏱️  总耗时: 20 ms[0m
[0;32mI (62628) RS485_LOOPBACK: ⚠️  数据匹配但时间过短(20.0ms)，可能是缓冲区残留[0m
[0;32mI (62638) RS485_LOOPBACK: 📊 统计: 16/21 成功 (76.2%)[0m
[0;32mI (62638) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (65648) RS485_LOOPBACK: === 第22次回环测试 ===[0m
[0;32mI (65648) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (65708) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (65708) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (65718) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (65738) RS485_LOOPBACK: 📥 接收到 10 字节，总计 10/10[0m
[0;32mI (65738) RS485_LOOPBACK: 📥 接收完成: "RS485_TEST" (10字节)[0m
[0;32mI (65738) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (65738) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (65748) RS485_LOOPBACK: 📊 统计: 17/22 成功 (77.3%)[0m
[0;32mI (65748) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (68758) RS485_LOOPBACK: === 第23次回环测试 ===[0m
[0;32mI (68758) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (68818) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (68818) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (68828) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (68848) RS485_LOOPBACK: 📥 接收到 11 字节，总计 11/11[0m
[0;32mI (68848) RS485_LOOPBACK: 📥 接收完成: "LOOPBACK_OK" (11字节)[0m
[0;32mI (68848) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (68848) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (68858) RS485_LOOPBACK: 📊 统计: 18/23 成功 (78.3%)[0m
[0;32mI (68858) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (71868) RS485_LOOPBACK: === 第24次回环测试 ===[0m
[0;32mI (71868) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (71928) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (71928) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (71938) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (71958) RS485_LOOPBACK: 📥 接收到 13 字节，总计 13/13[0m
[0;32mI (71958) RS485_LOOPBACK: 📥 接收完成: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (71958) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (71958) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (71968) RS485_LOOPBACK: 📊 统计: 19/24 成功 (79.2%)[0m
[0;32mI (71968) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (74978) RS485_LOOPBACK: === 第25次回环测试 ===[0m
[0;32mI (74978) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (75038) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (75038) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (75048) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (75068) RS485_LOOPBACK: 📥 接收到 16 字节，总计 16/16[0m
[0;32mI (75068) RS485_LOOPBACK: 📥 接收完成: "0123456789ABCDEF" (16字节)[0m
[0;32mI (75068) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (75068) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (75078) RS485_LOOPBACK: 📊 统计: 20/25 成功 (80.0%)[0m
[0;32mI (75078) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (78088) RS485_LOOPBACK: === 第26次回环测试 ===[0m
[0;32mI (78088) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (78148) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (78148) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (78148) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (78168) RS485_LOOPBACK: 📥 接收到 5 字节，总计 5/5[0m
[0;32mI (78168) RS485_LOOPBACK: 📥 接收完成: "HELLO" (5字节)[0m
[0;32mI (78168) RS485_LOOPBACK: ⏱️  总耗时: 20 ms[0m
[0;32mI (78168) RS485_LOOPBACK: ⚠️  数据匹配但时间过短(20.0ms)，可能是缓冲区残留[0m
[0;32mI (78178) RS485_LOOPBACK: 📊 统计: 20/26 成功 (76.9%)[0m
[0;32mI (78178) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (81188) RS485_LOOPBACK: === 第27次回环测试 ===[0m
[0;32mI (81188) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (81248) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (81248) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (81258) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (81278) RS485_LOOPBACK: 📥 接收到 10 字节，总计 10/10[0m
[0;32mI (81278) RS485_LOOPBACK: 📥 接收完成: "RS485_TEST" (10字节)[0m
[0;32mI (81278) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (81278) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (81288) RS485_LOOPBACK: 📊 统计: 21/27 成功 (77.8%)[0m
[0;32mI (81288) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (84298) RS485_LOOPBACK: === 第28次回环测试 ===[0m
[0;32mI (84298) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (84358) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (84358) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (84368) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (84388) RS485_LOOPBACK: 📥 接收到 11 字节，总计 11/11[0m
[0;32mI (84388) RS485_LOOPBACK: 📥 接收完成: "LOOPBACK_OK" (11字节)[0m
[0;32mI (84388) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (84388) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (84398) RS485_LOOPBACK: 📊 统计: 22/28 成功 (78.6%)[0m
[0;32mI (84398) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (87408) RS485_LOOPBACK: === 第29次回环测试 ===[0m
[0;32mI (87408) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (87468) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (87468) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (87478) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (87498) RS485_LOOPBACK: 📥 接收到 13 字节，总计 13/13[0m
[0;32mI (87498) RS485_LOOPBACK: 📥 接收完成: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (87498) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (87498) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (87508) RS485_LOOPBACK: 📊 统计: 23/29 成功 (79.3%)[0m
[0;32mI (87508) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (90518) RS485_LOOPBACK: === 第30次回环测试 ===[0m
[0;32mI (90518) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (90578) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (90578) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (90588) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (90608) RS485_LOOPBACK: 📥 接收到 16 字节，总计 16/16[0m
[0;32mI (90608) RS485_LOOPBACK: 📥 接收完成: "0123456789ABCDEF" (16字节)[0m
[0;32mI (90608) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (90608) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (90618) RS485_LOOPBACK: 📊 统计: 24/30 成功 (80.0%)[0m
[0;32mI (90618) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (93628) RS485_LOOPBACK: === 第31次回环测试 ===[0m
[0;32mI (93628) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (93688) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (93688) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (93688) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (93708) RS485_LOOPBACK: 📥 接收到 5 字节，总计 5/5[0m
[0;32mI (93708) RS485_LOOPBACK: 📥 接收完成: "HELLO" (5字节)[0m
[0;32mI (93708) RS485_LOOPBACK: ⏱️  总耗时: 20 ms[0m
[0;32mI (93708) RS485_LOOPBACK: ⚠️  数据匹配但时间过短(20.0ms)，可能是缓冲区残留[0m
[0;32mI (93718) RS485_LOOPBACK: 📊 统计: 24/31 成功 (77.4%)[0m
[0;32mI (93718) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (96728) RS485_LOOPBACK: === 第32次回环测试 ===[0m
[0;32mI (96728) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (96788) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (96788) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (96798) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (96818) RS485_LOOPBACK: 📥 接收到 10 字节，总计 10/10[0m
[0;32mI (96818) RS485_LOOPBACK: 📥 接收完成: "RS485_TEST" (10字节)[0m
[0;32mI (96818) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (96818) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (96828) RS485_LOOPBACK: 📊 统计: 25/32 成功 (78.1%)[0m
[0;32mI (96828) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (99838) RS485_LOOPBACK: === 第33次回环测试 ===[0m
[0;32mI (99838) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (99898) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (99898) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (99908) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (99928) RS485_LOOPBACK: 📥 接收到 11 字节，总计 11/11[0m
[0;32mI (99928) RS485_LOOPBACK: 📥 接收完成: "LOOPBACK_OK" (11字节)[0m
[0;32mI (99928) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (99928) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (99938) RS485_LOOPBACK: 📊 统计: 26/33 成功 (78.8%)[0m
[0;32mI (99938) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (102948) RS485_LOOPBACK: === 第34次回环测试 ===[0m
[0;32mI (102948) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (103008) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (103008) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (103018) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (103038) RS485_LOOPBACK: 📥 接收到 13 字节，总计 13/13[0m
[0;32mI (103038) RS485_LOOPBACK: 📥 接收完成: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (103038) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (103038) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (103048) RS485_LOOPBACK: 📊 统计: 27/34 成功 (79.4%)[0m
[0;32mI (103048) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (106058) RS485_LOOPBACK: === 第35次回环测试 ===[0m
[0;32mI (106058) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (106118) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (106118) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (106128) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (106148) RS485_LOOPBACK: 📥 接收到 16 字节，总计 16/16[0m
[0;32mI (106148) RS485_LOOPBACK: 📥 接收完成: "0123456789ABCDEF" (16字节)[0m
[0;32mI (106148) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (106148) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (106158) RS485_LOOPBACK: 📊 统计: 28/35 成功 (80.0%)[0m
[0;32mI (106158) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (109168) RS485_LOOPBACK: === 第36次回环测试 ===[0m
[0;32mI (109168) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (109228) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (109228) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (109228) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (109248) RS485_LOOPBACK: 📥 接收到 5 字节，总计 5/5[0m
[0;32mI (109248) RS485_LOOPBACK: 📥 接收完成: "HELLO" (5字节)[0m
[0;32mI (109248) RS485_LOOPBACK: ⏱️  总耗时: 20 ms[0m
[0;32mI (109248) RS485_LOOPBACK: ⚠️  数据匹配但时间过短(20.0ms)，可能是缓冲区残留[0m
[0;32mI (109258) RS485_LOOPBACK: 📊 统计: 28/36 成功 (77.8%)[0m
[0;32mI (109258) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (112268) RS485_LOOPBACK: === 第37次回环测试 ===[0m
[0;32mI (112268) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (112328) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (112328) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (112338) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (112358) RS485_LOOPBACK: 📥 接收到 10 字节，总计 10/10[0m
[0;32mI (112358) RS485_LOOPBACK: 📥 接收完成: "RS485_TEST" (10字节)[0m
[0;32mI (112358) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (112358) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (112368) RS485_LOOPBACK: 📊 统计: 29/37 成功 (78.4%)[0m
[0;32mI (112368) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (115378) RS485_LOOPBACK: === 第38次回环测试 ===[0m
[0;32mI (115378) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (115438) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (115438) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (115448) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (115468) RS485_LOOPBACK: 📥 接收到 11 字节，总计 11/11[0m
[0;32mI (115468) RS485_LOOPBACK: 📥 接收完成: "LOOPBACK_OK" (11字节)[0m
[0;32mI (115468) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (115468) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (115478) RS485_LOOPBACK: 📊 统计: 30/38 成功 (78.9%)[0m
[0;32mI (115478) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (118488) RS485_LOOPBACK: === 第39次回环测试 ===[0m
[0;32mI (118488) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (118548) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (118548) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (118558) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (118578) RS485_LOOPBACK: 📥 接收到 13 字节，总计 13/13[0m
[0;32mI (118578) RS485_LOOPBACK: 📥 接收完成: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (118578) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (118578) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (118588) RS485_LOOPBACK: 📊 统计: 31/39 成功 (79.5%)[0m
[0;32mI (118588) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (121598) RS485_LOOPBACK: === 第40次回环测试 ===[0m
[0;32mI (121598) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (121658) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (121658) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (121668) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (121688) RS485_LOOPBACK: 📥 接收到 16 字节，总计 16/16[0m
[0;32mI (121688) RS485_LOOPBACK: 📥 接收完成: "0123456789ABCDEF" (16字节)[0m
[0;32mI (121688) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (121688) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (121698) RS485_LOOPBACK: 📊 统计: 32/40 成功 (80.0%)[0m
[0;32mI (121698) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (124708) RS485_LOOPBACK: === 第41次回环测试 ===[0m
[0;32mI (124708) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (124768) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (124768) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (124768) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (124788) RS485_LOOPBACK: 📥 接收到 5 字节，总计 5/5[0m
[0;32mI (124788) RS485_LOOPBACK: 📥 接收完成: "HELLO" (5字节)[0m
[0;32mI (124788) RS485_LOOPBACK: ⏱️  总耗时: 20 ms[0m
[0;32mI (124788) RS485_LOOPBACK: ⚠️  数据匹配但时间过短(20.0ms)，可能是缓冲区残留[0m
[0;32mI (124798) RS485_LOOPBACK: 📊 统计: 32/41 成功 (78.0%)[0m
[0;32mI (124798) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (127808) RS485_LOOPBACK: === 第42次回环测试 ===[0m
[0;32mI (127808) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (127868) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (127868) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (127878) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (127898) RS485_LOOPBACK: 📥 接收到 10 字节，总计 10/10[0m
[0;32mI (127898) RS485_LOOPBACK: 📥 接收完成: "RS485_TEST" (10字节)[0m
[0;32mI (127898) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (127898) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (127908) RS485_LOOPBACK: 📊 统计: 33/42 成功 (78.6%)[0m
[0;32mI (127908) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (130918) RS485_LOOPBACK: === 第43次回环测试 ===[0m
[0;32mI (130918) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (130978) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (130978) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (130988) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (131008) RS485_LOOPBACK: 📥 接收到 11 字节，总计 11/11[0m
[0;32mI (131008) RS485_LOOPBACK: 📥 接收完成: "LOOPBACK_OK" (11字节)[0m
[0;32mI (131008) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (131008) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (131018) RS485_LOOPBACK: 📊 统计: 34/43 成功 (79.1%)[0m
[0;32mI (131018) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (134028) RS485_LOOPBACK: === 第44次回环测试 ===[0m
[0;32mI (134028) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (134088) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (134088) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (134098) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (134118) RS485_LOOPBACK: 📥 接收到 13 字节，总计 13/13[0m
[0;32mI (134118) RS485_LOOPBACK: 📥 接收完成: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (134118) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (134118) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (134128) RS485_LOOPBACK: 📊 统计: 35/44 成功 (79.5%)[0m
[0;32mI (134128) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (137138) RS485_LOOPBACK: === 第45次回环测试 ===[0m
[0;32mI (137138) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (137198) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (137198) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (137208) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (137228) RS485_LOOPBACK: 📥 接收到 16 字节，总计 16/16[0m
[0;32mI (137228) RS485_LOOPBACK: 📥 接收完成: "0123456789ABCDEF" (16字节)[0m
[0;32mI (137228) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (137228) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (137238) RS485_LOOPBACK: 📊 统计: 36/45 成功 (80.0%)[0m
[0;32mI (137238) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (140248) RS485_LOOPBACK: === 第46次回环测试 ===[0m
[0;32mI (140248) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (140308) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (140308) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (140308) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (140328) RS485_LOOPBACK: 📥 接收到 5 字节，总计 5/5[0m
[0;32mI (140328) RS485_LOOPBACK: 📥 接收完成: "HELLO" (5字节)[0m
[0;32mI (140328) RS485_LOOPBACK: ⏱️  总耗时: 20 ms[0m
[0;32mI (140328) RS485_LOOPBACK: ⚠️  数据匹配但时间过短(20.0ms)，可能是缓冲区残留[0m
[0;32mI (140338) RS485_LOOPBACK: 📊 统计: 36/46 成功 (78.3%)[0m
[0;32mI (140338) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (143348) RS485_LOOPBACK: === 第47次回环测试 ===[0m
[0;32mI (143348) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (143408) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (143408) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (143418) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (143438) RS485_LOOPBACK: 📥 接收到 10 字节，总计 10/10[0m
[0;32mI (143438) RS485_LOOPBACK: 📥 接收完成: "RS485_TEST" (10字节)[0m
[0;32mI (143438) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (143438) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (143448) RS485_LOOPBACK: 📊 统计: 37/47 成功 (78.7%)[0m
[0;32mI (143448) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (146458) RS485_LOOPBACK: === 第48次回环测试 ===[0m
[0;32mI (146458) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (146518) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (146518) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (146528) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (146548) RS485_LOOPBACK: 📥 接收到 11 字节，总计 11/11[0m
[0;32mI (146548) RS485_LOOPBACK: 📥 接收完成: "LOOPBACK_OK" (11字节)[0m
[0;32mI (146548) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (146548) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (146558) RS485_LOOPBACK: 📊 统计: 38/48 成功 (79.2%)[0m
[0;32mI (146558) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (149568) RS485_LOOPBACK: === 第49次回环测试 ===[0m
[0;32mI (149568) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (149628) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (149628) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (149638) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (149658) RS485_LOOPBACK: 📥 接收到 13 字节，总计 13/13[0m
[0;32mI (149658) RS485_LOOPBACK: 📥 接收完成: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (149658) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (149658) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (149668) RS485_LOOPBACK: 📊 统计: 39/49 成功 (79.6%)[0m
[0;32mI (149668) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (152678) RS485_LOOPBACK: === 第50次回环测试 ===[0m
[0;32mI (152678) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (152738) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (152738) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (152748) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (152768) RS485_LOOPBACK: 📥 接收到 16 字节，总计 16/16[0m
[0;32mI (152768) RS485_LOOPBACK: 📥 接收完成: "0123456789ABCDEF" (16字节)[0m
[0;32mI (152768) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (152768) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (152778) RS485_LOOPBACK: 📊 统计: 40/50 成功 (80.0%)[0m
[0;32mI (152778) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (155788) RS485_LOOPBACK: === 第51次回环测试 ===[0m
[0;32mI (155788) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (155848) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (155848) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (155848) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (155868) RS485_LOOPBACK: 📥 接收到 5 字节，总计 5/5[0m
[0;32mI (155868) RS485_LOOPBACK: 📥 接收完成: "HELLO" (5字节)[0m
[0;32mI (155868) RS485_LOOPBACK: ⏱️  总耗时: 20 ms[0m
[0;32mI (155868) RS485_LOOPBACK: ⚠️  数据匹配但时间过短(20.0ms)，可能是缓冲区残留[0m
[0;32mI (155878) RS485_LOOPBACK: 📊 统计: 40/51 成功 (78.4%)[0m
[0;32mI (155878) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (158888) RS485_LOOPBACK: === 第52次回环测试 ===[0m
[0;32mI (158888) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (158948) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (158948) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (158958) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (158978) RS485_LOOPBACK: 📥 接收到 10 字节，总计 10/10[0m
[0;32mI (158978) RS485_LOOPBACK: 📥 接收完成: "RS485_TEST" (10字节)[0m
[0;32mI (158978) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (158978) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (158988) RS485_LOOPBACK: 📊 统计: 41/52 成功 (78.8%)[0m
[0;32mI (158988) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (161998) RS485_LOOPBACK: === 第53次回环测试 ===[0m
[0;32mI (161998) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (162058) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (162058) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (162068) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (162088) RS485_LOOPBACK: 📥 接收到 11 字节，总计 11/11[0m
[0;32mI (162088) RS485_LOOPBACK: 📥 接收完成: "LOOPBACK_OK" (11字节)[0m
[0;32mI (162088) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (162088) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (162098) RS485_LOOPBACK: 📊 统计: 42/53 成功 (79.2%)[0m
[0;32mI (162098) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (165108) RS485_LOOPBACK: === 第54次回环测试 ===[0m
[0;32mI (165108) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (165168) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (165168) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (165178) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (165198) RS485_LOOPBACK: 📥 接收到 13 字节，总计 13/13[0m
[0;32mI (165198) RS485_LOOPBACK: 📥 接收完成: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (165198) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (165198) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (165208) RS485_LOOPBACK: 📊 统计: 43/54 成功 (79.6%)[0m
[0;32mI (165208) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (168218) RS485_LOOPBACK: === 第55次回环测试 ===[0m
[0;32mI (168218) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (168278) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (168278) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (168288) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (168308) RS485_LOOPBACK: 📥 接收到 16 字节，总计 16/16[0m
[0;32mI (168308) RS485_LOOPBACK: 📥 接收完成: "0123456789ABCDEF" (16字节)[0m
[0;32mI (168308) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (168308) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (168318) RS485_LOOPBACK: 📊 统计: 44/55 成功 (80.0%)[0m
[0;32mI (168318) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (171328) RS485_LOOPBACK: === 第56次回环测试 ===[0m
[0;32mI (171328) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (171388) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (171388) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (171388) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (171408) RS485_LOOPBACK: 📥 接收到 5 字节，总计 5/5[0m
[0;32mI (171408) RS485_LOOPBACK: 📥 接收完成: "HELLO" (5字节)[0m
[0;32mI (171408) RS485_LOOPBACK: ⏱️  总耗时: 20 ms[0m
[0;32mI (171408) RS485_LOOPBACK: ⚠️  数据匹配但时间过短(20.0ms)，可能是缓冲区残留[0m
[0;32mI (171418) RS485_LOOPBACK: 📊 统计: 44/56 成功 (78.6%)[0m
[0;32mI (171418) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (174428) RS485_LOOPBACK: === 第57次回环测试 ===[0m
[0;32mI (174428) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (174488) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (174488) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (174498) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (174518) RS485_LOOPBACK: 📥 接收到 10 字节，总计 10/10[0m
[0;32mI (174518) RS485_LOOPBACK: 📥 接收完成: "RS485_TEST" (10字节)[0m
[0;32mI (174518) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (174518) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (174528) RS485_LOOPBACK: 📊 统计: 45/57 成功 (78.9%)[0m
[0;32mI (174528) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (177538) RS485_LOOPBACK: === 第58次回环测试 ===[0m
[0;32mI (177538) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (177598) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (177598) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (177608) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (177628) RS485_LOOPBACK: 📥 接收到 11 字节，总计 11/11[0m
[0;32mI (177628) RS485_LOOPBACK: 📥 接收完成: "LOOPBACK_OK" (11字节)[0m
[0;32mI (177628) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (177628) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (177638) RS485_LOOPBACK: 📊 统计: 46/58 成功 (79.3%)[0m
[0;32mI (177638) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (180648) RS485_LOOPBACK: === 第59次回环测试 ===[0m
[0;32mI (180648) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (180708) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (180708) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (180718) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (180738) RS485_LOOPBACK: 📥 接收到 13 字节，总计 13/13[0m
[0;32mI (180738) RS485_LOOPBACK: 📥 接收完成: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (180738) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (180738) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (180748) RS485_LOOPBACK: 📊 统计: 47/59 成功 (79.7%)[0m
[0;32mI (180748) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (183758) RS485_LOOPBACK: === 第60次回环测试 ===[0m
[0;32mI (183758) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (183818) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (183818) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (183828) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (183848) RS485_LOOPBACK: 📥 接收到 16 字节，总计 16/16[0m
[0;32mI (183848) RS485_LOOPBACK: 📥 接收完成: "0123456789ABCDEF" (16字节)[0m
[0;32mI (183848) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (183848) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (183858) RS485_LOOPBACK: 📊 统计: 48/60 成功 (80.0%)[0m
[0;32mI (183858) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (186868) RS485_LOOPBACK: === 第61次回环测试 ===[0m
[0;32mI (186868) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (186928) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (186928) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (186928) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (186948) RS485_LOOPBACK: 📥 接收到 5 字节，总计 5/5[0m
[0;32mI (186948) RS485_LOOPBACK: 📥 接收完成: "HELLO" (5字节)[0m
[0;32mI (186948) RS485_LOOPBACK: ⏱️  总耗时: 20 ms[0m
[0;32mI (186948) RS485_LOOPBACK: ⚠️  数据匹配但时间过短(20.0ms)，可能是缓冲区残留[0m
[0;32mI (186958) RS485_LOOPBACK: 📊 统计: 48/61 成功 (78.7%)[0m
[0;32mI (186958) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (189968) RS485_LOOPBACK: === 第62次回环测试 ===[0m
[0;32mI (189968) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (190028) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (190028) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (190038) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (190058) RS485_LOOPBACK: 📥 接收到 10 字节，总计 10/10[0m
[0;32mI (190058) RS485_LOOPBACK: 📥 接收完成: "RS485_TEST" (10字节)[0m
[0;32mI (190058) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (190058) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (190068) RS485_LOOPBACK: 📊 统计: 49/62 成功 (79.0%)[0m
[0;32mI (190068) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (193078) RS485_LOOPBACK: === 第63次回环测试 ===[0m
[0;32mI (193078) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (193138) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (193138) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (193148) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (193168) RS485_LOOPBACK: 📥 接收到 11 字节，总计 11/11[0m
[0;32mI (193168) RS485_LOOPBACK: 📥 接收完成: "LOOPBACK_OK" (11字节)[0m
[0;32mI (193168) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (193168) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (193178) RS485_LOOPBACK: 📊 统计: 50/63 成功 (79.4%)[0m
[0;32mI (193178) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (196188) RS485_LOOPBACK: === 第64次回环测试 ===[0m
[0;32mI (196188) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (196248) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (196248) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (196258) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (196278) RS485_LOOPBACK: 📥 接收到 13 字节，总计 13/13[0m
[0;32mI (196278) RS485_LOOPBACK: 📥 接收完成: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (196278) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (196278) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (196288) RS485_LOOPBACK: 📊 统计: 51/64 成功 (79.7%)[0m
[0;32mI (196288) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (199298) RS485_LOOPBACK: === 第65次回环测试 ===[0m
[0;32mI (199298) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (199358) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (199358) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (199368) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (199388) RS485_LOOPBACK: 📥 接收到 16 字节，总计 16/16[0m
[0;32mI (199388) RS485_LOOPBACK: 📥 接收完成: "0123456789ABCDEF" (16字节)[0m
[0;32mI (199388) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (199388) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (199398) RS485_LOOPBACK: 📊 统计: 52/65 成功 (80.0%)[0m
[0;32mI (199398) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (202408) RS485_LOOPBACK: === 第66次回环测试 ===[0m
[0;32mI (202408) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (202468) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (202468) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (202468) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (202488) RS485_LOOPBACK: 📥 接收到 5 字节，总计 5/5[0m
[0;32mI (202488) RS485_LOOPBACK: 📥 接收完成: "HELLO" (5字节)[0m
[0;32mI (202488) RS485_LOOPBACK: ⏱️  总耗时: 20 ms[0m
[0;32mI (202488) RS485_LOOPBACK: ⚠️  数据匹配但时间过短(20.0ms)，可能是缓冲区残留[0m
[0;32mI (202498) RS485_LOOPBACK: 📊 统计: 52/66 成功 (78.8%)[0m
[0;32mI (202498) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (205508) RS485_LOOPBACK: === 第67次回环测试 ===[0m
[0;32mI (205508) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (205568) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (205568) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (205578) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (205598) RS485_LOOPBACK: 📥 接收到 10 字节，总计 10/10[0m
[0;32mI (205598) RS485_LOOPBACK: 📥 接收完成: "RS485_TEST" (10字节)[0m
[0;32mI (205598) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (205598) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (205608) RS485_LOOPBACK: 📊 统计: 53/67 成功 (79.1%)[0m
[0;32mI (205608) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (208618) RS485_LOOPBACK: === 第68次回环测试 ===[0m
[0;32mI (208618) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (208678) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (208678) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (208688) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (208708) RS485_LOOPBACK: 📥 接收到 11 字节，总计 11/11[0m
[0;32mI (208708) RS485_LOOPBACK: 📥 接收完成: "LOOPBACK_OK" (11字节)[0m
[0;32mI (208708) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (208708) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (208718) RS485_LOOPBACK: 📊 统计: 54/68 成功 (79.4%)[0m
[0;32mI (208718) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (211728) RS485_LOOPBACK: === 第69次回环测试 ===[0m
[0;32mI (211728) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (211788) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (211788) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (211798) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (211818) RS485_LOOPBACK: 📥 接收到 13 字节，总计 13/13[0m
[0;32mI (211818) RS485_LOOPBACK: 📥 接收完成: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (211818) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (211818) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (211828) RS485_LOOPBACK: 📊 统计: 55/69 成功 (79.7%)[0m
[0;32mI (211828) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (214838) RS485_LOOPBACK: === 第70次回环测试 ===[0m
[0;32mI (214838) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (214898) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (214898) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (214908) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (214928) RS485_LOOPBACK: 📥 接收到 16 字节，总计 16/16[0m
[0;32mI (214928) RS485_LOOPBACK: 📥 接收完成: "0123456789ABCDEF" (16字节)[0m
[0;32mI (214928) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (214928) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (214938) RS485_LOOPBACK: 📊 统计: 56/70 成功 (80.0%)[0m
[0;32mI (214938) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (217948) RS485_LOOPBACK: === 第71次回环测试 ===[0m
[0;32mI (217948) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (218008) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (218008) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (218008) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (218028) RS485_LOOPBACK: 📥 接收到 5 字节，总计 5/5[0m
[0;32mI (218028) RS485_LOOPBACK: 📥 接收完成: "HELLO" (5字节)[0m
[0;32mI (218028) RS485_LOOPBACK: ⏱️  总耗时: 20 ms[0m
[0;32mI (218028) RS485_LOOPBACK: ⚠️  数据匹配但时间过短(20.0ms)，可能是缓冲区残留[0m
[0;32mI (218038) RS485_LOOPBACK: 📊 统计: 56/71 成功 (78.9%)[0m
[0;32mI (218038) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (221048) RS485_LOOPBACK: === 第72次回环测试 ===[0m
[0;32mI (221048) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (221108) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (221108) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (221118) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (221138) RS485_LOOPBACK: 📥 接收到 10 字节，总计 10/10[0m
[0;32mI (221138) RS485_LOOPBACK: 📥 接收完成: "RS485_TEST" (10字节)[0m
[0;32mI (221138) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (221138) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (221148) RS485_LOOPBACK: 📊 统计: 57/72 成功 (79.2%)[0m
[0;32mI (221148) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (224158) RS485_LOOPBACK: === 第73次回环测试 ===[0m
[0;32mI (224158) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (224218) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (224218) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (224228) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (224248) RS485_LOOPBACK: 📥 接收到 11 字节，总计 11/11[0m
[0;32mI (224248) RS485_LOOPBACK: 📥 接收完成: "LOOPBACK_OK" (11字节)[0m
[0;32mI (224248) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (224248) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (224258) RS485_LOOPBACK: 📊 统计: 58/73 成功 (79.5%)[0m
[0;32mI (224258) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (227268) RS485_LOOPBACK: === 第74次回环测试 ===[0m
[0;32mI (227268) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (227328) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (227328) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (227338) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (227358) RS485_LOOPBACK: 📥 接收到 13 字节，总计 13/13[0m
[0;32mI (227358) RS485_LOOPBACK: 📥 接收完成: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (227358) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (227358) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (227368) RS485_LOOPBACK: 📊 统计: 59/74 成功 (79.7%)[0m
[0;32mI (227368) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (230378) RS485_LOOPBACK: === 第75次回环测试 ===[0m
[0;32mI (230378) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (230438) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (230438) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (230448) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (230468) RS485_LOOPBACK: 📥 接收到 16 字节，总计 16/16[0m
[0;32mI (230468) RS485_LOOPBACK: 📥 接收完成: "0123456789ABCDEF" (16字节)[0m
[0;32mI (230468) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (230468) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (230478) RS485_LOOPBACK: 📊 统计: 60/75 成功 (80.0%)[0m
[0;32mI (230478) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (233488) RS485_LOOPBACK: === 第76次回环测试 ===[0m
[0;32mI (233488) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (233548) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (233548) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (233548) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (233568) RS485_LOOPBACK: 📥 接收到 5 字节，总计 5/5[0m
[0;32mI (233568) RS485_LOOPBACK: 📥 接收完成: "HELLO" (5字节)[0m
[0;32mI (233568) RS485_LOOPBACK: ⏱️  总耗时: 20 ms[0m
[0;32mI (233568) RS485_LOOPBACK: ⚠️  数据匹配但时间过短(20.0ms)，可能是缓冲区残留[0m
[0;32mI (233578) RS485_LOOPBACK: 📊 统计: 60/76 成功 (78.9%)[0m
[0;32mI (233578) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (236588) RS485_LOOPBACK: === 第77次回环测试 ===[0m
[0;32mI (236588) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (236648) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (236648) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (236658) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (236678) RS485_LOOPBACK: 📥 接收到 10 字节，总计 10/10[0m
[0;32mI (236678) RS485_LOOPBACK: 📥 接收完成: "RS485_TEST" (10字节)[0m
[0;32mI (236678) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (236678) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (236688) RS485_LOOPBACK: 📊 统计: 61/77 成功 (79.2%)[0m
[0;32mI (236688) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (239698) RS485_LOOPBACK: === 第78次回环测试 ===[0m
[0;32mI (239698) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (239758) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (239758) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (239768) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (239788) RS485_LOOPBACK: 📥 接收到 11 字节，总计 11/11[0m
[0;32mI (239788) RS485_LOOPBACK: 📥 接收完成: "LOOPBACK_OK" (11字节)[0m
[0;32mI (239788) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (239788) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (239798) RS485_LOOPBACK: 📊 统计: 62/78 成功 (79.5%)[0m
[0;32mI (239798) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (242808) RS485_LOOPBACK: === 第79次回环测试 ===[0m
[0;32mI (242808) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (242868) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (242868) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (242878) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (242898) RS485_LOOPBACK: 📥 接收到 13 字节，总计 13/13[0m
[0;32mI (242898) RS485_LOOPBACK: 📥 接收完成: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (242898) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (242898) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (242908) RS485_LOOPBACK: 📊 统计: 63/79 成功 (79.7%)[0m
[0;32mI (242908) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (245918) RS485_LOOPBACK: === 第80次回环测试 ===[0m
[0;32mI (245918) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (245978) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (245978) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (245988) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (246008) RS485_LOOPBACK: 📥 接收到 16 字节，总计 16/16[0m
[0;32mI (246008) RS485_LOOPBACK: 📥 接收完成: "0123456789ABCDEF" (16字节)[0m
[0;32mI (246008) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (246008) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (246018) RS485_LOOPBACK: 📊 统计: 64/80 成功 (80.0%)[0m
[0;32mI (246018) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (249028) RS485_LOOPBACK: === 第81次回环测试 ===[0m
[0;32mI (249028) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (249088) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (249088) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (249088) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (249108) RS485_LOOPBACK: 📥 接收到 5 字节，总计 5/5[0m
[0;32mI (249108) RS485_LOOPBACK: 📥 接收完成: "HELLO" (5字节)[0m
[0;32mI (249108) RS485_LOOPBACK: ⏱️  总耗时: 20 ms[0m
[0;32mI (249108) RS485_LOOPBACK: ⚠️  数据匹配但时间过短(20.0ms)，可能是缓冲区残留[0m
[0;32mI (249118) RS485_LOOPBACK: 📊 统计: 64/81 成功 (79.0%)[0m
[0;32mI (249118) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (252128) RS485_LOOPBACK: === 第82次回环测试 ===[0m
[0;32mI (252128) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (252188) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (252188) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (252198) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (252218) RS485_LOOPBACK: 📥 接收到 10 字节，总计 10/10[0m
[0;32mI (252218) RS485_LOOPBACK: 📥 接收完成: "RS485_TEST" (10字节)[0m
[0;32mI (252218) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (252218) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (252228) RS485_LOOPBACK: 📊 统计: 65/82 成功 (79.3%)[0m
[0;32mI (252228) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (255238) RS485_LOOPBACK: === 第83次回环测试 ===[0m
[0;32mI (255238) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (255298) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (255298) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (255308) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (255328) RS485_LOOPBACK: 📥 接收到 11 字节，总计 11/11[0m
[0;32mI (255328) RS485_LOOPBACK: 📥 接收完成: "LOOPBACK_OK" (11字节)[0m
[0;32mI (255328) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (255328) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (255338) RS485_LOOPBACK: 📊 统计: 66/83 成功 (79.5%)[0m
[0;32mI (255338) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (258348) RS485_LOOPBACK: === 第84次回环测试 ===[0m
[0;32mI (258348) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (258408) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (258408) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (258418) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (258438) RS485_LOOPBACK: 📥 接收到 13 字节，总计 13/13[0m
[0;32mI (258438) RS485_LOOPBACK: 📥 接收完成: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (258438) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (258438) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (258448) RS485_LOOPBACK: 📊 统计: 67/84 成功 (79.8%)[0m
[0;32mI (258448) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (261458) RS485_LOOPBACK: === 第85次回环测试 ===[0m
[0;32mI (261458) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (261518) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (261518) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (261528) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (261548) RS485_LOOPBACK: 📥 接收到 16 字节，总计 16/16[0m
[0;32mI (261548) RS485_LOOPBACK: 📥 接收完成: "0123456789ABCDEF" (16字节)[0m
[0;32mI (261548) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (261548) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (261558) RS485_LOOPBACK: 📊 统计: 68/85 成功 (80.0%)[0m
[0;32mI (261558) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (264568) RS485_LOOPBACK: === 第86次回环测试 ===[0m
[0;32mI (264568) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (264628) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (264628) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (264628) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (264648) RS485_LOOPBACK: 📥 接收到 5 字节，总计 5/5[0m
[0;32mI (264648) RS485_LOOPBACK: 📥 接收完成: "HELLO" (5字节)[0m
[0;32mI (264648) RS485_LOOPBACK: ⏱️  总耗时: 20 ms[0m
[0;32mI (264648) RS485_LOOPBACK: ⚠️  数据匹配但时间过短(20.0ms)，可能是缓冲区残留[0m
[0;32mI (264658) RS485_LOOPBACK: 📊 统计: 68/86 成功 (79.1%)[0m
[0;32mI (264658) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (267668) RS485_LOOPBACK: === 第87次回环测试 ===[0m
[0;32mI (267668) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (267728) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (267728) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (267738) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (267758) RS485_LOOPBACK: 📥 接收到 10 字节，总计 10/10[0m
[0;32mI (267758) RS485_LOOPBACK: 📥 接收完成: "RS485_TEST" (10字节)[0m
[0;32mI (267758) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (267758) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (267768) RS485_LOOPBACK: 📊 统计: 69/87 成功 (79.3%)[0m
[0;32mI (267768) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (270778) RS485_LOOPBACK: === 第88次回环测试 ===[0m
[0;32mI (270778) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (270838) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (270838) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (270848) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (270868) RS485_LOOPBACK: 📥 接收到 11 字节，总计 11/11[0m
[0;32mI (270868) RS485_LOOPBACK: 📥 接收完成: "LOOPBACK_OK" (11字节)[0m
[0;32mI (270868) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (270868) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (270878) RS485_LOOPBACK: 📊 统计: 70/88 成功 (79.5%)[0m
[0;32mI (270878) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (273888) RS485_LOOPBACK: === 第89次回环测试 ===[0m
[0;32mI (273888) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (273948) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (273948) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (273958) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (273978) RS485_LOOPBACK: 📥 接收到 13 字节，总计 13/13[0m
[0;32mI (273978) RS485_LOOPBACK: 📥 接收完成: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (273978) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (273978) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (273988) RS485_LOOPBACK: 📊 统计: 71/89 成功 (79.8%)[0m
[0;32mI (273988) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (276998) RS485_LOOPBACK: === 第90次回环测试 ===[0m
[0;32mI (276998) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (277058) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (277058) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (277068) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (277088) RS485_LOOPBACK: 📥 接收到 16 字节，总计 16/16[0m
[0;32mI (277088) RS485_LOOPBACK: 📥 接收完成: "0123456789ABCDEF" (16字节)[0m
[0;32mI (277088) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (277088) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (277098) RS485_LOOPBACK: 📊 统计: 72/90 成功 (80.0%)[0m
[0;32mI (277098) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (280108) RS485_LOOPBACK: === 第91次回环测试 ===[0m
[0;32mI (280108) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (280168) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (280168) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (280168) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (280188) RS485_LOOPBACK: 📥 接收到 5 字节，总计 5/5[0m
[0;32mI (280188) RS485_LOOPBACK: 📥 接收完成: "HELLO" (5字节)[0m
[0;32mI (280188) RS485_LOOPBACK: ⏱️  总耗时: 20 ms[0m
[0;32mI (280188) RS485_LOOPBACK: ⚠️  数据匹配但时间过短(20.0ms)，可能是缓冲区残留[0m
[0;32mI (280198) RS485_LOOPBACK: 📊 统计: 72/91 成功 (79.1%)[0m
[0;32mI (280198) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (283208) RS485_LOOPBACK: === 第92次回环测试 ===[0m
[0;32mI (283208) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (283268) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (283268) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (283278) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (283298) RS485_LOOPBACK: 📥 接收到 10 字节，总计 10/10[0m
[0;32mI (283298) RS485_LOOPBACK: 📥 接收完成: "RS485_TEST" (10字节)[0m
[0;32mI (283298) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (283298) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (283308) RS485_LOOPBACK: 📊 统计: 73/92 成功 (79.3%)[0m
[0;32mI (283308) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (286318) RS485_LOOPBACK: === 第93次回环测试 ===[0m
[0;32mI (286318) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (286378) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (286378) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (286388) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (286408) RS485_LOOPBACK: 📥 接收到 11 字节，总计 11/11[0m
[0;32mI (286408) RS485_LOOPBACK: 📥 接收完成: "LOOPBACK_OK" (11字节)[0m
[0;32mI (286408) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (286408) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (286418) RS485_LOOPBACK: 📊 统计: 74/93 成功 (79.6%)[0m
[0;32mI (286418) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (289428) RS485_LOOPBACK: === 第94次回环测试 ===[0m
[0;32mI (289428) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (289488) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (289488) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (289498) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (289518) RS485_LOOPBACK: 📥 接收到 13 字节，总计 13/13[0m
[0;32mI (289518) RS485_LOOPBACK: 📥 接收完成: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (289518) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (289518) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (289528) RS485_LOOPBACK: 📊 统计: 75/94 成功 (79.8%)[0m
[0;32mI (289528) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (292538) RS485_LOOPBACK: === 第95次回环测试 ===[0m
[0;32mI (292538) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (292598) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (292598) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (292608) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (292628) RS485_LOOPBACK: 📥 接收到 16 字节，总计 16/16[0m
[0;32mI (292628) RS485_LOOPBACK: 📥 接收完成: "0123456789ABCDEF" (16字节)[0m
[0;32mI (292628) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (292628) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (292638) RS485_LOOPBACK: 📊 统计: 76/95 成功 (80.0%)[0m
[0;32mI (292638) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (295648) RS485_LOOPBACK: === 第96次回环测试 ===[0m
[0;32mI (295648) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (295708) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (295708) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (295708) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (295728) RS485_LOOPBACK: 📥 接收到 5 字节，总计 5/5[0m
[0;32mI (295728) RS485_LOOPBACK: 📥 接收完成: "HELLO" (5字节)[0m
[0;32mI (295728) RS485_LOOPBACK: ⏱️  总耗时: 20 ms[0m
[0;32mI (295728) RS485_LOOPBACK: ⚠️  数据匹配但时间过短(20.0ms)，可能是缓冲区残留[0m
[0;32mI (295738) RS485_LOOPBACK: 📊 统计: 76/96 成功 (79.2%)[0m
[0;32mI (295738) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (298748) RS485_LOOPBACK: === 第97次回环测试 ===[0m
[0;32mI (298748) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (298808) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (298808) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (298818) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (298838) RS485_LOOPBACK: 📥 接收到 10 字节，总计 10/10[0m
[0;32mI (298838) RS485_LOOPBACK: 📥 接收完成: "RS485_TEST" (10字节)[0m
[0;32mI (298838) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (298838) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (298848) RS485_LOOPBACK: 📊 统计: 77/97 成功 (79.4%)[0m
[0;32mI (298848) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (301858) RS485_LOOPBACK: === 第98次回环测试 ===[0m
[0;32mI (301858) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (301918) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (301918) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (301928) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (301948) RS485_LOOPBACK: 📥 接收到 11 字节，总计 11/11[0m
[0;32mI (301948) RS485_LOOPBACK: 📥 接收完成: "LOOPBACK_OK" (11字节)[0m
[0;32mI (301948) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (301948) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (301958) RS485_LOOPBACK: 📊 统计: 78/98 成功 (79.6%)[0m
[0;32mI (301958) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (304968) RS485_LOOPBACK: === 第99次回环测试 ===[0m
[0;32mI (304968) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (305028) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (305028) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (305038) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (305058) RS485_LOOPBACK: 📥 接收到 13 字节，总计 13/13[0m
[0;32mI (305058) RS485_LOOPBACK: 📥 接收完成: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (305058) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (305058) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (305068) RS485_LOOPBACK: 📊 统计: 79/99 成功 (79.8%)[0m
[0;32mI (305068) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (308078) RS485_LOOPBACK: === 第100次回环测试 ===[0m
[0;32mI (308078) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (308138) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (308138) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (308148) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (308168) RS485_LOOPBACK: 📥 接收到 16 字节，总计 16/16[0m
[0;32mI (308168) RS485_LOOPBACK: 📥 接收完成: "0123456789ABCDEF" (16字节)[0m
[0;32mI (308168) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (308168) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (308178) RS485_LOOPBACK: 📊 统计: 80/100 成功 (80.0%)[0m
[0;32mI (308178) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (311188) RS485_LOOPBACK: === 第101次回环测试 ===[0m
[0;32mI (311188) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (311248) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (311248) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (311248) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (311268) RS485_LOOPBACK: 📥 接收到 5 字节，总计 5/5[0m
[0;32mI (311268) RS485_LOOPBACK: 📥 接收完成: "HELLO" (5字节)[0m
[0;32mI (311268) RS485_LOOPBACK: ⏱️  总耗时: 20 ms[0m
[0;32mI (311268) RS485_LOOPBACK: ⚠️  数据匹配但时间过短(20.0ms)，可能是缓冲区残留[0m
[0;32mI (311278) RS485_LOOPBACK: 📊 统计: 80/101 成功 (79.2%)[0m
[0;32mI (311278) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (314288) RS485_LOOPBACK: === 第102次回环测试 ===[0m
[0;32mI (314288) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (314348) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (314348) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (314358) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (314378) RS485_LOOPBACK: 📥 接收到 10 字节，总计 10/10[0m
[0;32mI (314378) RS485_LOOPBACK: 📥 接收完成: "RS485_TEST" (10字节)[0m
[0;32mI (314378) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (314378) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (314388) RS485_LOOPBACK: 📊 统计: 81/102 成功 (79.4%)[0m
[0;32mI (314388) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (317398) RS485_LOOPBACK: === 第103次回环测试 ===[0m
[0;32mI (317398) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (317458) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (317458) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (317468) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (317488) RS485_LOOPBACK: 📥 接收到 11 字节，总计 11/11[0m
[0;32mI (317488) RS485_LOOPBACK: 📥 接收完成: "LOOPBACK_OK" (11字节)[0m
[0;32mI (317488) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (317488) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (317498) RS485_LOOPBACK: 📊 统计: 82/103 成功 (79.6%)[0m
[0;32mI (317498) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (320508) RS485_LOOPBACK: === 第104次回环测试 ===[0m
[0;32mI (320508) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (320568) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (320568) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (320578) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (320598) RS485_LOOPBACK: 📥 接收到 13 字节，总计 13/13[0m
[0;32mI (320598) RS485_LOOPBACK: 📥 接收完成: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (320598) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (320598) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (320608) RS485_LOOPBACK: 📊 统计: 83/104 成功 (79.8%)[0m
[0;32mI (320608) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (323618) RS485_LOOPBACK: === 第105次回环测试 ===[0m
[0;32mI (323618) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (323678) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (323678) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (323688) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (323708) RS485_LOOPBACK: 📥 接收到 16 字节，总计 16/16[0m
[0;32mI (323708) RS485_LOOPBACK: 📥 接收完成: "0123456789ABCDEF" (16字节)[0m
[0;32mI (323708) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (323708) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (323718) RS485_LOOPBACK: 📊 统计: 84/105 成功 (80.0%)[0m
[0;32mI (323718) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (326728) RS485_LOOPBACK: === 第106次回环测试 ===[0m
[0;32mI (326728) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (326788) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (326788) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (326788) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (326808) RS485_LOOPBACK: 📥 接收到 5 字节，总计 5/5[0m
[0;32mI (326808) RS485_LOOPBACK: 📥 接收完成: "HELLO" (5字节)[0m
[0;32mI (326808) RS485_LOOPBACK: ⏱️  总耗时: 20 ms[0m
[0;32mI (326808) RS485_LOOPBACK: ⚠️  数据匹配但时间过短(20.0ms)，可能是缓冲区残留[0m
[0;32mI (326818) RS485_LOOPBACK: 📊 统计: 84/106 成功 (79.2%)[0m
[0;32mI (326818) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (329828) RS485_LOOPBACK: === 第107次回环测试 ===[0m
[0;32mI (329828) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (329888) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (329888) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (329898) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (329918) RS485_LOOPBACK: 📥 接收到 10 字节，总计 10/10[0m
[0;32mI (329918) RS485_LOOPBACK: 📥 接收完成: "RS485_TEST" (10字节)[0m
[0;32mI (329918) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (329918) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (329928) RS485_LOOPBACK: 📊 统计: 85/107 成功 (79.4%)[0m
[0;32mI (329928) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (332938) RS485_LOOPBACK: === 第108次回环测试 ===[0m
[0;32mI (332938) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (332998) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (332998) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (333008) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (333028) RS485_LOOPBACK: 📥 接收到 11 字节，总计 11/11[0m
[0;32mI (333028) RS485_LOOPBACK: 📥 接收完成: "LOOPBACK_OK" (11字节)[0m
[0;32mI (333028) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (333028) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (333038) RS485_LOOPBACK: 📊 统计: 86/108 成功 (79.6%)[0m
[0;32mI (333038) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (336048) RS485_LOOPBACK: === 第109次回环测试 ===[0m
[0;32mI (336048) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (336108) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (336108) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (336118) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (336138) RS485_LOOPBACK: 📥 接收到 13 字节，总计 13/13[0m
[0;32mI (336138) RS485_LOOPBACK: 📥 接收完成: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (336138) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (336138) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (336148) RS485_LOOPBACK: 📊 统计: 87/109 成功 (79.8%)[0m
[0;32mI (336148) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (339158) RS485_LOOPBACK: === 第110次回环测试 ===[0m
[0;32mI (339158) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (339218) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (339218) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (339228) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (339248) RS485_LOOPBACK: 📥 接收到 16 字节，总计 16/16[0m
[0;32mI (339248) RS485_LOOPBACK: 📥 接收完成: "0123456789ABCDEF" (16字节)[0m
[0;32mI (339248) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (339248) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (339258) RS485_LOOPBACK: 📊 统计: 88/110 成功 (80.0%)[0m
[0;32mI (339258) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (342268) RS485_LOOPBACK: === 第111次回环测试 ===[0m
[0;32mI (342268) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (342328) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (342328) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (342328) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (342348) RS485_LOOPBACK: 📥 接收到 5 字节，总计 5/5[0m
[0;32mI (342348) RS485_LOOPBACK: 📥 接收完成: "HELLO" (5字节)[0m
[0;32mI (342348) RS485_LOOPBACK: ⏱️  总耗时: 20 ms[0m
[0;32mI (342348) RS485_LOOPBACK: ⚠️  数据匹配但时间过短(20.0ms)，可能是缓冲区残留[0m
[0;32mI (342358) RS485_LOOPBACK: 📊 统计: 88/111 成功 (79.3%)[0m
[0;32mI (342358) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (345368) RS485_LOOPBACK: === 第112次回环测试 ===[0m
[0;32mI (345368) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (345428) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (345428) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (345438) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (345458) RS485_LOOPBACK: 📥 接收到 10 字节，总计 10/10[0m
[0;32mI (345458) RS485_LOOPBACK: 📥 接收完成: "RS485_TEST" (10字节)[0m
[0;32mI (345458) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (345458) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (345468) RS485_LOOPBACK: 📊 统计: 89/112 成功 (79.5%)[0m
[0;32mI (345468) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (348478) RS485_LOOPBACK: === 第113次回环测试 ===[0m
[0;32mI (348478) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (348538) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (348538) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (348548) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (348568) RS485_LOOPBACK: 📥 接收到 11 字节，总计 11/11[0m
[0;32mI (348568) RS485_LOOPBACK: 📥 接收完成: "LOOPBACK_OK" (11字节)[0m
[0;32mI (348568) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (348568) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (348578) RS485_LOOPBACK: 📊 统计: 90/113 成功 (79.6%)[0m
[0;32mI (348578) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (351588) RS485_LOOPBACK: === 第114次回环测试 ===[0m
[0;32mI (351588) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (351648) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (351648) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (351658) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (351678) RS485_LOOPBACK: 📥 接收到 13 字节，总计 13/13[0m
[0;32mI (351678) RS485_LOOPBACK: 📥 接收完成: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (351678) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (351678) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (351688) RS485_LOOPBACK: 📊 统计: 91/114 成功 (79.8%)[0m
[0;32mI (351688) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (354698) RS485_LOOPBACK: === 第115次回环测试 ===[0m
[0;32mI (354698) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (354758) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (354758) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (354768) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (354788) RS485_LOOPBACK: 📥 接收到 16 字节，总计 16/16[0m
[0;32mI (354788) RS485_LOOPBACK: 📥 接收完成: "0123456789ABCDEF" (16字节)[0m
[0;32mI (354788) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (354788) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (354798) RS485_LOOPBACK: 📊 统计: 92/115 成功 (80.0%)[0m
[0;32mI (354798) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (357808) RS485_LOOPBACK: === 第116次回环测试 ===[0m
[0;32mI (357808) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (357868) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (357868) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (357868) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (357888) RS485_LOOPBACK: 📥 接收到 5 字节，总计 5/5[0m
[0;32mI (357888) RS485_LOOPBACK: 📥 接收完成: "HELLO" (5字节)[0m
[0;32mI (357888) RS485_LOOPBACK: ⏱️  总耗时: 20 ms[0m
[0;32mI (357888) RS485_LOOPBACK: ⚠️  数据匹配但时间过短(20.0ms)，可能是缓冲区残留[0m
[0;32mI (357898) RS485_LOOPBACK: 📊 统计: 92/116 成功 (79.3%)[0m
[0;32mI (357898) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (360908) RS485_LOOPBACK: === 第117次回环测试 ===[0m
[0;32mI (360908) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (360968) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (360968) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (360978) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (360998) RS485_LOOPBACK: 📥 接收到 10 字节，总计 10/10[0m
[0;32mI (360998) RS485_LOOPBACK: 📥 接收完成: "RS485_TEST" (10字节)[0m
[0;32mI (360998) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (360998) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (361008) RS485_LOOPBACK: 📊 统计: 93/117 成功 (79.5%)[0m
[0;32mI (361008) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (364018) RS485_LOOPBACK: === 第118次回环测试 ===[0m
[0;32mI (364018) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (364078) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (364078) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (364088) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (364108) RS485_LOOPBACK: 📥 接收到 11 字节，总计 11/11[0m
[0;32mI (364108) RS485_LOOPBACK: 📥 接收完成: "LOOPBACK_OK" (11字节)[0m
[0;32mI (364108) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (364108) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (364118) RS485_LOOPBACK: 📊 统计: 94/118 成功 (79.7%)[0m
[0;32mI (364118) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (367128) RS485_LOOPBACK: === 第119次回环测试 ===[0m
[0;32mI (367128) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (367188) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (367188) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (367198) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (367218) RS485_LOOPBACK: 📥 接收到 13 字节，总计 13/13[0m
[0;32mI (367218) RS485_LOOPBACK: 📥 接收完成: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (367218) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (367218) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (367228) RS485_LOOPBACK: 📊 统计: 95/119 成功 (79.8%)[0m
[0;32mI (367228) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (370238) RS485_LOOPBACK: === 第120次回环测试 ===[0m
[0;32mI (370238) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (370298) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (370298) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (370308) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (370328) RS485_LOOPBACK: 📥 接收到 16 字节，总计 16/16[0m
[0;32mI (370328) RS485_LOOPBACK: 📥 接收完成: "0123456789ABCDEF" (16字节)[0m
[0;32mI (370328) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (370328) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (370338) RS485_LOOPBACK: 📊 统计: 96/120 成功 (80.0%)[0m
[0;32mI (370338) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (373348) RS485_LOOPBACK: === 第121次回环测试 ===[0m
[0;32mI (373348) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (373408) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (373408) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (373408) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (373428) RS485_LOOPBACK: 📥 接收到 5 字节，总计 5/5[0m
[0;32mI (373428) RS485_LOOPBACK: 📥 接收完成: "HELLO" (5字节)[0m
[0;32mI (373428) RS485_LOOPBACK: ⏱️  总耗时: 20 ms[0m
[0;32mI (373428) RS485_LOOPBACK: ⚠️  数据匹配但时间过短(20.0ms)，可能是缓冲区残留[0m
[0;32mI (373438) RS485_LOOPBACK: 📊 统计: 96/121 成功 (79.3%)[0m
[0;32mI (373438) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (376448) RS485_LOOPBACK: === 第122次回环测试 ===[0m
[0;32mI (376448) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (376508) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (376508) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (376518) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (376538) RS485_LOOPBACK: 📥 接收到 10 字节，总计 10/10[0m
[0;32mI (376538) RS485_LOOPBACK: 📥 接收完成: "RS485_TEST" (10字节)[0m
[0;32mI (376538) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (376538) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (376548) RS485_LOOPBACK: 📊 统计: 97/122 成功 (79.5%)[0m
[0;32mI (376548) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (379558) RS485_LOOPBACK: === 第123次回环测试 ===[0m
[0;32mI (379558) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (379618) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (379618) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (379628) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (379648) RS485_LOOPBACK: 📥 接收到 11 字节，总计 11/11[0m
[0;32mI (379648) RS485_LOOPBACK: 📥 接收完成: "LOOPBACK_OK" (11字节)[0m
[0;32mI (379648) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (379648) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (379658) RS485_LOOPBACK: 📊 统计: 98/123 成功 (79.7%)[0m
[0;32mI (379658) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (382668) RS485_LOOPBACK: === 第124次回环测试 ===[0m
[0;32mI (382668) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (382728) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (382728) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (382738) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (382758) RS485_LOOPBACK: 📥 接收到 13 字节，总计 13/13[0m
[0;32mI (382758) RS485_LOOPBACK: 📥 接收完成: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (382758) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (382758) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (382768) RS485_LOOPBACK: 📊 统计: 99/124 成功 (79.8%)[0m
[0;32mI (382768) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (385778) RS485_LOOPBACK: === 第125次回环测试 ===[0m
[0;32mI (385778) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (385838) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (385838) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (385848) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (385868) RS485_LOOPBACK: 📥 接收到 16 字