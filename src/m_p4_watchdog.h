/**
 * ESP32-P4 水质监测系统 - 看门狗模块
 *
 * 提供系统看门狗保护，防止系统死锁和异常
 * 专为ESP32-P4平台设计，高性能线程安全任务管理
 */

#ifndef M_P4_WATCHDOG_H
#define M_P4_WATCHDOG_H

#include "m_p4_system_config.h"
#include "esp_task_wdt.h"
#include "esp_timer.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/semphr.h"

// ==================== 看门狗配置参数 ====================
#define WATCHDOG_TIMEOUT_S 15                // 看门狗超时时间(秒) - 适合复杂系统
#define WATCHDOG_CHECK_INTERVAL_MS 1000      // 看门狗检查间隔(毫秒)

// ==================== 看门狗状态机定义 ====================
#define WATCHDOG_STATE_INIT      0           // 初始化状态
#define WATCHDOG_STATE_RUNNING   1           // 正常运行状态
#define WATCHDOG_STATE_ERROR     3           // 错误状态

// ==================== 任务ID定义 ====================
#define TASK_ID_SENSOR          0            // 传感器任务
#define TASK_ID_WATCHDOG        1            // 看门狗任务
#define TASK_ID_WIFI            2            // WiFi通信任务
#define TASK_ID_BLUETOOTH       3            // 蓝牙通信任务

#define MAX_TASKS               4            // 最大任务数量

// ==================== 任务状态定义 ====================
#define TASK_STATUS_UNKNOWN     0            // 未知状态
#define TASK_STATUS_RUNNING     1            // 正常运行
#define TASK_STATUS_ERROR       2            // 错误状态

// ==================== 任务超时设置 ====================
#define TASK_TIMEOUT_MS         8000         // 任务状态报告超时时间(毫秒)

// ==================== 全局看门狗中断服务函数 ====================
void IRAM_ATTR watchdogResetModule(void* arg);

// ==================== 看门狗任务状态结构体 ====================
typedef struct {
    uint8_t status;                          // 任务状态
    unsigned long lastReportTime;            // 最后报告时间
    char taskName[16];                       // 任务名称
    bool isRegistered;                       // 是否已注册
} WatchdogTaskStatus_t;

// ==================== 看门狗类 ====================
class WatchdogManager {
private:
    // 看门狗定时器句柄
    esp_timer_handle_t _watchdogTimer = NULL;

    // 看门狗状态
    uint8_t _watchdogState = WATCHDOG_STATE_INIT;

    // 任务状态数组
    WatchdogTaskStatus_t _taskStatus[MAX_TASKS];

    // 时间记录
    unsigned long _lastWatchdogCheck = 0;

    // 计数器
    uint8_t _registeredTaskCount = 0;        // 已注册的任务数量

    // 互斥锁
    SemaphoreHandle_t _task_mutex = NULL;

    // 看门狗任务句柄
    TaskHandle_t _watchdog_task_handle = NULL;

    // 声明全局中断处理函数为友元
    friend void watchdogResetModule(void* arg);

    // 看门狗任务函数 - 静态函数
    static void watchdogTaskFunction(void* pvParameters) {
        WatchdogManager* self = (WatchdogManager*)pvParameters;
        self->watchdogTaskImpl();
    }

    // 看门狗任务实现
    void watchdogTaskImpl();

public:
    // 构造函数
    WatchdogManager();

    // 析构函数
    ~WatchdogManager();

    // 初始化看门狗
    bool init();

    // 启动看门狗任务
    bool startTask(int core = 0, int priority = 3, int stackSize = 4096);

    // 喂狗
    void feedWatchdog();

    // 注册任务
    bool registerTask(uint8_t taskId, const char* taskName);

    // 任务报告状态
    bool reportTaskStatus(uint8_t taskId, uint8_t status);

    // 检查所有任务健康状态
    bool checkAllTasksHealth(unsigned long currentTime);

    // 打印任务状态
    void printTaskStatus();

    // 获取当前看门狗状态
    uint8_t getWatchdogState();

    // 获取已注册任务数量
    uint8_t getRegisteredTaskCount();

    // 便捷方法：传感器任务报告状态
    void updateSensorTimestamp();
};

// ==================== 声明全局看门狗管理器实例 ====================
extern WatchdogManager g_watchdog;

#endif // M_P4_WATCHDOG_H
