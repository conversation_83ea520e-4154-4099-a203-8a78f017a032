#include "m_deviceInfoData.h"
#include <string.h>

// ==================== 跨平台辅助函数实现 ====================

// [共用] 获取参数类型的中文名称
const char* getParameterChineseName(SensorParameterType type) {
  switch (type) {
    case ph:
      return "pH值";
    case orp:
      return "氧化还原电位";
    case oxygen:
      return "溶解氧";
    case temperature:
      return "温度";
    case conductivity:
      return "电导率";
    case turbidity:
      return "浊度";
    case salinity:
      return "盐度";
    case calcium:
      return "钙离子";
    case carbonate:
      return "碳酸根";
    case potassium:
      return "钾离子";
    case magnesium:
      return "镁离子";
    case ammonia:
      return "氨氮";
    case nitrite:
      return "亚硝酸盐";
    case nitrate:
      return "硝酸盐";
    case other:
      return "未知";
    default:
      return "未知";
  }
}

// [共用] 获取默认单位
const char* getParameterDefaultUnit(SensorParameterType type) {
  switch (type) {
    case ph:
      return "pH";
    case orp:
      return "mV";
    case oxygen:
      return "mg/L";
    case temperature:
      return "℃";
    case conductivity:
      return "uS/cm";
    case turbidity:
      return "NTU";
    case salinity:
      return "ppt";
    case calcium:
      return "mg/L";
    case carbonate:
      return "mg/L";
    case potassium:
      return "mg/L";
    case magnesium:
      return "mg/L";
    case ammonia:
      return "mg/L";
    case nitrite:
      return "mg/L";
    case nitrate:
      return "mg/L";
    case other:
      return "mg/L";
    default:
      return "";
  }
}

// [共用] 获取传感器名称（带"传感器"后缀）
const char* getSensorName(SensorParameterType type) {
  switch (type) {
    case ph:
      return "pH传感器";
    case temperature:
      return "温度传感器";
    case oxygen:
      return "溶解氧传感器";
    case conductivity:
      return "电导率传感器";
    case orp:
      return "ORP传感器";
    case turbidity:
      return "浊度传感器";
    case salinity:
      return "盐度传感器";
    case calcium:
      return "钙离子传感器";
    case carbonate:
      return "碳酸根传感器";
    case potassium:
      return "钾离子传感器";
    case magnesium:
      return "镁离子传感器";
    case ammonia:
      return "氨氮传感器";
    case nitrite:
      return "亚硝酸盐传感器";
    case nitrate:
      return "硝酸盐传感器";
    default:
      return "未知传感器";
  }
}

// [共用] 获取参数类型名称（兼容旧代码）
String getParameterTypeName(SensorParameterType type) {
  switch(type) {
    case ph: return "pH";
    case orp: return "ORP";
    case oxygen: return "溶解氧";
    case temperature: return "温度";
    case conductivity: return "电导率";
    case turbidity: return "浊度";
    default: return "未知";
  }
}

// [共用] 判断是否需要温度补偿
bool needsTemperatureCompensation(SensorParameterType type) {
  switch (type) {
    case ph:
    case oxygen:
    case conductivity:
    case salinity:
      return true;
    default:
      return false;
  }
}

// [共用] 判断是否需要缓冲服务
bool isBufferingNeeded(const String& tradeName) {
  // OEM溶解氧传感器不需要缓冲服务，因为硬件自带此功能
  if (tradeName.indexOf("OEM") >= 0 && tradeName.indexOf("溶解氧") >= 0) {
    return false;
  }
  
  // 其他传感器默认需要缓冲服务
  return true;
}

// ==================== 预定义传感器数据 ====================

// [共用] 预定义模拟传感器数据
SensorData devices[MAX_DEVICES_INIT] = {
  // 溶解氧传感器
  {
    .id = "1",
    .uuid = "oxygen-sensor-001",
    .name = "溶解氧传感器",
    .tradeName = "OEM溶解氧传感器",
    .parameterType = oxygen,
    .address = 1,
    .value = 0.0,
    .unit = "mg/L",
    .ratio = 0.0,
    .hasTemperature = true,
    .manualTemp = 25.0,
    .autoTempComp = true,
    .updatedAt = 0,
    .createdAt = 0,
    .isConnected = false,
    .registerCount = 42,  // 需要读取42个寄存器，确保能获取到所有数据
    .commandCount = 1,
    .serverIp = "************",
    .fixedPort = 504,
    .serverBasePort = 504,
    .serialChannel = 2,
    .useBufferedAverage = false,   
    .responseTimeMs = 0,
    .baudRate = 3,
    .userName = "管理员",
    .userId = "admin-001",
    .iconName = "scatter_plot_outlined",
    .statusIconName = "water_drop_outlined",
    .statusIconColor = 0xFF00BCD4,
    .status = "手动温补: 26.5℃",
    .enabled = true
  },

  // pH传感器
  {
    .id = "2",
    .uuid = "ph-sensor-001",
    .name = "pH传感器",
    .tradeName = "科瑞兴pH传感器",
    .parameterType = ph,
    .address = 2,
    .value = 7.0,
    .unit = "pH",
    .ratio = 0.0,
    .hasTemperature = true,
    .manualTemp = 25.0,
    .autoTempComp = true,
    .updatedAt = 0,
    .createdAt = 0,
    .isConnected = false,
    .registerCount = 2,
    .commandCount = 2,
    .serverIp = "************",
    .fixedPort = 502,
    .serverBasePort = 502,
    .serialChannel = 0,
    .useBufferedAverage = false,   
    .responseTimeMs = 0,
    .baudRate = 3,
    .userName = "管理员",
    .userId = "admin-001",
    .iconName = "science",
    .statusIconName = "water_drop",
    .statusIconColor = 0xFF00B0FF,
    .status = "自动温补: 25.0℃",
    .enabled = true
  },

  // ORP传感器
  {
    .id = "3",
    .uuid = "orp-sensor-001",
    .name = "ORP传感器",
    .tradeName = "科瑞兴ORP传感器",
    .parameterType = orp,
    .address = 3,
    .value = 0.0,
    .unit = "mV",
    .ratio = 0.0,
    .hasTemperature = true,
    .manualTemp = 25.0,
    .autoTempComp = true,
    .updatedAt = 0,
    .createdAt = 0,
    .isConnected = false,
    .registerCount = 2,
    .commandCount = 1,
    .serverIp = "************",
    .fixedPort = 503,
    .serverBasePort = 503,
    .serialChannel = 1,
    .useBufferedAverage = false,  
    .responseTimeMs = 0,
    .baudRate = 3,
    .userName = "管理员",
    .userId = "admin-001",
    .iconName = "bubble_chart_outlined",
    .statusIconName = "more_horiz",
    .statusIconColor = 0xFFE91E63,
    .status = "",
    .enabled = true
  }
};

// [共用] 设备数量
int deviceCount = 3;

// ==================== 设备命令初始化函数 ====================

// [共用] 初始化设备命令
void initializeDeviceCommands() {
  // 溶解氧传感器命令
  devices[0].commands[0] = SensorCommand(
    "获取溶解氧和温度数据",
    (const uint8_t[]){0x01, 0x03, 0x00, 0x00, 0x00, 0x2A, 0xC4, 0x1E},
    8,
    "读取寄存器0x0000的42个值 (地址为1)"
  );
  devices[0].commandCount = 1;

  // pH传感器命令
  devices[1].commands[0] = SensorCommand(
    "获取PH和温度数据",
    (const uint8_t[]){0x02, 0x03, 0x00, 0x00, 0x00, 0x02, 0xC4, 0x38},
    8,
    "读取寄存器0x0000的2个值 (地址为2)"
  );
  devices[1].commands[1] = SensorCommand(
    "读取温补模式状态",
    (const uint8_t[]){0x02, 0x03, 0x00, 0x16, 0x00, 0x01, 0x65, 0xFA},
    8,
    "读取寄存器0x0016 (地址为2)"
  );
  devices[1].commandCount = 2;

  // ORP传感器命令
  devices[2].commands[0] = SensorCommand(
    "获取ORP和温度数据",
    (const uint8_t[]){0x03, 0x03, 0x00, 0x01, 0x00, 0x02, 0x94, 0x36},
    8,
    "读取寄存器0x0001的2个值 (地址为3)"
  );
  devices[2].commandCount = 1;
}

// [共用] 初始化设备默认值
void initializeDeviceDefaults() {
  // 为所有设备设置默认值
  for (int i = 0; i < deviceCount; i++) {
    if (devices[i].value == 0.0 && devices[i].address == 0) {
      // 设置基本默认值
      devices[i].value = 0.0;
      devices[i].hasTemperature = true;
      devices[i].manualTemp = 25.0;
      devices[i].autoTempComp = true;
      devices[i].updatedAt = 0;
      devices[i].createdAt = 0;
      devices[i].isConnected = false;
      devices[i].registerCount = 2;
      devices[i].commandCount = 0;
      devices[i].fixedPort = 502;
      devices[i].serverBasePort = 502;
      devices[i].serialChannel = 0;
      devices[i].useBufferedAverage = false;  // 默认使用缓存最新值
      devices[i].responseTimeMs = 0;
      devices[i].baudRate = 3;
      devices[i].statusIconColor = 0xFF4CAF50;
      devices[i].enabled = true;
    }
  }
}

// ==================== 水质参数辅助函数 ====================

// [共用] 创建默认参数
void createDefaultParameters(WaterQualityParameter* parameters, int maxCount, int* count) {
  if (!parameters || !count || maxCount < 7) {
    if (count) *count = 0;
    return;
  }

  int index = 0;

  // pH参数
  parameters[index++] = WaterQualityParameter(ph, 0.0, 14.0, "pH");

  // ORP参数
  parameters[index++] = WaterQualityParameter(orp, -2000.0, 2000.0, "mV");

  // 溶解氧参数
  parameters[index++] = WaterQualityParameter(oxygen, 0.0, 20.0, "mg/L");

  // 温度参数
  parameters[index++] = WaterQualityParameter(temperature, -10.0, 80.0, "℃");

  // 电导率参数
  parameters[index++] = WaterQualityParameter(conductivity, 0.0, 200000.0, "uS/cm");

  // 浊度参数
  parameters[index++] = WaterQualityParameter(turbidity, 0.0, 4000.0, "NTU");

  // 盐度参数
  parameters[index++] = WaterQualityParameter(salinity, 0.0, 70.0, "ppt");

  *count = index;
}
