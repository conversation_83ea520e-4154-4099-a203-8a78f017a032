#!/usr/bin/env python3
"""
验证Modbus RTU命令的正确性
"""

def calculate_crc16(data):
    """计算Modbus RTU的CRC16校验码"""
    crc = 0xFFFF
    for byte in data:
        crc ^= byte
        for _ in range(8):
            if crc & 0x0001:
                crc >>= 1
                crc ^= 0xA001
            else:
                crc >>= 1
    return crc

def verify_modbus_command(hex_string):
    """验证Modbus命令"""
    # 解析十六进制字符串
    data_bytes = bytes.fromhex(hex_string.replace(' ', ''))
    
    if len(data_bytes) < 8:
        print(f"❌ 命令长度不足: {len(data_bytes)} 字节")
        return False
    
    # 解析命令
    slave_addr = data_bytes[0]
    function_code = data_bytes[1] 
    start_addr = (data_bytes[2] << 8) | data_bytes[3]
    quantity = (data_bytes[4] << 8) | data_bytes[5]
    received_crc = (data_bytes[7] << 8) | data_bytes[6]  # 小端序
    
    print(f"📋 Modbus命令解析:")
    print(f"   从站地址: {slave_addr}")
    print(f"   功能码: 0x{function_code:02X} ({'读保持寄存器' if function_code == 3 else '读输入寄存器' if function_code == 4 else '未知'})")
    print(f"   起始地址: 0x{start_addr:04X} ({start_addr})")
    print(f"   寄存器数量: {quantity}")
    print(f"   接收到的CRC: 0x{received_crc:04X}")
    
    # 计算正确的CRC
    data_without_crc = data_bytes[:-2]
    calculated_crc = calculate_crc16(data_without_crc)
    print(f"   计算的CRC: 0x{calculated_crc:04X}")
    
    if received_crc == calculated_crc:
        print("✅ CRC校验正确")
        return True
    else:
        print("❌ CRC校验错误")
        return False

def main():
    print("🔍 验证ESP32-P4发送的Modbus命令")
    print("=" * 50)
    
    # 验证发送的命令
    commands = [
        ("pH传感器", "02 03 00 00 00 02 C4 38"),
        ("ORP传感器", "03 03 00 01 00 02 94 29"), 
        ("溶解氧传感器", "01 03 00 00 00 2A C4 15")
    ]
    
    for sensor_name, cmd in commands:
        print(f"\n📡 {sensor_name}:")
        print(f"   发送命令: {cmd}")
        is_valid = verify_modbus_command(cmd)
        if is_valid:
            print(f"   ✅ 命令格式正确")
        else:
            print(f"   ❌ 命令格式错误")
        print("-" * 40)

if __name__ == "__main__":
    main()
