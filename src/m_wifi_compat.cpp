#include "m_wifi_compat.h"

#ifdef CONFIG_IDF_TARGET_ESP32P4

#include "nvs_flash.h"
#include "esp_mac.h"
#include <cstring>

static const char* TAG = "WiFiCompat";

// 静态成员初始化
WiFiClass* WiFiClass::instance = nullptr;

// 全局WiFi对象
WiFiClass WiFi;

WiFiClass::WiFiClass() 
    : sta_netif(nullptr)
    , ap_netif(nullptr)
    , wifi_event_group(nullptr)
    , initialized(false)
    , current_status(WL_DISCONNECTED)
    , retry_count(0)
{
    instance = this;
}

WiFiClass::~WiFiClass() {
    end();
}

WiFiClass& WiFiClass::getInstance() {
    if (!instance) {
        instance = new WiFiClass();
    }
    return *instance;
}

esp_err_t WiFiClass::wifi_init() {
    if (initialized) {
        return ESP_OK;
    }
    
    // 初始化NVS
    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);
    
    // 初始化网络接口
    ESP_ERROR_CHECK(esp_netif_init());
    ESP_ERROR_CHECK(esp_event_loop_create_default());
    
    // 创建WiFi事件组
    wifi_event_group = xEventGroupCreate();
    
    // 创建默认WiFi STA
    sta_netif = esp_netif_create_default_wifi_sta();
    
    // 暂时跳过WiFi驱动初始化，只设置基本状态
    ESP_LOGI(TAG, "WiFi initialization prepared (driver init skipped for compatibility)");
    
    // 注册事件处理器
    ESP_ERROR_CHECK(esp_event_handler_register(WIFI_EVENT, ESP_EVENT_ANY_ID, &wifi_event_handler, this));
    ESP_ERROR_CHECK(esp_event_handler_register(IP_EVENT, IP_EVENT_STA_GOT_IP, &ip_event_handler, this));
    
    initialized = true;
    ESP_LOGI(TAG, "WiFi initialized successfully");
    
    return ESP_OK;
}

void WiFiClass::wifi_event_handler(void* arg, esp_event_base_t event_base,
                                   int32_t event_id, void* event_data) {
    WiFiClass* wifi = static_cast<WiFiClass*>(arg);
    
    if (event_base == WIFI_EVENT) {
        switch (event_id) {
            case WIFI_EVENT_STA_START:
                ESP_LOGI(TAG, "WiFi station started");
                esp_wifi_connect();
                break;
                
            case WIFI_EVENT_STA_CONNECTED:
                ESP_LOGI(TAG, "WiFi connected to AP");
                wifi->update_status(WL_CONNECTED);
                break;
                
            case WIFI_EVENT_STA_DISCONNECTED: {
                wifi_event_sta_disconnected_t* event = (wifi_event_sta_disconnected_t*) event_data;
                ESP_LOGI(TAG, "WiFi disconnected, reason: %d", event->reason);
                
                if (wifi->retry_count < MAX_RETRY) {
                    esp_wifi_connect();
                    wifi->retry_count++;
                    ESP_LOGI(TAG, "Retry to connect to the AP (%d/%d)", wifi->retry_count, MAX_RETRY);
                    wifi->update_status(WL_DISCONNECTED);
                } else {
                    xEventGroupSetBits(wifi->wifi_event_group, WIFI_FAIL_BIT);
                    wifi->update_status(WL_CONNECT_FAILED);
                    ESP_LOGI(TAG, "Connect to the AP failed");
                }
                break;
            }
            
            default:
                break;
        }
    }
}

void WiFiClass::ip_event_handler(void* arg, esp_event_base_t event_base,
                                 int32_t event_id, void* event_data) {
    WiFiClass* wifi = static_cast<WiFiClass*>(arg);
    
    if (event_base == IP_EVENT && event_id == IP_EVENT_STA_GOT_IP) {
        ip_event_got_ip_t* event = (ip_event_got_ip_t*) event_data;
        ESP_LOGI(TAG, "Got IP:" IPSTR, IP2STR(&event->ip_info.ip));
        wifi->retry_count = 0;
        xEventGroupSetBits(wifi->wifi_event_group, WIFI_CONNECTED_BIT);
        wifi->update_status(WL_CONNECTED);
    }
}

void WiFiClass::update_status(wl_status_t status) {
    current_status = status;
}

wl_status_t WiFiClass::begin(const char* ssid, const char* passphrase) {
    if (!ssid) {
        ESP_LOGE(TAG, "SSID cannot be null");
        return WL_CONNECT_FAILED;
    }
    
    // 初始化WiFi
    esp_err_t ret = wifi_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "WiFi init failed: %s", esp_err_to_name(ret));
        return WL_CONNECT_FAILED;
    }
    
    // 保存连接信息
    current_ssid = String(ssid);
    current_password = passphrase ? String(passphrase) : String("");
    
    // 设置WiFi模式为STA
    ESP_ERROR_CHECK(esp_wifi_set_mode(WIFI_MODE_STA));
    
    // 配置WiFi连接参数
    wifi_config_t wifi_config = {};
    strncpy((char*)wifi_config.sta.ssid, ssid, sizeof(wifi_config.sta.ssid) - 1);
    if (passphrase) {
        strncpy((char*)wifi_config.sta.password, passphrase, sizeof(wifi_config.sta.password) - 1);
    }
    wifi_config.sta.threshold.authmode = WIFI_AUTH_WPA2_PSK;
    wifi_config.sta.pmf_cfg.capable = true;
    wifi_config.sta.pmf_cfg.required = false;
    
    ESP_ERROR_CHECK(esp_wifi_set_config(WIFI_IF_STA, &wifi_config));
    ESP_ERROR_CHECK(esp_wifi_start());
    
    ESP_LOGI(TAG, "WiFi connecting to SSID: %s", ssid);
    
    // 等待连接结果
    EventBits_t bits = xEventGroupWaitBits(wifi_event_group,
                                           WIFI_CONNECTED_BIT | WIFI_FAIL_BIT,
                                           pdFALSE,
                                           pdFALSE,
                                           pdMS_TO_TICKS(10000)); // 10秒超时
    
    if (bits & WIFI_CONNECTED_BIT) {
        ESP_LOGI(TAG, "Connected to AP SSID: %s", ssid);
        return WL_CONNECTED;
    } else if (bits & WIFI_FAIL_BIT) {
        ESP_LOGI(TAG, "Failed to connect to SSID: %s", ssid);
        return WL_CONNECT_FAILED;
    } else {
        ESP_LOGE(TAG, "Unexpected event");
        return WL_CONNECT_FAILED;
    }
}

wl_status_t WiFiClass::begin(const String& ssid, const String& passphrase) {
    return begin(ssid.c_str(), passphrase.isEmpty() ? nullptr : passphrase.c_str());
}

void WiFiClass::disconnect(bool wifioff) {
    if (!initialized) return;
    
    esp_wifi_disconnect();
    if (wifioff) {
        esp_wifi_stop();
    }
    update_status(WL_DISCONNECTED);
    ESP_LOGI(TAG, "WiFi disconnected");
}

void WiFiClass::end() {
    if (!initialized) return;
    
    esp_wifi_stop();
    esp_wifi_deinit();
    
    if (wifi_event_group) {
        vEventGroupDelete(wifi_event_group);
        wifi_event_group = nullptr;
    }
    
    initialized = false;
    ESP_LOGI(TAG, "WiFi ended");
}

wl_status_t WiFiClass::status() {
    return current_status;
}

bool WiFiClass::isConnected() {
    return current_status == WL_CONNECTED;
}

IPAddress WiFiClass::localIP() {
    if (!isConnected() || !sta_netif) {
        return IPAddress(0, 0, 0, 0);
    }
    
    esp_netif_ip_info_t ip_info;
    esp_err_t ret = esp_netif_get_ip_info(sta_netif, &ip_info);
    if (ret == ESP_OK) {
        return IPAddress(ip_info.ip.addr);
    }
    return IPAddress(0, 0, 0, 0);
}

IPAddress WiFiClass::gatewayIP() {
    if (!isConnected() || !sta_netif) {
        return IPAddress(0, 0, 0, 0);
    }
    
    esp_netif_ip_info_t ip_info;
    esp_err_t ret = esp_netif_get_ip_info(sta_netif, &ip_info);
    if (ret == ESP_OK) {
        return IPAddress(ip_info.gw.addr);
    }
    return IPAddress(0, 0, 0, 0);
}

IPAddress WiFiClass::subnetMask() {
    if (!isConnected() || !sta_netif) {
        return IPAddress(0, 0, 0, 0);
    }
    
    esp_netif_ip_info_t ip_info;
    esp_err_t ret = esp_netif_get_ip_info(sta_netif, &ip_info);
    if (ret == ESP_OK) {
        return IPAddress(ip_info.netmask.addr);
    }
    return IPAddress(0, 0, 0, 0);
}

String WiFiClass::SSID() {
    return current_ssid;
}

String WiFiClass::macAddress() {
    uint8_t mac[6];
    esp_err_t ret = esp_wifi_get_mac(WIFI_IF_STA, mac);
    if (ret == ESP_OK) {
        char mac_str[18];
        snprintf(mac_str, sizeof(mac_str), "%02X:%02X:%02X:%02X:%02X:%02X",
                 mac[0], mac[1], mac[2], mac[3], mac[4], mac[5]);
        return String(mac_str);
    }
    return String("");
}

uint8_t* WiFiClass::macAddress(uint8_t* mac) {
    esp_err_t ret = esp_wifi_get_mac(WIFI_IF_STA, mac);
    if (ret == ESP_OK) {
        return mac;
    }
    return nullptr;
}

int32_t WiFiClass::RSSI() {
    if (!isConnected()) {
        return 0;
    }

    wifi_ap_record_t ap_info;
    esp_err_t ret = esp_wifi_sta_get_ap_info(&ap_info);
    if (ret == ESP_OK) {
        return ap_info.rssi;
    }
    return 0;
}

IPAddress WiFiClass::dnsIP(uint8_t dns_no) {
    if (!isConnected() || !sta_netif) {
        return IPAddress(0, 0, 0, 0);
    }

    esp_netif_dns_info_t dns_info;
    esp_netif_dns_type_t dns_type = (dns_no == 0) ? ESP_NETIF_DNS_MAIN : ESP_NETIF_DNS_BACKUP;

    esp_err_t ret = esp_netif_get_dns_info(sta_netif, dns_type, &dns_info);
    if (ret == ESP_OK) {
        return IPAddress(dns_info.ip.u_addr.ip4.addr);
    }
    return IPAddress(0, 0, 0, 0);
}

bool WiFiClass::config(IPAddress local_ip, IPAddress gateway, IPAddress subnet, IPAddress dns1, IPAddress dns2) {
    if (!sta_netif) {
        ESP_LOGE(TAG, "STA netif not initialized");
        return false;
    }

    // 停止DHCP客户端
    esp_netif_dhcpc_stop(sta_netif);

    // 设置静态IP配置
    esp_netif_ip_info_t ip_info;
    ip_info.ip.addr = local_ip.toInt();
    ip_info.gw.addr = gateway.toInt();
    ip_info.netmask.addr = subnet.toInt();

    esp_err_t ret = esp_netif_set_ip_info(sta_netif, &ip_info);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to set IP info: %s", esp_err_to_name(ret));
        return false;
    }

    // 设置DNS
    if (dns1.toInt() != 0) {
        esp_netif_dns_info_t dns_info;
        dns_info.ip.u_addr.ip4.addr = dns1.toInt();
        esp_netif_set_dns_info(sta_netif, ESP_NETIF_DNS_MAIN, &dns_info);
    }

    if (dns2.toInt() != 0) {
        esp_netif_dns_info_t dns_info;
        dns_info.ip.u_addr.ip4.addr = dns2.toInt();
        esp_netif_set_dns_info(sta_netif, ESP_NETIF_DNS_BACKUP, &dns_info);
    }

    ESP_LOGI(TAG, "Static IP configured successfully");
    return true;
}

bool WiFiClass::setDNS(IPAddress dns1, IPAddress dns2) {
    if (!sta_netif) {
        return false;
    }

    if (dns1.toInt() != 0) {
        esp_netif_dns_info_t dns_info;
        dns_info.ip.u_addr.ip4.addr = dns1.toInt();
        esp_netif_set_dns_info(sta_netif, ESP_NETIF_DNS_MAIN, &dns_info);
    }

    if (dns2.toInt() != 0) {
        esp_netif_dns_info_t dns_info;
        dns_info.ip.u_addr.ip4.addr = dns2.toInt();
        esp_netif_set_dns_info(sta_netif, ESP_NETIF_DNS_BACKUP, &dns_info);
    }

    return true;
}

bool WiFiClass::setSleep(bool enable) {
    wifi_ps_type_t ps_type = enable ? WIFI_PS_MIN_MODEM : WIFI_PS_NONE;
    esp_err_t ret = esp_wifi_set_ps(ps_type);
    return (ret == ESP_OK);
}

bool WiFiClass::getSleep() {
    wifi_ps_type_t ps_type;
    esp_err_t ret = esp_wifi_get_ps(&ps_type);
    if (ret == ESP_OK) {
        return (ps_type != WIFI_PS_NONE);
    }
    return false;
}

bool WiFiClass::mode(wifi_mode_t_compat mode) {
    if (!initialized) {
        esp_err_t ret = wifi_init();
        if (ret != ESP_OK) {
            return false;
        }
    }

    wifi_mode_t esp_mode;
    switch (mode) {
        case WIFI_OFF:
            esp_mode = WIFI_MODE_NULL;
            break;
        case WIFI_STA:
            esp_mode = WIFI_MODE_STA;
            break;
        case WIFI_AP:
            esp_mode = WIFI_MODE_AP;
            break;
        case WIFI_AP_STA:
            esp_mode = WIFI_MODE_APSTA;
            break;
        default:
            return false;
    }

    esp_err_t ret = esp_wifi_set_mode(esp_mode);
    return (ret == ESP_OK);
}

#endif // CONFIG_IDF_TARGET_ESP32P4
