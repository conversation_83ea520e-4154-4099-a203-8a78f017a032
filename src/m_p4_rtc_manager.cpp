#include "m_p4_rtc_manager.h"
#include "esp_sntp.h"
#include "esp_netif.h"
#include "lwip/err.h"
#include "lwip/sys.h"
#include "sys/time.h"
#include <cstring>

static const char* TAG = "RTC_MANAGER";

// 全局实例
RTCManager rtcManager;

// 月份天数表（平年）
static const int daysInMonth[] = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};

// 星期名称
static const char* weekdayNames[] = {"Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"};

RTCManager::RTCManager() : initialized(false), rtc_mutex(nullptr), sync_timer(nullptr) {
    // 初始化闹钟数组
    for (int i = 0; i < 4; i++) {
        alarms[i].enabled = false;
        alarms[i].hour = 0;
        alarms[i].minute = 0;
        alarms[i].weekdays = 0;
        alarms[i].callback = nullptr;
    }
}

RTCManager::~RTCManager() {
    end();
}

bool RTCManager::begin() {
    if (initialized) {
        return true;
    }
    
    // 创建互斥锁
    rtc_mutex = xSemaphoreCreateMutex();
    if (rtc_mutex == nullptr) {
        printf("[RTC] 创建互斥锁失败\n");
        return false;
    }
    
    // 初始化系统时间
    struct timeval tv;
    gettimeofday(&tv, nullptr);
    
    // 如果系统时间无效，设置默认时间
    if (tv.tv_sec < 946684800) { // 2000-01-01 00:00:00
        setDateTime(2024, 1, 1, 0, 0, 0);
        printf("[RTC] 设置默认时间: 2024-01-01 00:00:00\n");
    }
    
    // 创建同步定时器
    esp_timer_create_args_t timer_args = {
        .callback = syncTimerCallback,
        .arg = this,
        .name = "rtc_sync"
    };
    
    if (esp_timer_create(&timer_args, &sync_timer) != ESP_OK) {
        printf("[RTC] 创建同步定时器失败\n");
        vSemaphoreDelete(rtc_mutex);
        return false;
    }
    
    initialized = true;
    printf("[RTC] ✅ RTC管理器初始化成功\n");
    printDateTime();
    
    return true;
}

void RTCManager::end() {
    if (!initialized) {
        return;
    }
    
    // 停止自动同步
    disableAutoSync();
    
    // 删除定时器
    if (sync_timer) {
        esp_timer_delete(sync_timer);
        sync_timer = nullptr;
    }
    
    // 删除互斥锁
    if (rtc_mutex) {
        vSemaphoreDelete(rtc_mutex);
        rtc_mutex = nullptr;
    }
    
    initialized = false;
    printf("[RTC] RTC管理器已关闭\n");
}

bool RTCManager::setDateTime(int year, int month, int day, int hour, int minute, int second) {
    rtc_datetime_t dt = {year, month, day, hour, minute, second, 0};
    return setDateTime(&dt);
}

bool RTCManager::setDateTime(const rtc_datetime_t* dt) {
    if (!initialized || !dt || !validateDateTime(dt)) {
        return false;
    }
    
    if (xSemaphoreTake(rtc_mutex, pdMS_TO_TICKS(100)) != pdTRUE) {
        return false;
    }
    
    // 转换为Unix时间戳
    struct tm timeinfo;
    memset(&timeinfo, 0, sizeof(timeinfo));
    timeinfo.tm_year = dt->year - 1900;
    timeinfo.tm_mon = dt->month - 1;
    timeinfo.tm_mday = dt->day;
    timeinfo.tm_hour = dt->hour;
    timeinfo.tm_min = dt->minute;
    timeinfo.tm_sec = dt->second;
    
    time_t timestamp = mktime(&timeinfo);
    struct timeval tv = {timestamp, 0};
    
    bool success = (settimeofday(&tv, nullptr) == 0);
    
    if (success) {
        printf("[RTC] ✅ 时间设置成功: %04d-%02d-%02d %02d:%02d:%02d\n",
               dt->year, dt->month, dt->day, dt->hour, dt->minute, dt->second);
    } else {
        printf("[RTC] ❌ 时间设置失败\n");
    }
    
    xSemaphoreGive(rtc_mutex);
    return success;
}

bool RTCManager::getDateTime(rtc_datetime_t* dt) {
    if (!initialized || !dt) {
        return false;
    }
    
    if (xSemaphoreTake(rtc_mutex, pdMS_TO_TICKS(100)) != pdTRUE) {
        return false;
    }
    
    time_t now;
    struct tm timeinfo;
    
    time(&now);
    localtime_r(&now, &timeinfo);
    
    dt->year = timeinfo.tm_year + 1900;
    dt->month = timeinfo.tm_mon + 1;
    dt->day = timeinfo.tm_mday;
    dt->hour = timeinfo.tm_hour;
    dt->minute = timeinfo.tm_min;
    dt->second = timeinfo.tm_sec;
    dt->weekday = timeinfo.tm_wday;
    
    xSemaphoreGive(rtc_mutex);
    return true;
}

uint64_t RTCManager::getTimestamp() {
    time_t now;
    time(&now);
    return (uint64_t)now;
}

uint64_t RTCManager::getTimestampMs() {
    struct timeval tv;
    gettimeofday(&tv, nullptr);
    return (uint64_t)tv.tv_sec * 1000 + tv.tv_usec / 1000;
}

bool RTCManager::setTimestamp(uint64_t timestamp) {
    struct timeval tv = {(time_t)timestamp, 0};
    bool success = (settimeofday(&tv, nullptr) == 0);
    
    if (success) {
        printf("[RTC] ✅ 时间戳设置成功: %llu\n", timestamp);
    } else {
        printf("[RTC] ❌ 时间戳设置失败\n");
    }
    
    return success;
}

std::string RTCManager::getFormattedTime(const char* format) {
    if (!initialized) {
        return "RTC未初始化";
    }

    time_t now;
    struct tm timeinfo;
    char buffer[64];

    time(&now);
    localtime_r(&now, &timeinfo);
    strftime(buffer, sizeof(buffer), format, &timeinfo);

    return std::string(buffer);
}

std::string RTCManager::getFormattedDate(const char* format) {
    return getFormattedTime(format);
}

std::string RTCManager::getISOTime() {
    return getFormattedTime("%Y-%m-%dT%H:%M:%SZ");
}

std::string RTCManager::getLogTime() {
    struct timeval tv;
    gettimeofday(&tv, nullptr);
    struct tm timeinfo;
    localtime_r(&tv.tv_sec, &timeinfo);

    char buffer[64];  // 增加缓冲区大小
    snprintf(buffer, sizeof(buffer), "[%04d-%02d-%02d %02d:%02d:%02d.%03d]",
             timeinfo.tm_year + 1900, timeinfo.tm_mon + 1, timeinfo.tm_mday,
             timeinfo.tm_hour, timeinfo.tm_min, timeinfo.tm_sec,
             (int)(tv.tv_usec / 1000));

    return std::string(buffer);
}

bool RTCManager::isLeapYear(int year) {
    return (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0);
}

int RTCManager::getDaysInMonth(int year, int month) {
    if (month < 1 || month > 12) return 0;
    if (month == 2 && isLeapYear(year)) return 29;
    return daysInMonth[month - 1];
}

bool RTCManager::validateDateTime(const rtc_datetime_t* dt) {
    if (!dt) return false;
    
    if (dt->year < 2000 || dt->year > 2099) return false;
    if (dt->month < 1 || dt->month > 12) return false;
    if (dt->day < 1 || dt->day > getDaysInMonth(dt->year, dt->month)) return false;
    if (dt->hour < 0 || dt->hour > 23) return false;
    if (dt->minute < 0 || dt->minute > 59) return false;
    if (dt->second < 0 || dt->second > 59) return false;
    
    return true;
}

void RTCManager::printDateTime() {
    if (!initialized) {
        printf("[RTC] RTC未初始化\n");
        return;
    }
    
    rtc_datetime_t dt;
    if (getDateTime(&dt)) {
        printf("[RTC] 📅 当前时间: %04d-%02d-%02d %02d:%02d:%02d (%s)\n",
               dt.year, dt.month, dt.day, dt.hour, dt.minute, dt.second,
               weekdayNames[dt.weekday]);
    } else {
        printf("[RTC] ❌ 获取时间失败\n");
    }
}

void RTCManager::printStatus() {
    printf("[RTC] === RTC管理器状态 ===\n");
    printf("[RTC] 初始化状态: %s\n", initialized ? "已初始化" : "未初始化");
    
    if (initialized) {
        printDateTime();
        printf("[RTC] 运行时间: %llu 毫秒\n", getUptime());
        printf("[RTC] Unix时间戳: %llu\n", getTimestamp());
        
        // 显示闹钟状态
        int activeAlarms = 0;
        for (int i = 0; i < 4; i++) {
            if (alarms[i].enabled) {
                activeAlarms++;
                printf("[RTC] 闹钟%d: %02d:%02d (星期掩码: 0x%02X)\n",
                       i, alarms[i].hour, alarms[i].minute, alarms[i].weekdays);
            }
        }
        printf("[RTC] 活动闹钟数量: %d\n", activeAlarms);
    }
    printf("[RTC] ==================\n");
}

uint64_t RTCManager::getUptime() {
    return esp_timer_get_time() / 1000; // 转换为毫秒
}

void RTCManager::syncTimerCallback(void* arg) {
    RTCManager* rtc = static_cast<RTCManager*>(arg);
    if (rtc && rtc->initialized) {
        rtc->checkAlarms();
    }
}

bool RTCManager::isValidTime(int hour, int minute, int second) {
    return (hour >= 0 && hour <= 23) && 
           (minute >= 0 && minute <= 59) && 
           (second >= 0 && second <= 59);
}

bool RTCManager::isValidDate(int year, int month, int day) {
    if (year < 2000 || year > 2099) return false;
    if (month < 1 || month > 12) return false;
    if (day < 1 || day > getDaysInMonth(year, month)) return false;
    return true;
}

void RTCManager::checkAlarms() {
    if (!initialized) return;
    
    rtc_datetime_t dt;
    if (!getDateTime(&dt)) return;
    
    for (int i = 0; i < 4; i++) {
        if (alarms[i].enabled && 
            alarms[i].hour == dt.hour && 
            alarms[i].minute == dt.minute &&
            dt.second == 0) { // 只在整分钟触发
            
            // 检查星期掩码
            if (alarms[i].weekdays & (1 << dt.weekday)) {
                printf("[RTC] 🔔 闹钟%d触发: %02d:%02d\n", i, dt.hour, dt.minute);
                if (alarms[i].callback) {
                    alarms[i].callback();
                }
            }
        }
    }
}

int RTCManager::setAlarm(int hour, int minute, int weekdays, void (*callback)(void)) {
    if (!initialized || !isValidTime(hour, minute)) {
        return -1;
    }

    // 查找空闲的闹钟槽
    for (int i = 0; i < 4; i++) {
        if (!alarms[i].enabled) {
            alarms[i].enabled = true;
            alarms[i].hour = hour;
            alarms[i].minute = minute;
            alarms[i].weekdays = weekdays;
            alarms[i].callback = callback;

            printf("[RTC] ✅ 闹钟%d设置成功: %02d:%02d (星期掩码: 0x%02X)\n",
                   i, hour, minute, weekdays);
            return i;
        }
    }

    printf("[RTC] ❌ 闹钟设置失败: 无可用槽位\n");
    return -1;
}

bool RTCManager::enableAlarm(int alarmId, bool enable) {
    if (!initialized || alarmId < 0 || alarmId >= 4) {
        return false;
    }

    alarms[alarmId].enabled = enable;
    printf("[RTC] 闹钟%d %s\n", alarmId, enable ? "启用" : "禁用");
    return true;
}

bool RTCManager::disableAlarm(int alarmId) {
    return enableAlarm(alarmId, false);
}

void RTCManager::clearAllAlarms() {
    for (int i = 0; i < 4; i++) {
        alarms[i].enabled = false;
        alarms[i].callback = nullptr;
    }
    printf("[RTC] 所有闹钟已清除\n");
}

bool RTCManager::syncWithNTP(const char* ntpServer, int timeZone) {
    if (!initialized) {
        return false;
    }

    printf("[RTC] 开始NTP时间同步...\n");

    // 设置时区
    setenv("TZ", "CST-8", 1);
    tzset();

    // 配置SNTP
    sntp_setoperatingmode(SNTP_OPMODE_POLL);
    sntp_setservername(0, ntpServer);
    sntp_init();

    // 等待时间同步
    time_t now = 0;
    struct tm timeinfo;
    memset(&timeinfo, 0, sizeof(timeinfo));
    int retry = 0;
    const int retry_count = 10;

    while (sntp_get_sync_status() == SNTP_SYNC_STATUS_RESET && ++retry < retry_count) {
        printf("[RTC] 等待NTP同步... (%d/%d)\n", retry, retry_count);
        vTaskDelay(pdMS_TO_TICKS(2000));
    }

    if (retry < retry_count) {
        time(&now);
        localtime_r(&now, &timeinfo);
        printf("[RTC] ✅ NTP同步成功: %04d-%02d-%02d %02d:%02d:%02d\n",
               timeinfo.tm_year + 1900, timeinfo.tm_mon + 1, timeinfo.tm_mday,
               timeinfo.tm_hour, timeinfo.tm_min, timeinfo.tm_sec);
        return true;
    } else {
        printf("[RTC] ❌ NTP同步失败\n");
        return false;
    }
}

void RTCManager::enableAutoSync(int intervalMinutes) {
    if (!initialized || sync_timer == nullptr) {
        return;
    }

    // 停止现有定时器
    esp_timer_stop(sync_timer);

    // 启动新的定时器
    uint64_t interval_us = (uint64_t)intervalMinutes * 60 * 1000000;
    if (esp_timer_start_periodic(sync_timer, interval_us) == ESP_OK) {
        printf("[RTC] ✅ 自动同步已启用，间隔: %d 分钟\n", intervalMinutes);
    } else {
        printf("[RTC] ❌ 自动同步启用失败\n");
    }
}

void RTCManager::disableAutoSync() {
    if (sync_timer) {
        esp_timer_stop(sync_timer);
        printf("[RTC] 自动同步已禁用\n");
    }
}

bool RTCManager::hasRTCBattery() {
    // ESP32-P4的RTC电池检测
    // 这里可以通过检测RTC域的电压来判断
    return true; // 简化实现，假设有电池
}

float RTCManager::getRTCBatteryVoltage() {
    // 简化实现，返回模拟电压值
    return 3.0f; // 假设3.0V
}

void RTCManager::enterDeepSleep(uint64_t sleepTimeMs) {
    printf("[RTC] 进入深度睡眠，持续时间: %llu 毫秒\n", sleepTimeMs);
    esp_sleep_enable_timer_wakeup(sleepTimeMs * 1000);
    esp_deep_sleep_start();
}

void RTCManager::setWakeupTime(int hour, int minute) {
    if (!isValidTime(hour, minute)) {
        return;
    }

    rtc_datetime_t current;
    if (!getDateTime(&current)) {
        return;
    }

    // 计算到唤醒时间的秒数
    int currentMinutes = current.hour * 60 + current.minute;
    int wakeupMinutes = hour * 60 + minute;

    int sleepMinutes;
    if (wakeupMinutes > currentMinutes) {
        sleepMinutes = wakeupMinutes - currentMinutes;
    } else {
        sleepMinutes = (24 * 60) - currentMinutes + wakeupMinutes; // 次日
    }

    uint64_t sleepTimeMs = (uint64_t)sleepMinutes * 60 * 1000;
    printf("[RTC] 设置唤醒时间: %02d:%02d (睡眠 %d 分钟)\n", hour, minute, sleepMinutes);

    enterDeepSleep(sleepTimeMs);
}

int RTCManager::getAge(int birthYear, int birthMonth, int birthDay) {
    rtc_datetime_t current;
    if (!getDateTime(&current)) {
        return -1;
    }

    int age = current.year - birthYear;

    // 如果还没到生日，年龄减1
    if (current.month < birthMonth ||
        (current.month == birthMonth && current.day < birthDay)) {
        age--;
    }

    return age;
}

std::string RTCManager::getStatusString() {
    if (!initialized) {
        return "RTC未初始化";
    }

    char buffer[128];
    rtc_datetime_t dt;
    if (getDateTime(&dt)) {
        snprintf(buffer, sizeof(buffer),
                "时间: %04d-%02d-%02d %02d:%02d:%02d, 运行: %llu ms",
                dt.year, dt.month, dt.day, dt.hour, dt.minute, dt.second,
                getUptime());
    } else {
        snprintf(buffer, sizeof(buffer), "获取时间失败");
    }

    return std::string(buffer);
}
