#ifndef M_P4_RTC_MANAGER_H
#define M_P4_RTC_MANAGER_H

// ESP-IDF项目，不需要Arduino.h
// #include "m_serial_compat.h" // 暂时不包含，避免编译警告

#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/semphr.h"
#include "esp_timer.h"
#include "esp_log.h"
#include "esp_sleep.h"
#include "driver/rtc_io.h"
#include "soc/rtc.h"
#include "time.h"
#include "sys/time.h"
#include <string>
#include <string>

// ESP-IDF中使用std::string替代Arduino的String
#ifdef CONFIG_IDF_TARGET_ESP32P4
#define String std::string
#endif

/**
 * ESP32-P4 RTC时间管理器
 * 
 * 功能特性：
 * 1. 实时时钟管理 - 支持年月日时分秒
 * 2. 时间戳生成 - 提供毫秒级精度时间戳
 * 3. 时间同步 - 支持NTP/手动时间设置
 * 4. 低功耗模式 - 支持RTC电池供电
 * 5. 时间格式化 - 多种时间格式输出
 * 6. 闹钟功能 - 支持定时唤醒
 * 
 * 硬件要求：
 * - ESP32-P4-Module-DEV-KIT开发板
 * - 可充电RTC电池（CR1220或类似）
 * 
 * 使用方法：
 * RTCManager rtc;
 * rtc.begin();
 * rtc.setDateTime(2024, 12, 25, 10, 30, 0);
 * String timeStr = rtc.getFormattedTime();
 */

// RTC时间结构体
typedef struct {
    int year;       // 年份 (2000-2099)
    int month;      // 月份 (1-12)
    int day;        // 日期 (1-31)
    int hour;       // 小时 (0-23)
    int minute;     // 分钟 (0-59)
    int second;     // 秒钟 (0-59)
    int weekday;    // 星期 (0=周日, 1=周一, ..., 6=周六)
} rtc_datetime_t;

// RTC闹钟结构体
typedef struct {
    bool enabled;           // 闹钟是否启用
    int hour;              // 闹钟小时
    int minute;            // 闹钟分钟
    int weekdays;          // 闹钟星期掩码 (bit0=周日, bit1=周一, ...)
    void (*callback)(void); // 闹钟回调函数
} rtc_alarm_t;

class RTCManager {
private:
    bool initialized;
    SemaphoreHandle_t rtc_mutex;
    rtc_alarm_t alarms[4];  // 支持4个闹钟
    esp_timer_handle_t sync_timer;
    
    // 内部方法
    bool isLeapYear(int year);
    int getDaysInMonth(int year, int month);
    int calculateWeekday(int year, int month, int day);
    bool validateDateTime(const rtc_datetime_t* dt);
    void syncSystemTime();
    static void syncTimerCallback(void* arg);
    
public:
    RTCManager();
    ~RTCManager();
    
    // 初始化和配置
    bool begin();
    void end();
    bool isInitialized() const { return initialized; }
    
    // 时间设置和获取
    bool setDateTime(int year, int month, int day, int hour, int minute, int second);
    bool setDateTime(const rtc_datetime_t* dt);
    bool getDateTime(rtc_datetime_t* dt);
    
    // 时间戳相关
    uint64_t getTimestamp();        // 获取Unix时间戳（秒）
    uint64_t getTimestampMs();      // 获取Unix时间戳（毫秒）
    bool setTimestamp(uint64_t timestamp);
    
    // 时间格式化
    std::string getFormattedTime(const char* format = "%Y-%m-%d %H:%M:%S");
    std::string getFormattedDate(const char* format = "%Y-%m-%d");
    std::string getISOTime();            // ISO 8601格式: 2024-12-25T10:30:00Z
    std::string getLogTime();            // 日志格式: [2024-12-25 10:30:00.123]
    
    // 闹钟功能
    int setAlarm(int hour, int minute, int weekdays = 0x7F, void (*callback)(void) = nullptr);
    bool enableAlarm(int alarmId, bool enable = true);
    bool disableAlarm(int alarmId);
    void clearAllAlarms();
    void checkAlarms();
    
    // 时间同步
    bool syncWithNTP(const char* ntpServer = "pool.ntp.org", int timeZone = 8);
    void enableAutoSync(int intervalMinutes = 60);
    void disableAutoSync();
    
    // 电源管理
    bool hasRTCBattery();
    float getRTCBatteryVoltage();
    void enterDeepSleep(uint64_t sleepTimeMs);
    void setWakeupTime(int hour, int minute);
    
    // 实用工具
    bool isValidTime(int hour, int minute, int second = 0);
    bool isValidDate(int year, int month, int day);
    int getAge(int birthYear, int birthMonth, int birthDay);
    uint64_t getUptime();           // 系统运行时间（毫秒）
    
    // 调试和状态
    void printStatus();
    void printDateTime();
    std::string getStatusString();
};

// 全局RTC管理器实例
extern RTCManager rtcManager;

// 便捷宏定义
#define RTC_NOW() rtcManager.getFormattedTime()
#define RTC_DATE() rtcManager.getFormattedDate()
#define RTC_TIMESTAMP() rtcManager.getTimestamp()
#define RTC_LOG_TIME() rtcManager.getLogTime()

#endif // M_P4_RTC_MANAGER_H
