{"version": "1.0", "algorithm": "sha256", "created_at": "2025-06-20T07:37:50.367289+00:00", "files": [{"path": ".clang-format", "size": 349, "hash": "6d80d9de197c06d14c68ca8b4b3ab546707f6350807b7f4fe9cc6f8b899cd34b"}, {"path": ".gitattributes", "size": 29, "hash": "13cb7f5e12dd469dc5c88afcf0cd035abe6cd1fc2f30afe224991e66b4262222"}, {"path": ".giti<PERSON>re", "size": 269, "hash": "991e9c0e5032ef139152759063dec5c2c8b048af1ec039a74048cccf1e8b750d"}, {"path": ".m<PERSON><PERSON><PERSON>", "size": 42, "hash": "001886e8c7f212fc4c55e478da29a8a62faaaecc98071e95acfe57ab8cfa3429"}, {"path": ".prettieri<PERSON>re", "size": 5, "hash": "8bf5a385c929e31553fda850fc06940106c8ba2a9ad2f7429fc642f8902918ff"}, {"path": "ArduinoJson.h", "size": 129, "hash": "dc64dbb373853f5d1bb822a0829c3d1ed75ff53bf0525c36b1da360cdbccc63c"}, {"path": "CHANGELOG.md", "size": 9196, "hash": "53940b2fb8ab64f1e757f57dbd26675596300819c71aabbf85ebdcd4601823dc"}, {"path": "CMakeLists.txt", "size": 562, "hash": "b767a47c7f9f150a17c25774ed947b4da8e0ba53e0c5b38ac929491ced434a3c"}, {"path": "CONTRIBUTING.md", "size": 265, "hash": "f4eddce8ad45aaa746a5560c46c0d152d25d0c6877f1259198e4a61c17e0be56"}, {"path": "LICENSE.txt", "size": 1117, "hash": "ebba0906d6c8b3daa8a1b59acb2d3a416485dd77fc10f6dd4c9ed2cbe467ee2d"}, {"path": "README.md", "size": 10083, "hash": "2d6c61d33a61958670206155b0bb8812187a6630effbe61db67a4094e631b7cc"}, {"path": "SUPPORT.md", "size": 871, "hash": "8dba145f11d4b6763428039403b63d36713b1cd8ecc7725f9193c72d26510418"}, {"path": "appveyor.yml", "size": 1292, "hash": "4a4a54024352ed0a00e01c4e9cce97f6a2accec3d789983d0d65225780c4a685"}, {"path": "component.mk", "size": 33, "hash": "826b4df456f64f499903dd27045fae7e4a3c3dcad8924e595ca092a863f401f0"}, {"path": "idf_component.yml", "size": 485, "hash": "3541c0897a03053bb47120ae375656f90b2efd369225ef94706196e2780dddd4"}, {"path": "keywords.txt", "size": 745, "hash": "e29fe27c3287259e5e41d725c9821d6855fb8f7fdf85a0e4057b73f66934c50d"}, {"path": "library.json", "size": 748, "hash": "39309b243592657a63225549850c911d48edeb7659179e8be69605c923861585"}, {"path": "library.properties", "size": 527, "hash": "99fea66a63d26b0b7d83ef06fbba529555a400bdf1740048fdcf5003283b6268"}, {"path": "src/ArduinoJson.h", "size": 297, "hash": "5d2f09f1bf64f0859918420fcaf3221a05b1f4dd75c26aacb0db4a18b208b636"}, {"path": "src/ArduinoJson.hpp", "size": 2256, "hash": "36695bc1cb58a4dbbc85f50f142f0476108cc676622b6ee945d43585107131b6"}, {"path": "src/CMakeLists.txt", "size": 1948, "hash": "24732ac1172b82c11f8a4d1e7306f0268e7876f6c00c68eb4f2cd603ad22f99c"}, {"path": "src/ArduinoJson/Configuration.hpp", "size": 8264, "hash": "905b896b905b04787f2b865484afa1a8a7cb7dc34dfb6de60713ee640f2199e4"}, {"path": "src/ArduinoJson/Namespace.hpp", "size": 1553, "hash": "a54e347ce43fb1fb44ab473b34efb5fadadb37d614801c63bc1a1ac1de4e93c0"}, {"path": "src/ArduinoJson/compatibility.hpp", "size": 4233, "hash": "fba78fa73fb71ea2074db774c8e892c8d3bb348e8bc4b1242bfb29f4ab9247dd"}, {"path": "src/ArduinoJson/version.hpp", "size": 300, "hash": "e99a24ab66d8a1b316a196b3b2a68d5f76a1d72989569273a605a3461cb9ac18"}, {"path": "src/ArduinoJson/Array/ArrayData.hpp", "size": 1825, "hash": "837ee2d89772f01a381872c48dc380537c5062ffe831855a0e4d005ea0cc0485"}, {"path": "src/ArduinoJson/Array/ArrayImpl.hpp", "size": 2055, "hash": "c26b99787dc06d9c1ed8bb431452602f233dc2f61e849260ab683f086c74b79d"}, {"path": "src/ArduinoJson/Array/ElementProxy.hpp", "size": 1946, "hash": "6dbd8aa8dc3f50bdbec88c8f6e1e80ce22e396604dc05077ecbc2e14e2cbef9e"}, {"path": "src/ArduinoJson/Array/JsonArray.hpp", "size": 6652, "hash": "ca547bc44a7fcf8952d4258c15b2cec7596756ffffde51fac76eaae0b2716d7b"}, {"path": "src/ArduinoJson/Array/JsonArrayConst.hpp", "size": 3768, "hash": "f735457699d258d2565742813701fdbf22c1c28b6c02b85a479f3cdd6f53198a"}, {"path": "src/ArduinoJson/Array/JsonArrayIterator.hpp", "size": 2123, "hash": "bed9b03504b6f6624c1cc51fca0170b0514c6e602d2b56ef54b79f126c70dbc3"}, {"path": "src/ArduinoJson/Array/Utilities.hpp", "size": 3730, "hash": "77d4249d8041736bec8dbedb097edd3512aa3be1a154700a78035327f96a55fc"}, {"path": "src/ArduinoJson/Collection/CollectionData.hpp", "size": 2911, "hash": "f5891309dd32441f769d5511f568e8637b6fd418b70a482552aa9c935bd981f3"}, {"path": "src/ArduinoJson/Collection/CollectionImpl.hpp", "size": 3855, "hash": "c71d8c6199b52f6ac8fbb00f36edc3a98ba0d7edf9de2e2ca579c1a0132e24d5"}, {"path": "src/ArduinoJson/Deserialization/DeserializationError.hpp", "size": 2959, "hash": "d10f21f44a137dfd68b844d78271d51b0d6fdf24235393a598719496db6f7353"}, {"path": "src/ArduinoJson/Deserialization/DeserializationOptions.hpp", "size": 1004, "hash": "9302870dc4130c426eb19e108a77acd2d4c2c88a85b5fe07d19e186f1f7e64ca"}, {"path": "src/ArduinoJson/Deserialization/Filter.hpp", "size": 1551, "hash": "af64fe15db2b647616a4da3346fecee1f5a88825cde03fcd7953b08e4e0c28fe"}, {"path": "src/ArduinoJson/Deserialization/NestingLimit.hpp", "size": 703, "hash": "4bc0cb186c863b1728551f30e57500695d93c4090373c1faa22baeb9964892ac"}, {"path": "src/ArduinoJson/Deserialization/Reader.hpp", "size": 2001, "hash": "b75c0eb3f6fb9ad90013c26975ae50973516807dd0af4a40c14d492971db41b7"}, {"path": "src/ArduinoJson/Deserialization/deserialize.hpp", "size": 2714, "hash": "9da43a503753e25e263a98b4549424ccacaad55d47ea02e034691277a661c933"}, {"path": "src/ArduinoJson/Document/JsonDocument.hpp", "size": 14025, "hash": "04a458891be3b4667e62ad88e8dccb46db84be234be3f590ebdd37681e4e6b53"}, {"path": "src/ArduinoJson/Json/EscapeSequence.hpp", "size": 831, "hash": "e581927ef4baee76eb9e82069de21c6bfc41037835caf3185cb9fb901144c9ff"}, {"path": "src/ArduinoJson/Json/JsonDeserializer.hpp", "size": 18054, "hash": "eab580ffc09cfb16ca9f5852455760e7d945ee4c7e5d506d2c46049cae1ca5dd"}, {"path": "src/ArduinoJson/Json/JsonSerializer.hpp", "size": 3886, "hash": "165b257e03bbc35b93c04a3615c22667ad3b38c4d68f52e712b75ac5ab73cf05"}, {"path": "src/Arduino<PERSON>son/Json/Latch.hpp", "size": 1044, "hash": "253e02b7b6e1979851cd29f1c6d471845b91f909dd766452d8e0c3033a62b38d"}, {"path": "src/ArduinoJson/J<PERSON>/PrettyJsonSerializer.hpp", "size": 3085, "hash": "7f014affaf7801e167a755ee9f83279854e373876bc75e474363775e153874f7"}, {"path": "src/ArduinoJson/Json/TextFormatter.hpp", "size": 4251, "hash": "57bfc74d015980fbd91a06193733c1ee33c0cc76969ff53e3f6a290b520e4012"}, {"path": "src/Arduino<PERSON>son/Json/Utf16.hpp", "size": 1457, "hash": "5af304d335fa27f697444c12fc32e21114479f9381aa75519819fc38c1a2745a"}, {"path": "src/Arduino<PERSON>son/Json/Utf8.hpp", "size": 1209, "hash": "8efc9f909d1c258528e2788165fa24d315b74142d6f519b3414ef8be2c34ac0c"}, {"path": "src/ArduinoJson/Memory/Alignment.hpp", "size": 1163, "hash": "2c2a04e89005b7e3e2bf3175410d660fb1a219e54408826f8a5e9c9b53e53fee"}, {"path": "src/ArduinoJson/Memory/Allocator.hpp", "size": 977, "hash": "3b3ba7e2bb200d557a78859073b417b5f827f18c5a25cf57c77f2348c7631ce2"}, {"path": "src/ArduinoJson/Memory/MemoryPool.hpp", "size": 2176, "hash": "892850923c7771ad98338ecdacf0ae8ae383bd79309cd7ea77ac217750d6d4ff"}, {"path": "src/ArduinoJson/Memory/MemoryPoolList.hpp", "size": 6048, "hash": "3fa44c52573cd7bb6f418f589773151cb01989250f97d9388ae8f187928fbd22"}, {"path": "src/ArduinoJson/Memory/ResourceManager.hpp", "size": 3112, "hash": "7c7dfef1e5f6593bdb032ff4a59ddaf6f6028fcdc6319699ad498985c107416d"}, {"path": "src/ArduinoJson/Memory/ResourceManagerImpl.hpp", "size": 1435, "hash": "824f4ff2f5ca628050e3d6e20aafcb5a23c4520ea4e3f8c4831d256128c4d2cc"}, {"path": "src/ArduinoJson/Memory/StringBuffer.hpp", "size": 1926, "hash": "f5d58605a4bfc609842d8e308685cfbbf2a67a600535a4a7e6f267a29fa39d4d"}, {"path": "src/ArduinoJson/Memory/StringBuilder.hpp", "size": 1953, "hash": "afd5a44e855bc96cc1d655c44c1bc60e6cb738d09db50f1e1b0a0aef4545aac5"}, {"path": "src/ArduinoJson/Memory/StringNode.hpp", "size": 2155, "hash": "860225203715dcfcb399b193b2196ca388c59eca7aeedbf3587a525915cae3b1"}, {"path": "src/ArduinoJson/Memory/StringPool.hpp", "size": 2408, "hash": "51271fd322ea1b3787242e0d43196f92b84725a022844674040518a6dfef9fe9"}, {"path": "src/ArduinoJson/Misc/SerializedValue.hpp", "size": 1428, "hash": "d4c3c3ef7805bbc92aa759c50497ddcb7af6890b28c5405c3ee055dd866a430f"}, {"path": "src/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>/MsgPack/MsgPackBinary.hpp", "size": 2789, "hash": "ac66968129fa0d537f4bcbf085c5b5c6c2a9076b7943361b12bce97ef849a8e5"}, {"path": "src/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>/MsgPack/MsgPackDeserializer.hpp", "size": 13232, "hash": "8d82a6c6ed575826a0a462d62c8b2973d3aac783ddaf7d588e20c2a238881cbc"}, {"path": "src/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>/MsgPack/MsgPackExtension.hpp", "size": 3165, "hash": "763501aee1773a80de806ded6ede3620f480959bdc0d3fb1cca5f726997e7147"}, {"path": "src/<PERSON>rd<PERSON><PERSON><PERSON><PERSON>/MsgPack/MsgPackSerializer.hpp", "size": 6217, "hash": "4672f7e6a4422f8f75d7839462278969ce8d1c92d538e16f1e9ec7325a704884"}, {"path": "src/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>/MsgPack/endianness.hpp", "size": 1054, "hash": "1553671272918ee19331576c4ad279cae0614309feea2fa5b6ad7bed0fb1b8e4"}, {"path": "src/<PERSON>rduin<PERSON><PERSON>son/MsgPack/ieee754.hpp", "size": 491, "hash": "6884f01acb37bf41a35f772fe9443bed8bb7fefbba83c1ddbd470cd49ed84635"}, {"path": "src/ArduinoJson/Numbers/FloatParts.hpp", "size": 2423, "hash": "d7317069a5e814d595ad0dc2efb87c7d0f01a36e5bf419fe1fd445fcf2fbd4f3"}, {"path": "src/ArduinoJson/Numbers/FloatTraits.hpp", "size": 6553, "hash": "e47728474bb23a8627bf977bc8f0dc0dff3e2219300bbaa86f9bc68881dd6304"}, {"path": "src/ArduinoJson/Numbers/JsonFloat.hpp", "size": 354, "hash": "c5acc45159813dfe91974d083897b733da310d81c63acfc71f45e28509f8cf9d"}, {"path": "src/ArduinoJson/Numbers/JsonInteger.hpp", "size": 827, "hash": "530ec42a392467a6b4f9bae248093038265e9a8e8cfd449a04c2b93e3083ba25"}, {"path": "src/ArduinoJson/Numbers/arithmeticCompare.hpp", "size": 3596, "hash": "09b0baa129e52874c13eb30eb4c5426b6a18dc931850525892faf3fcbb1351d1"}, {"path": "src/ArduinoJson/Numbers/convertNumber.hpp", "size": 4238, "hash": "6f266f21da8aa051534a334300855acaaa447ea3ad2ec59868a47210e8ce4191"}, {"path": "src/ArduinoJson/Numbers/parseNumber.hpp", "size": 5815, "hash": "d5fcedcbbe39d51be47d6ee8dd9c3519ff120b0dfca193cb3bd27f3f4284e914"}, {"path": "src/ArduinoJson/Object/JsonObject.hpp", "size": 8220, "hash": "a5a2d622428dd75e29e7861fdc443f8c9f022f50a6229db1375e12d6761e12cf"}, {"path": "src/ArduinoJson/Object/JsonObjectConst.hpp", "size": 5489, "hash": "9f0e2965d503e5cbf11b632a1fd119694a4eb32b00f6173de3d2e9f0b6ce2d0a"}, {"path": "src/ArduinoJson/Object/JsonObjectIterator.hpp", "size": 2023, "hash": "651189ebe60ed87096fc188688d8cb019e1ba4a545c63bcb0232bfee148aa9bb"}, {"path": "src/ArduinoJson/Object/JsonPair.hpp", "size": 1533, "hash": "daa3c26c2a89b9f7ff8b96e88c77a526ed7e4360c849fc420829451f30987b17"}, {"path": "src/ArduinoJson/Object/MemberProxy.hpp", "size": 2051, "hash": "2182c5b13889a8b07c157e5a6987b0a8fcf74f47595f3dc11f7d66d9ee307e5a"}, {"path": "src/ArduinoJson/Object/ObjectData.hpp", "size": 2029, "hash": "4fa596fcd4d6014a46781fe0972423258c4a3a3d0b67984cd3f5c9fe4f3f91d3"}, {"path": "src/ArduinoJson/Object/ObjectImpl.hpp", "size": 2548, "hash": "2d41a186c71897e3fe056b728429fa746b813fb138e62ab1a060cf8c24b785a1"}, {"path": "src/<PERSON>rduin<PERSON><PERSON>son/Polyfills/alias_cast.hpp", "size": 529, "hash": "3a7505ae51ebc04cbbeadc5e199e78c78c2eef85b7dc398c2a92dd6a7b8cb934"}, {"path": "src/ArduinoJson/Polyfills/assert.hpp", "size": 297, "hash": "b5252fe977be572cfdec8c887fc24c45dc24edf6bab6ddcc794d11cfeb463764"}, {"path": "src/ArduinoJson/Polyfills/attributes.hpp", "size": 1110, "hash": "f7422477c513753ae0d70e30cf61e99bcc4734cd9157bd1b856faa15b6bd68d7"}, {"path": "src/ArduinoJson/Polyfills/ctype.hpp", "size": 373, "hash": "a85de0bc1f7b83ab6bd2acf6e81fcfdaa6f23a68c19443d354d1f01a3b38e1b3"}, {"path": "src/ArduinoJson/Polyfills/integer.hpp", "size": 541, "hash": "72296ae7f2a1d41d313ac7a07424ac318e6f32066afdb21160f6f2e206196dd0"}, {"path": "src/ArduinoJson/Polyfills/limits.hpp", "size": 938, "hash": "af2ee86ce21c3f8765ed740e9043148080a494b0cdaf1173a2629057655d815f"}, {"path": "src/ArduinoJson/Polyfills/math.hpp", "size": 493, "hash": "128e8add4c1989fb96b9b76017239cf8c728ee877777cc69aad02952c52236a6"}, {"path": "src/ArduinoJson/Polyfills/pgmspace.hpp", "size": 3409, "hash": "9f0d377e3d58b9504811079179a14914df815159c648ef750496e1956fc21fcf"}, {"path": "src/ArduinoJson/Polyfills/pgmspace_generic.hpp", "size": 1365, "hash": "042e78afcd10161b708033902fe9613c949b6a272989deba3758cb3828f8545a"}, {"path": "src/ArduinoJson/Polyfills/preprocessor.hpp", "size": 1286, "hash": "5b2c815ea646000f58e92e8f81338b623c98100039c08df4ad50d110a6b8fc0d"}, {"path": "src/ArduinoJson/Polyfills/type_traits.hpp", "size": 926, "hash": "595300e35e6dbbe946880c62e41252ad1811cc96dcd1fceb7f66a00d5039f54a"}, {"path": "src/ArduinoJson/Polyfills/utility.hpp", "size": 685, "hash": "ac45a4b5a85a00ae5eb7102a684dfe3eaf30496eb83eb117859d864eea250b9b"}, {"path": "src/ArduinoJson/Serialization/CountingDecorator.hpp", "size": 610, "hash": "4cfab0eba58b207dbf34b2a349cb94ffecb62d5f99cdb73291ccd8d9d08a9114"}, {"path": "src/ArduinoJson/Serialization/Writer.hpp", "size": 1128, "hash": "35b4927299e24ff719395e5608da402207d174f2bb74cbdff7a98224300b02ac"}, {"path": "src/ArduinoJson/Serialization/measure.hpp", "size": 597, "hash": "afdd5d7e645f73d04aef10dcd93d2ee4f12fb03acbc31e2d1ec304245fcae0db"}, {"path": "src/ArduinoJson/Serialization/serialize.hpp", "size": 1936, "hash": "2f6a41290337ab2c6d992811211706f9dfd988bf3f14b80b663ec3ce12811d5d"}, {"path": "src/ArduinoJson/Strings/IsString.hpp", "size": 479, "hash": "597367d5b755192db6ed8355fe70ab41b9464a251fdc413c164546f14d87a13b"}, {"path": "src/ArduinoJson/Strings/JsonString.hpp", "size": 2480, "hash": "a00ae607eb05f6dcfc57e8aae4338d9f9b9d856758eacb4c17829e914fad9c5c"}, {"path": "src/ArduinoJson/Strings/StringAdapter.hpp", "size": 1352, "hash": "e8812647aeb2e723b5d9d941a03fed5745448bbd4d0c0ee23ce4734067b77393"}, {"path": "src/ArduinoJson/Strings/StringAdapters.hpp", "size": 2122, "hash": "a197878f0dfe8ddb6642d3851a849a139e022814bd6ebffcd49260c55a14ba4a"}, {"path": "src/ArduinoJson/Strings/StringTraits.hpp", "size": 1804, "hash": "c7a6d9c862b4347f6d693bd23beb7da3226ff7f07759ab0c0b33d49affcad1f6"}, {"path": "src/Arduino<PERSON>son/Variant/Converter.hpp", "size": 526, "hash": "8fbde36244011ff4be133896c07a4e72389f7cd556fe8df5dd188dbcb8300306"}, {"path": "src/ArduinoJson/Variant/ConverterImpl.hpp", "size": 11404, "hash": "5eca9fb6dc47eb1dc68ccac73f75e3573125ad9ba6caf39ef4a4e6412b487d5f"}, {"path": "src/ArduinoJson/Variant/JsonVariant.hpp", "size": 1873, "hash": "5e78cf8441f7757969717d1902c733bb9a5f66f46e8994edae1d1745e1737635"}, {"path": "src/ArduinoJson/Variant/JsonVariantConst.hpp", "size": 6916, "hash": "eabdd67fba07b5d9e5ab2d36f830e12a34bce42023564d2e2d657b6b5ef081a3"}, {"path": "src/ArduinoJson/Variant/JsonVariantCopier.hpp", "size": 687, "hash": "688e6d4c0737ca861f8cccdd9579f0a1f30d0d0065487dae1273704ebddc5a47"}, {"path": "src/ArduinoJson/Variant/JsonVariantVisitor.hpp", "size": 1602, "hash": "8d8acb8de6c0bada3704967e785dd45a2f7b9c2bdcb2fd424e025cd49dc326f7"}, {"path": "src/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>/Variant/VariantAttorney.hpp", "size": 926, "hash": "b3d072e949e4b4908137c53654bb4f428f8af03802fbd0a9c8f799a2d1b148f7"}, {"path": "src/<PERSON>rduino<PERSON>son/Variant/VariantCompare.hpp", "size": 5102, "hash": "133daa388eeb63ebfc84ad156701bfbf2634da24beea1070717e65513826c2e2"}, {"path": "src/Arduino<PERSON>son/Variant/VariantContent.hpp", "size": 1913, "hash": "0ce1ebbc005d8d3af9d278da5b69f8c167b0a6123ab497215f675d013bf7f8c5"}, {"path": "src/Arduino<PERSON>son/Variant/VariantData.hpp", "size": 17142, "hash": "12d6e766e33f2027f4bfe5dbe04bc35ac8eb653d77b59dec58677ae724563fa9"}, {"path": "src/Arduino<PERSON>son/Variant/VariantDataVisitor.hpp", "size": 536, "hash": "bf1d66bbebfbc06d095a2d94d3ace488b637b4d3301a0f3395e140a1e074a59f"}, {"path": "src/Arduino<PERSON>son/Variant/VariantImpl.hpp", "size": 4007, "hash": "26ed0fcb210f83f9dcd0c2f3e7314238e7453d9334f98d1c08875ddc6504d250"}, {"path": "src/Arduino<PERSON>son/Variant/VariantOperators.hpp", "size": 6017, "hash": "23ec13c033ce9ffe060e3aa2b4a354974284f2f220249d5ddaa31615fd2650ff"}, {"path": "src/<PERSON>rduin<PERSON><PERSON><PERSON>/Variant/VariantRefBase.hpp", "size": 10615, "hash": "5b837847df20e83244d2e253d5bf130ccde9f344bc7431e356f68595afe2121c"}, {"path": "src/Arduino<PERSON>son/Variant/VariantRefBaseImpl.hpp", "size": 5819, "hash": "d0907cd1abb0f92782d1dd42adda9fa5c329da18505b2133e29b5aee25df1929"}, {"path": "src/<PERSON>rduino<PERSON>son/Variant/VariantTag.hpp", "size": 318, "hash": "df26f34e70bf722fe11ca44eda05ef35d40649effe7383d74d0a61dddcaac18e"}, {"path": "src/Arduino<PERSON>son/Variant/VariantTo.hpp", "size": 698, "hash": "9f7fde152ea25afabdbe77954ee4ac87dff13455d320e87c71fb2df191961db5"}, {"path": "src/ArduinoJson/Strings/Adapters/FlashString.hpp", "size": 2245, "hash": "c4f8b6319c7847812ba015ce4aad586a326f7e48f92f0039e0c85fae149d989a"}, {"path": "src/ArduinoJson/Strings/Adapters/RamString.hpp", "size": 2796, "hash": "eb72a8224a4f92d7b54b54c303ed49a8e7cd0a951f7574541864e3201bf3fc5d"}, {"path": "src/ArduinoJson/Strings/Adapters/StringObject.hpp", "size": 1245, "hash": "4006612eda3de1a348367b4c5b4c7b7a853d1d133cb3a0c801735628615cc076"}, {"path": "src/ArduinoJson/Serialization/Writers/ArduinoStringWriter.hpp", "size": 1191, "hash": "fbdabc93bbaaee693c400ee2c7edf5900e5449559f4a574ab4459a0ebf13c06b"}, {"path": "src/ArduinoJson/Serialization/Writers/DummyWriter.hpp", "size": 362, "hash": "5bdce40e2aad008af7b793a05b4701850a8872e0f38554067574bb86f25939bd"}, {"path": "src/ArduinoJson/Serialization/Writers/PrintWriter.hpp", "size": 578, "hash": "47decd98959f245258d8cbdc6f14963db540fb5eafe7d9c2d92caa3fd1585a79"}, {"path": "src/ArduinoJson/Serialization/Writers/StaticStringWriter.hpp", "size": 673, "hash": "612199f335cb95ad66bd6dde33965f0bbe6dfda66f10b952913baa840cf31b47"}, {"path": "src/ArduinoJson/Serialization/Writers/StdStreamWriter.hpp", "size": 680, "hash": "eca1c78e623ac7a9399f6c14cd278dac2819a2d4951002fdef18bf7f75a43123"}, {"path": "src/ArduinoJson/Serialization/Writers/StdStringWriter.hpp", "size": 975, "hash": "59cc23eab10479b37df381c9efe7be7d2d33bf1b9f76b8012aed3fa8f59e6c86"}, {"path": "src/ArduinoJson/Polyfills/mpl/max.hpp", "size": 563, "hash": "226adfd3d90f3014e44b2cb90df2babd8ae7d9e2fb38db9a81e930d0792892d9"}, {"path": "src/ArduinoJson/Polyfills/type_traits/conditional.hpp", "size": 600, "hash": "b55cbe4e5464c5673e9fbff94ff9a4db377db5e01a55421d14b875dd508c8ff6"}, {"path": "src/ArduinoJson/Polyfills/type_traits/decay.hpp", "size": 614, "hash": "cba214f0ddae81d9341e06f909a9ddce9109120e8908c990924be667d54f77bd"}, {"path": "src/ArduinoJson/Polyfills/type_traits/declval.hpp", "size": 261, "hash": "68c3fd5b4fd7a194257df4213b1df4b26865eee43015db43de6728448b8ea7a8"}, {"path": "src/ArduinoJson/Polyfills/type_traits/enable_if.hpp", "size": 532, "hash": "47577979433842ea55b65398f54d4aa4f4bf28ca095890cafc22e10d8f7f42c1"}, {"path": "src/ArduinoJson/Polyfills/type_traits/function_traits.hpp", "size": 633, "hash": "8ead527bc1d4e79c1a66d4cc37d3820c3e36ecfda2469209c5ef29987ca98c61"}, {"path": "src/ArduinoJson/Polyfills/type_traits/integral_constant.hpp", "size": 459, "hash": "c9f3cd3fdd00797a23ee1e3e4a52bda5eae2bc211dbabbbd72a92254758a1363"}, {"path": "src/ArduinoJson/Polyfills/type_traits/is_array.hpp", "size": 442, "hash": "068ae1616d3b89b245bc8fcaca819037f7f7659517399e47c538b43eb593ff6f"}, {"path": "src/ArduinoJson/Polyfills/type_traits/is_base_of.hpp", "size": 686, "hash": "684cf9616274e83ca3378d90db361967aa556e4afa3b845d41fad6b0c4e63991"}, {"path": "src/ArduinoJson/Polyfills/type_traits/is_class.hpp", "size": 516, "hash": "58fa2c0d9b8b44101457458f6d2e68127adc4dee3f8fc9b22a079352b40c66b8"}, {"path": "src/ArduinoJson/Polyfills/type_traits/is_const.hpp", "size": 408, "hash": "0a5f6411e1d30bf1af49e9dd9d065ce650f604a4a8e49de273e1d93a01712afe"}, {"path": "src/ArduinoJson/Polyfills/type_traits/is_convertible.hpp", "size": 967, "hash": "41696f28f53615eeb2a62335dcc7210bff05f2531c1376bb6504629e45ccb8ea"}, {"path": "src/ArduinoJson/Polyfills/type_traits/is_enum.hpp", "size": 568, "hash": "860e3dec40ab1a2e4da676e24be4c13b1894269fa8fbcaf04c5742d4780bab6a"}, {"path": "src/ArduinoJson/Polyfills/type_traits/is_floating_point.hpp", "size": 483, "hash": "b0551306d4e34dd5867b534040a984cfe9a8980cd46a90571afa9b4df7851851"}, {"path": "src/ArduinoJson/Polyfills/type_traits/is_integral.hpp", "size": 1036, "hash": "68adf519deefab1240675e310b8b221f2b4380dde7a73bd92386f96d9d2a7994"}, {"path": "src/ArduinoJson/Polyfills/type_traits/is_pointer.hpp", "size": 338, "hash": "1b1c0735093bca4d1c87eb7312b34c79dcfb059ec1553d8f06178c9b4fa8325f"}, {"path": "src/ArduinoJson/Polyfills/type_traits/is_same.hpp", "size": 414, "hash": "23675f383d58591b548acccc07ca21ee4d748624c587c806bb7bee65e2535c16"}, {"path": "src/ArduinoJson/Polyfills/type_traits/is_signed.hpp", "size": 769, "hash": "9cb22cd44f3e8a6641f4ec1ab37abba2010bfbbfaf190e6b44601160a5383b7a"}, {"path": "src/ArduinoJson/Polyfills/type_traits/is_unsigned.hpp", "size": 690, "hash": "3840c0556287ade9a15f1062a5a437d444471902ae86dba1acc79530c1496f17"}, {"path": "src/ArduinoJson/Polyfills/type_traits/make_unsigned.hpp", "size": 1259, "hash": "0f544cce3804309aa54e2418fe6ebc2ed3ab5a3778c761e88ade1c29fc66e1c6"}, {"path": "src/ArduinoJson/Polyfills/type_traits/remove_const.hpp", "size": 510, "hash": "4359448d59ef880a498aee52df2025ee7f725cfedec804660663423995a8022d"}, {"path": "src/ArduinoJson/Polyfills/type_traits/remove_cv.hpp", "size": 583, "hash": "70abe18102c8647939bb5a19547838687f981ab40a85e269ea721b7e35d99079"}, {"path": "src/ArduinoJson/Polyfills/type_traits/remove_reference.hpp", "size": 526, "hash": "a6a125233312f34bd4bb56329141ee98e75ee1833d764d2817f0fbc60eade2dc"}, {"path": "src/ArduinoJson/Polyfills/type_traits/type_identity.hpp", "size": 286, "hash": "2b4754a94e97558104cbadd72e87a4232c06bd68f68fba6a7100ee8c10834914"}, {"path": "src/ArduinoJson/Polyfills/type_traits/void_t.hpp", "size": 423, "hash": "d8c4e357864d6d0d883351fd73c25e1ca9eead8aaf7c335e5ce4cb3018191000"}, {"path": "src/ArduinoJson/Deserialization/Readers/ArduinoStreamReader.hpp", "size": 676, "hash": "dba0bae30c8e0862ad381d682a9290b49bcc21e5a5d1c86df6f18de77afc2875"}, {"path": "src/ArduinoJson/Deserialization/Readers/ArduinoStringReader.hpp", "size": 446, "hash": "9323b3d75b00c215cc9077fda32cc68d1f41437b25a886f7e4b134919b32005c"}, {"path": "src/ArduinoJson/Deserialization/Readers/FlashReader.hpp", "size": 1222, "hash": "32fe57fe318cdc21efbe7e8515628b44c9dd6b3437de83fe647777ef0ab4b043"}, {"path": "src/ArduinoJson/Deserialization/Readers/IteratorReader.hpp", "size": 1031, "hash": "3f2a6ae8dae4c25acab53e55f224d84aaa2fbe2b365d4f030378dd9911e6b384"}, {"path": "src/ArduinoJson/Deserialization/Readers/RamReader.hpp", "size": 1322, "hash": "905edbf0a2e1ef3dc0f50f4411034d699a09a9639bd4d1692f2782b39dd68927"}, {"path": "src/ArduinoJson/Deserialization/Readers/StdStreamReader.hpp", "size": 642, "hash": "cc4027d58f9e4a69fb19544ff425768d6f2f43db40cc003307ed50d976f24eca"}, {"path": "src/ArduinoJson/Deserialization/Readers/VariantReader.hpp", "size": 508, "hash": "bf1d3a8baa7d88a8f0932bee9f280c00cec7fdc60f611ec02b2bffbaf26213c9"}]}