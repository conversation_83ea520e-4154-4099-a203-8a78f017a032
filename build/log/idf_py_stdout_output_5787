Command: /Users/<USER>/.espressif/python_env/idf5.5_py3.9_env/bin/python /Users/<USER>/esp/v5.5/esp-idf/tools/idf_monitor.py -p /dev/cu.wchusbserial5A7A0449951 -b 115200 --toolchain-prefix riscv32-esp-elf- --target esp32p4 --revision 1 --decode-panic backtrace /Users/<USER>/Documents/Arduino/libraries/ESP32-S3-Touch-LCD-4.3-Demo/Arduino/examples/esp32P4/build/HelloWorld.elf /Users/<USER>/Documents/Arduino/libraries/ESP32-S3-Touch-LCD-4.3-Demo/Arduino/examples/esp32P4/build/bootloader/bootloader.elf -m '/Users/<USER>/.espressif/python_env/idf5.5_py3.9_env/bin/python' '/Users/<USER>/esp/v5.5/esp-idf/tools/idf.py'
ESP-ROM:esp32p4-eco2-20240710
Build:Jul 10 2024
rst:0x1 (POWERON),boot:0x30f (SPI_FAST_FLASH_BOOT)
SPI mode:DIO, clock div:1
load:0x4ff33ce0,len:0x164c
load:0x4ff29ed0,len:0xd64
load:0x4ff2cbd0,len:0x3364
entry 0x4ff29eda
[0;32mI (25) boot: ESP-IDF v5.5-dirty 2nd stage bootloader[0m
[0;32mI (26) boot: compile time Jul 29 2025 12:48:23[0m
[0;32mI (26) boot: Multicore bootloader[0m
[0;32mI (27) boot: chip revision: v1.0[0m
[0;32mI (29) boot: efuse block revision: v0.3[0m
[0;32mI (33) boot.esp32p4: SPI Speed      : 80MHz[0m
[0;32mI (36) boot.esp32p4: SPI Mode       : DIO[0m
[0;32mI (40) boot.esp32p4: SPI Flash Size : 16MB[0m
[0;32mI (44) boot: Enabling RNG early entropy source...[0m
[0;32mI (49) boot: Partition Table:[0m
[0;32mI (51) boot: ## Label            Usage          Type ST Offset   Length[0m
[0;32mI (57) boot:  0 nvs              WiFi data        01 02 00009000 00006000[0m
[0;32mI (64) boot:  1 phy_init         RF data          01 01 0000f000 00001000[0m
[0;32mI (70) boot:  2 factory          factory app      00 00 00010000 00800000[0m
[0;32mI (77) boot:  3 storage          Unknown data     01 82 00810000 00600000[0m
[0;32mI (83) boot:  4 coredump         Unknown data     01 03 00e10000 00010000[0m
[0;32mI (90) boot:  5 nvs_key          NVS keys         01 04 00e20000 00001000[0m
[0;32mI (96) boot:  6 data             Unknown data     01 81 00e30000 001d0000[0m
[0;32mI (104) boot: End of partition table[0m
[0;32mI (107) esp_image: segment 0: paddr=00010020 vaddr=40020020 size=0be50h ( 48720) map[0m
[0;32mI (123) esp_image: segment 1: paddr=0001be78 vaddr=30100000 size=00044h (    68) load[0m
[0;32mI (125) esp_image: segment 2: paddr=0001bec4 vaddr=4ff00000 size=04154h ( 16724) load[0m
[0;32mI (133) esp_image: segment 3: paddr=00020020 vaddr=40000020 size=1dd1ch (122140) map[0m
[0;32mI (158) esp_image: segment 4: paddr=0003dd44 vaddr=4ff04154 size=0b81ch ( 47132) load[0m
[0;32mI (169) esp_image: segment 5: paddr=00049568 vaddr=4ff0f980 size=01da8h (  7592) load[0m
[0;32mI (175) boot: Loaded app from partition at offset 0x10000[0m
[0;32mI (175) boot: Disabling RNG early entropy source...[0m
[0;32mI (186) cpu_start: Multicore app[0m
[0;32mI (195) cpu_start: Pro cpu start user code[0m
[0;32mI (195) cpu_start: cpu freq: 360000000 Hz[0m
[0;32mI (195) app_init: Application information:[0m
[0;32mI (196) app_init: Project name:     HelloWorld[0m
[0;32mI (199) app_init: App version:      1[0m
[0;32mI (203) app_init: Compile time:     Jul 29 2025 12:48:17[0m
[0;32mI (208) app_init: ELF file SHA256:  30ad8996b...[0m
[0;32mI (212) app_init: ESP-IDF:          v5.5-dirty[0m
[0;32mI (216) efuse_init: Min chip rev:     v0.1[0m
[0;32mI (220) efuse_init: Max chip rev:     v1.99 [0m
[0;32mI (224) efuse_init: Chip rev:         v1.0[0m
[0;32mI (228) heap_init: Initializing. RAM available for dynamic allocation:[0m
[0;32mI (234) heap_init: At 4FF12E90 len 00028130 (160 KiB): RAM[0m
[0;32mI (239) heap_init: At 4FF3AFC0 len 00004BF0 (18 KiB): RAM[0m
[0;32mI (245) heap_init: At 4FF40000 len 00060000 (384 KiB): RAM[0m
[0;32mI (250) heap_init: At 50108080 len 00007F80 (31 KiB): RTCRAM[0m
[0;32mI (255) heap_init: At 30100044 len 00001FBC (7 KiB): TCM[0m
[0;33mW (261) spi_flash: GigaDevice detected but related driver is not linked, please check option `SPI_FLASH_SUPPORT_GD_CHIP`[0m
[0;32mI (271) spi_flash: detected chip: generic[0m
[0;32mI (275) spi_flash: flash io: dio[0m
[0;32mI (278) main_task: Started on CPU0[0m
[0;32mI (318) main_task: Calling app_main()[0m
[0;32mI (318) RS485_LOOPBACK: === ESP32-P4 RS485回环测试程序 ===[0m
[0;32mI (318) RS485_LOOPBACK: 🎯 测试目标: 验证HVD75扩展板回环功能[0m
[0;32mI (318) RS485_LOOPBACK: 📋 测试条件:[0m
[0;32mI (328) RS485_LOOPBACK:    - HVD75扩展板A+和B-已短接[0m
[0;32mI (328) RS485_LOOPBACK:    - ESP32-P4 GPIO21 → 扩展板RX (TX_PIN)[0m
[0;32mI (338) RS485_LOOPBACK:    - ESP32-P4 GPIO20 ← 扩展板TX (RX_PIN)[0m
[0;32mI (348) RS485_LOOPBACK:    - 3.3V供电正常[0m
[0;32mI (348) RS485_LOOPBACK: 🚀 开始测试...[0m

[0;32mI (348) RS485_LOOPBACK: 🔧 RS485回环测试开始[0m
[0;32mI (358) RS485_LOOPBACK:    UART: 2, TX: GPIO21, RX: GPIO20, 波特率: 9600[0m
[0;32mI (368) RS485_LOOPBACK:    前提条件: HVD75扩展板A+和B-已短接[0m
[0;32mI (368) RS485_LOOPBACK: ✅ UART配置完成[0m
[0;32mI (378) RS485_LOOPBACK: === 第1次回环测试 ===[0m
[0;32mI (378) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (388) main_task: Returned from app_main()[0m
[0;32mI (398) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (398) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (398) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (418) RS485_LOOPBACK: 📥 接收到 5 字节，总计 5/5[0m
[0;32mI (418) RS485_LOOPBACK: 📥 接收完成: "HELLO" (5字节)[0m
[0;32mI (418) RS485_LOOPBACK: ⏱️  总耗时: 20 ms[0m
[0;32mI (418) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (428) RS485_LOOPBACK: 📊 统计: 1/1 成功 (100.0%)[0m
[0;32mI (428) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (3438) RS485_LOOPBACK: === 第2次回环测试 ===[0m
[0;32mI (3438) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (3448) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (3448) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (3458) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (3478) RS485_LOOPBACK: 📥 接收到 10 字节，总计 10/10[0m
[0;32mI (3478) RS485_LOOPBACK: 📥 接收完成: "RS485_TEST" (10字节)[0m
[0;32mI (3478) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (3478) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (3488) RS485_LOOPBACK: 📊 统计: 2/2 成功 (100.0%)[0m
[0;32mI (3488) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (6498) RS485_LOOPBACK: === 第3次回环测试 ===[0m
[0;32mI (6498) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (6508) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (6508) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (6518) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (6538) RS485_LOOPBACK: 📥 接收到 11 字节，总计 11/11[0m
[0;32mI (6538) RS485_LOOPBACK: 📥 接收完成: "LOOPBACK_OK" (11字节)[0m
[0;32mI (6538) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (6538) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (6548) RS485_LOOPBACK: 📊 统计: 3/3 成功 (100.0%)[0m
[0;32mI (6548) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (9558) RS485_LOOPBACK: === 第4次回环测试 ===[0m
[0;32mI (9558) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (9568) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (9568) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (9578) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (9598) RS485_LOOPBACK: 📥 接收到 13 字节，总计 13/13[0m
[0;32mI (9598) RS485_LOOPBACK: 📥 接收完成: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (9598) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (9598) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (9608) RS485_LOOPBACK: 📊 统计: 4/4 成功 (100.0%)[0m
[0;32mI (9608) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (12618) RS485_LOOPBACK: === 第5次回环测试 ===[0m
[0;32mI (12618) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (12628) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (12628) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (12638) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (12658) RS485_LOOPBACK: 📥 接收到 16 字节，总计 16/16[0m
[0;32mI (12658) RS485_LOOPBACK: 📥 接收完成: "0123456789ABCDEF" (16字节)[0m
[0;32mI (12658) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (12658) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (12668) RS485_LOOPBACK: 📊 统计: 5/5 成功 (100.0%)[0m
[0;32mI (12668) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (15678) RS485_LOOPBACK: === 第6次回环测试 ===[0m
[0;32mI (15678) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (15688) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (15688) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (15688) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (15708) RS485_LOOPBACK: 📥 接收到 5 字节，总计 5/5[0m
[0;32mI (15708) RS485_LOOPBACK: 📥 接收完成: "HELLO" (5字节)[0m
[0;32mI (15708) RS485_LOOPBACK: ⏱️  总耗时: 20 ms[0m
[0;32mI (15708) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (15718) RS485_LOOPBACK: 📊 统计: 6/6 成功 (100.0%)[0m
[0;32mI (15718) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (18728) RS485_LOOPBACK: === 第7次回环测试 ===[0m
[0;32mI (18728) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (18738) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (18738) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (18748) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (18768) RS485_LOOPBACK: 📥 接收到 10 字节，总计 10/10[0m
[0;32mI (18768) RS485_LOOPBACK: 📥 接收完成: "RS485_TEST" (10字节)[0m
[0;32mI (18768) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (18768) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (18778) RS485_LOOPBACK: 📊 统计: 7/7 成功 (100.0%)[0m
[0;32mI (18778) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (21788) RS485_LOOPBACK: === 第8次回环测试 ===[0m
[0;32mI (21788) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (21798) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (21798) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (21808) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (21828) RS485_LOOPBACK: 📥 接收到 11 字节，总计 11/11[0m
[0;32mI (21828) RS485_LOOPBACK: 📥 接收完成: "LOOPBACK_OK" (11字节)[0m
[0;32mI (21828) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (21828) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (21838) RS485_LOOPBACK: 📊 统计: 8/8 成功 (100.0%)[0m
[0;32mI (21838) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (24848) RS485_LOOPBACK: === 第9次回环测试 ===[0m
[0;32mI (24848) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (24858) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (24858) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (24868) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (24888) RS485_LOOPBACK: 📥 接收到 13 字节，总计 13/13[0m
[0;32mI (24888) RS485_LOOPBACK: 📥 接收完成: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (24888) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (24888) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (24898) RS485_LOOPBACK: 📊 统计: 9/9 成功 (100.0%)[0m
[0;32mI (24898) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (27908) RS485_LOOPBACK: === 第10次回环测试 ===[0m
[0;32mI (27908) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (27918) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (27918) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (27928) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (27948) RS485_LOOPBACK: 📥 接收到 16 字节，总计 16/16[0m
[0;32mI (27948) RS485_LOOPBACK: 📥 接收完成: "0123456789ABCDEF" (16字节)[0m
[0;32mI (27948) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (27948) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (27958) RS485_LOOPBACK: 📊 统计: 10/10 成功 (100.0%)[0m
[0;32mI (27958) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (30968) RS485_LOOPBACK: === 第11次回环测试 ===[0m
[0;32mI (30968) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (30978) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (30978) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (30978) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (30998) RS485_LOOPBACK: 📥 接收到 5 字节，总计 5/5[0m
[0;32mI (30998) RS485_LOOPBACK: 📥 接收完成: "HELLO" (5字节)[0m
[0;32mI (30998) RS485_LOOPBACK: ⏱️  总耗时: 20 ms[0m
[0;32mI (30998) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (31008) RS485_LOOPBACK: 📊 统计: 11/11 成功 (100.0%)[0m
[0;32mI (31008) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (34018) RS485_LOOPBACK: === 第12次回环测试 ===[0m
[0;32mI (34018) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (34028) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (34028) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (34038) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (34058) RS485_LOOPBACK: 📥 接收到 10 字节，总计 10/10[0m
[0;32mI (34058) RS485_LOOPBACK: 📥 接收完成: "RS485_TEST" (10字节)[0m
[0;32mI (34058) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (34058) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (34068) RS485_LOOPBACK: 📊 统计: 12/12 成功 (100.0%)[0m
[0;32mI (34068) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (37078) RS485_LOOPBACK: === 第13次回环测试 ===[0m
[0;32mI (37078) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (37088) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (37088) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (37098) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (37118) RS485_LOOPBACK: 📥 接收到 11 字节，总计 11/11[0m
[0;32mI (37118) RS485_LOOPBACK: 📥 接收完成: "LOOPBACK_OK" (11字节)[0m
[0;32mI (37118) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (37118) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (37128) RS485_LOOPBACK: 📊 统计: 13/13 成功 (100.0%)[0m
[0;32mI (37128) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (40138) RS485_LOOPBACK: === 第14次回环测试 ===[0m
[0;32mI (40138) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (40148) RS485_LOOPBACK: 🧹 缓冲区已清空[0m
[0;32mI (40148) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (40158) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (40178) RS485_LOOPBACK: 📥 接收到 13 字节，总计 13/13[0m
[0;32mI (40178) RS485_LOOPBACK: 📥 接收完成: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (40178) RS485_LOOPBACK: ⏱️  总耗时: 30 ms[0m
[0;32mI (40178) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (40188) RS485_LOOPBACK: 📊 统计: 14/14 成功 (100.0%)[0m
[0;32mI (40188) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (43198) RS485_LOOPBACK: === 第15次回环测试 ===[0m
[0;32mI 