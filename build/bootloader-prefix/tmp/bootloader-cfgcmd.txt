cmd='/Users/<USER>/.espressif/tools/cmake/3.30.2/CMake.app/Contents/bin/cmake;-DSDKCONFIG=/Users/<USER>/Documents/Arduino/libraries/ESP32-S3-Touch-LCD-4.3-Demo/Arduino/examples/esp32P4/sdkconfig;-DIDF_PATH=/Users/<USER>/esp/v5.5/esp-idf;-DIDF_TARGET=esp32p4;-DPYTHON_DEPS_CHECKED=1;-DPYTHON=/Users/<USER>/.espressif/python_env/idf5.5_py3.9_env/bin/python;-DEXTRA_COMPONENT_DIRS=/Users/<USER>/esp/v5.5/esp-idf/components/bootloader;-DPROJECT_SOURCE_DIR=/Users/<USER>/Documents/Arduino/libraries/ESP32-S3-Touch-LCD-4.3-Demo/Arduino/examples/esp32P4;-DIGNORE_EXTRA_COMPONENT=;-GNinja;-S;<SOURCE_DIR><SOURCE_SUBDIR>;-B;<BINARY_DIR>'
