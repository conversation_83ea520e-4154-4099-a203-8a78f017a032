Archive member included to satisfy reference by file (symbol)

esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
                              (esp_bootloader_desc)
esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
                              (__assert_func)
esp-idf/main/libmain.a(bootloader_start.c.obj)
                              (call_start_cpu0)
esp-idf/riscv/libriscv.a(rv_utils.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj) (rv_utils_dbgr_is_attached)
esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
                              esp-idf/main/libmain.a(bootloader_start.c.obj) (bootloader_utility_load_partition_table)
esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj) (esp_partition_table_verify)
esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj) (bootloader_load_image)
esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj) (bootloader_sha256_start)
esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj) (bootloader_console_deinit)
esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj) (bootloader_ana_clock_glitch_reset_config)
esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32p4.c.obj)
                              esp-idf/main/libmain.a(bootloader_start.c.obj) (bootloader_init)
esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj) (bootloader_common_ota_select_crc)
esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32p4.c.obj) (bootloader_clock_configure)
esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32p4.c.obj) (bootloader_init_mem)
esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj) (bootloader_fill_random)
esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj) (esp_flash_encryption_enabled)
esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32p4.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj) (bootloader_random_disable)
esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj) (bootloader_mmap_get_free_pages)
esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32p4.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32p4.c.obj) (bootloader_flash_update_id)
esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32p4.c.obj) (bootloader_clear_bss_section)
esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32p4.c.obj) (bootloader_console_init)
esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj) (ESP_EFUSE_DIS_DIRECT_BOOT)
esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj) (esp_efuse_enable_rom_secure_download_mode)
esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj) (esp_efuse_read_field_blob)
esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                              esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj) (esp_efuse_utility_process)
esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj) (esp_efuse_get_key_dis_read)
esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                              esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj) (esp_efuse_utility_clear_program_registers)
esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj) (esp_cpu_configure_region_protection)
esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj) (rtc_clk_init)
esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                              esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj) (rtc_clk_32k_enable)
esp-idf/esp_rom/libesp_rom.a(esp_rom_regi2c_esp32p4.c.obj)
                              esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj) (esp_rom_regi2c_read)
esp-idf/esp_rom/libesp_rom.a(esp_rom_wdt.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj) (wdt_hal_init)
esp-idf/log/liblog.a(log_timestamp.c.obj)
                              esp-idf/main/libmain.a(bootloader_start.c.obj) (esp_log_timestamp)
esp-idf/hal/libhal.a(efuse_hal.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32p4.c.obj) (efuse_hal_chip_revision)
esp-idf/hal/libhal.a(efuse_hal.c.obj)
                              esp-idf/hal/libhal.a(efuse_hal.c.obj) (efuse_hal_get_major_chip_version)
esp-idf/hal/libhal.a(lp_timer_hal.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj) (lp_timer_hal_get_cycle_count)
esp-idf/hal/libhal.a(mmu_hal.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj) (mmu_hal_unmap_all)
esp-idf/hal/libhal.a(cache_hal.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32p4.c.obj) (cache_hal_init)
/Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_lshrdi3.o)
                              esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj) (__lshrdi3)
/Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_ashldi3.o)
                              esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj) (__ashldi3)
/Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_popcountsi2.o)
                              esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj) (__popcountsi2)
/Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_udivdi3.o)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj) (__udivdi3)
/Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(adddf3.o)
                              esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj) (__adddf3)
/Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(divdf3.o)
                              esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj) (__divdf3)
/Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(muldf3.o)
                              esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj) (__muldf3)
/Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(fixdfsi.o)
                              esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj) (__fixdfsi)
/Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(fixunsdfsi.o)
                              esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj) (__fixunsdfsi)
/Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(floatsidf.o)
                              esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj) (__floatsidf)
/Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(floatunsidf.o)
                              esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj) (__floatunsidf)
/Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(extendsfdf2.o)
                              esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj) (__extendsfdf2)
/Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_clz.o)
                              /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_udivdi3.o) (__clz_tab)
/Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_clzsi2.o)
                              /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(adddf3.o) (__clzsi2)
/Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-memcmp.o)
                              esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj) (memcmp)
/Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-impure.o)
                              esp-idf/main/libmain.a(bootloader_start.c.obj) (_impure_data)
/Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-memset.o)
                              esp-idf/main/libmain.a(bootloader_start.c.obj) (memset)
/Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-memcpy.o)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj) (memcpy)

Discarded input sections

 .text          0x00000000        0x0 CMakeFiles/bootloader.elf.dir/project_elf_src_esp32p4.c.obj
 .data          0x00000000        0x0 CMakeFiles/bootloader.elf.dir/project_elf_src_esp32p4.c.obj
 .bss           0x00000000        0x0 CMakeFiles/bootloader.elf.dir/project_elf_src_esp32p4.c.obj
 .comment       0x00000000       0x30 CMakeFiles/bootloader.elf.dir/project_elf_src_esp32p4.c.obj
 .note.GNU-stack
                0x00000000        0x0 CMakeFiles/bootloader.elf.dir/project_elf_src_esp32p4.c.obj
 .riscv.attributes
                0x00000000       0x6c CMakeFiles/bootloader.elf.dir/project_elf_src_esp32p4.c.obj
 .text          0x00000000        0x0 esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
 .data          0x00000000        0x0 esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
 .bss           0x00000000        0x0 esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
 .text          0x00000000        0x0 esp-idf/main/libmain.a(bootloader_start.c.obj)
 .data          0x00000000        0x0 esp-idf/main/libmain.a(bootloader_start.c.obj)
 .bss           0x00000000        0x0 esp-idf/main/libmain.a(bootloader_start.c.obj)
 .text.__getreent
                0x00000000        0xa esp-idf/main/libmain.a(bootloader_start.c.obj)
 .text          0x00000000        0x0 esp-idf/riscv/libriscv.a(rv_utils.c.obj)
 .data          0x00000000        0x0 esp-idf/riscv/libriscv.a(rv_utils.c.obj)
 .bss           0x00000000        0x0 esp-idf/riscv/libriscv.a(rv_utils.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .text.bootloader_common_get_partition_description
                0x00000000       0x9e esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .text.bootloader_atexit
                0x00000000        0x8 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .text.bootloader_sha256_hex_to_str
                0x00000000       0x50 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .rodata.bootloader_sha256_flash_contents.str1.4
                0x00000000       0x2d esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .text.bootloader_sha256_flash_contents
                0x00000000       0xfa esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .text.esp_image_bootloader_offset_get
                0x00000000        0xa esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .rodata.esp_image_bootloader_offset_set.str1.4
                0x00000000       0x44 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .text.esp_image_bootloader_offset_set
                0x00000000       0x3c esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .text.bootloader_load_image_no_verify
                0x00000000        0xe esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .text.esp_image_verify
                0x00000000        0x8 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .text.esp_image_get_metadata
                0x00000000       0xb4 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .text.esp_image_verify_bootloader_data
                0x00000000       0x28 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .text.esp_image_verify_bootloader
                0x00000000       0x2c esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .text.esp_image_get_flash_size
                0x00000000       0x52 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32p4.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32p4.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32p4.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .text.esp_flash_write_protect_crypt_cnt
                0x00000000       0x10 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .text.esp_get_flash_encryption_mode
                0x00000000       0x8e esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .rodata.esp_flash_encryption_set_release_mode.str1.4
                0x00000000       0xb6 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .text.esp_flash_encryption_set_release_mode
                0x00000000      0x114 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .rodata.esp_flash_encryption_cfg_verify_release_mode.str1.4
                0x00000000      0x383 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .text.esp_flash_encryption_cfg_verify_release_mode
                0x00000000      0x30c esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32p4.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32p4.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32p4.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .text.bootloader_flash_erase_range
                0x00000000       0x7e esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .text.bootloader_spi_flash_reset
                0x00000000       0x2c esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .iram1.7       0x00000000       0x88 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .iram1.8       0x00000000        0x4 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32p4.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32p4.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32p4.c.obj)
 .text.bootloader_flash_update_size
                0x00000000        0xc esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32p4.c.obj)
 .iram1.3       0x00000000       0x40 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32p4.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
 .text          0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .data          0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .bss           0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_TEMPERATURE_SENSOR
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ADC2_CH5_ATTEN0_INITCODE_DIFF
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ADC2_CH4_ATTEN0_INITCODE_DIFF
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ADC2_CH3_ATTEN0_INITCODE_DIFF
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ADC2_CH2_ATTEN0_INITCODE_DIFF
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ADC2_CH1_ATTEN0_INITCODE_DIFF
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ADC2_CH0_ATTEN0_INITCODE_DIFF
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ADC1_CH7_ATTEN0_INITCODE_DIFF
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ADC1_CH6_ATTEN0_INITCODE_DIFF
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ADC1_CH5_ATTEN0_INITCODE_DIFF
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ADC1_CH4_ATTEN0_INITCODE_DIFF
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ADC1_CH3_ATTEN0_INITCODE_DIFF
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ADC1_CH2_ATTEN0_INITCODE_DIFF
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ADC1_CH1_ATTEN0_INITCODE_DIFF
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ADC1_CH0_ATTEN0_INITCODE_DIFF
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ADC2_HI_DOUT_ATTEN3
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ADC2_HI_DOUT_ATTEN2
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ADC2_HI_DOUT_ATTEN1
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ADC2_HI_DOUT_ATTEN0
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_KEY5
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_KEY4
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_KEY3
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_KEY2
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_KEY1
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_KEY0
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_USER_DATA_MAC_CUSTOM
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_USER_DATA
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ADC1_HI_DOUT_ATTEN3
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ADC1_HI_DOUT_ATTEN2
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ADC1_HI_DOUT_ATTEN1
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ADC1_HI_DOUT_ATTEN0
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ADC2_AVE_INITCODE_ATTEN3
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ADC2_AVE_INITCODE_ATTEN2
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ADC2_AVE_INITCODE_ATTEN1
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ADC2_AVE_INITCODE_ATTEN0
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ADC1_AVE_INITCODE_ATTEN3
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ADC1_AVE_INITCODE_ATTEN2
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ADC1_AVE_INITCODE_ATTEN1
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ADC1_AVE_INITCODE_ATTEN0
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_OPTIONAL_UNIQUE_ID
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_LP_DCDC_DBIAS_VOL_GAP
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DSLP_LP_DBIAS
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DSLP_DBG
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_LSLP_HP_DBIAS
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ACTIVE_LP_DBIAS
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ACTIVE_HP_DBIAS
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_LDO_VO4_C
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_LDO_VO4_VOS
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_LDO_VO4_K
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_LDO_VO3_C
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_LDO_VO3_VOS
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_LDO_VO3_K
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_LDO_VO2_MUL
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_LDO_VO1_MUL
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_LDO_VO2_DREF
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_LDO_VO1_DREF
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_PKG_VERSION
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_PSRAM_VENDOR
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_TEMP
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_PSRAM_CAP
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_BLK_VERSION_MAJOR
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_BLK_VERSION_MINOR
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DISABLE_BLK_VERSION_MAJOR
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DISABLE_WAFER_VERSION_MAJOR
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WAFER_VERSION_MAJOR
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WAFER_VERSION_MINOR
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .data.ESP_EFUSE_MAC
                0x00000000       0x1c esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DIS_SWD
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DIS_WDT
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DCDC_VSET_EN
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_HP_PWR_SRC_SEL
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_KM_DISABLE_DEPLOY_MODE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_PXA0_TIEH_SEL_3
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_PXA0_TIEH_SEL_2
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_PXA0_TIEH_SEL_1
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_PXA0_TIEH_SEL_0
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DCDC_VSET
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_HYS_EN_PAD
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_SECURE_BOOT_DISABLE_FAST_WAKE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_SECURE_VERSION
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_FORCE_SEND_RESUME
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_UART_PRINT_CONTROL
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ENABLE_SECURITY_DOWNLOAD
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DIS_USB_SERIAL_JTAG_DOWNLOAD_MODE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_LOCK_KM_KEY
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DIS_USB_SERIAL_JTAG_ROM_PRINT
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DIS_DIRECT_BOOT
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DIS_DOWNLOAD_MODE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_FLASH_TPUW
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DIS_USB_OTG_DOWNLOAD_MODE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_FLASH_ECC_EN
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_FLASH_PAGE_SIZE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_FLASH_TYPE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_SECURE_BOOT_AGGRESSIVE_REVOKE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_SECURE_BOOT_EN
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_CRYPT_DPA_ENABLE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ECDSA_ENABLE_SOFT_K
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_SEC_DPA_LEVEL
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_KEY_PURPOSE_5
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_KEY_PURPOSE_4
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_KEY_PURPOSE_3
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_KEY_PURPOSE_2
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_KEY_PURPOSE_1
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_KEY_PURPOSE_0
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_SECURE_BOOT_KEY_REVOKE2
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_SECURE_BOOT_KEY_REVOKE1
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_SECURE_BOOT_KEY_REVOKE0
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_SPI_BOOT_CRYPT_CNT
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WDT_DELAY_SEL
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_XTS_KEY_LENGTH_256
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_FORCE_DISABLE_SW_INIT_KEY
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_FORCE_USE_KEY_MANAGER_KEY
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_KM_DEPLOY_ONLY_ONCE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_KM_RND_SWITCH_CYCLE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_KM_HUK_GEN_STATE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_USB_PHY_SEL
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DIS_DOWNLOAD_MANUAL_ENCRYPT
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DIS_PAD_JTAG
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_SOFT_DIS_JTAG
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_JTAG_SEL_ENABLE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DIS_TWAI
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_SPI_DOWNLOAD_MSPI_DIS
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DIS_FORCE_DOWNLOAD
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_POWERGLITCH_EN
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DIS_USB_JTAG
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_USB_OTG11_EXCHG_PINS
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_USB_DEVICE_EXCHG_PINS
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_RD_DIS_TEMPERATURE_SENSOR
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_RD_DIS_ADC2_CH5_ATTEN0_INITCODE_DIFF
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_RD_DIS_ADC2_CH4_ATTEN0_INITCODE_DIFF
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_RD_DIS_ADC2_CH3_ATTEN0_INITCODE_DIFF
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_RD_DIS_ADC2_CH2_ATTEN0_INITCODE_DIFF
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_RD_DIS_ADC2_CH1_ATTEN0_INITCODE_DIFF
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_RD_DIS_ADC2_CH0_ATTEN0_INITCODE_DIFF
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_RD_DIS_ADC1_CH7_ATTEN0_INITCODE_DIFF
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_RD_DIS_ADC1_CH6_ATTEN0_INITCODE_DIFF
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_RD_DIS_ADC1_CH5_ATTEN0_INITCODE_DIFF
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_RD_DIS_ADC1_CH4_ATTEN0_INITCODE_DIFF
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_RD_DIS_ADC1_CH3_ATTEN0_INITCODE_DIFF
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_RD_DIS_ADC1_CH2_ATTEN0_INITCODE_DIFF
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_RD_DIS_ADC1_CH1_ATTEN0_INITCODE_DIFF
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_RD_DIS_ADC1_CH0_ATTEN0_INITCODE_DIFF
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_RD_DIS_ADC2_HI_DOUT_ATTEN3
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_RD_DIS_ADC2_HI_DOUT_ATTEN2
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_RD_DIS_ADC2_HI_DOUT_ATTEN1
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_RD_DIS_ADC2_HI_DOUT_ATTEN0
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_RD_DIS_BLOCK_SYS_DATA2
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_RD_DIS_BLOCK_KEY5
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_RD_DIS_BLOCK_KEY4
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_RD_DIS_BLOCK_KEY3
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_RD_DIS_BLOCK_KEY2
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_RD_DIS_BLOCK_KEY1
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_RD_DIS_BLOCK_KEY0
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_RD_DIS
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_SOFT_DIS_JTAG
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_USB_PHY_SEL
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_USB_OTG11_EXCHG_PINS
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_USB_DEVICE_EXCHG_PINS
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_TEMPERATURE_SENSOR
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ADC2_CH5_ATTEN0_INITCODE_DIFF
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ADC2_CH4_ATTEN0_INITCODE_DIFF
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ADC2_CH3_ATTEN0_INITCODE_DIFF
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ADC2_CH2_ATTEN0_INITCODE_DIFF
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ADC2_CH1_ATTEN0_INITCODE_DIFF
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ADC2_CH0_ATTEN0_INITCODE_DIFF
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ADC1_CH7_ATTEN0_INITCODE_DIFF
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ADC1_CH6_ATTEN0_INITCODE_DIFF
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ADC1_CH5_ATTEN0_INITCODE_DIFF
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ADC1_CH4_ATTEN0_INITCODE_DIFF
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ADC1_CH3_ATTEN0_INITCODE_DIFF
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ADC1_CH2_ATTEN0_INITCODE_DIFF
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ADC1_CH1_ATTEN0_INITCODE_DIFF
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ADC1_CH0_ATTEN0_INITCODE_DIFF
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ADC2_HI_DOUT_ATTEN3
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ADC2_HI_DOUT_ATTEN2
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ADC2_HI_DOUT_ATTEN1
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ADC2_HI_DOUT_ATTEN0
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_BLOCK_SYS_DATA2
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_BLOCK_KEY5
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_BLOCK_KEY4
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_BLOCK_KEY3
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_BLOCK_KEY2
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_BLOCK_KEY1
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_BLOCK_KEY0
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_CUSTOM_MAC
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_BLOCK_USR_DATA
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ADC1_HI_DOUT_ATTEN3
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ADC1_HI_DOUT_ATTEN2
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ADC1_HI_DOUT_ATTEN1
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ADC1_HI_DOUT_ATTEN0
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ADC2_AVE_INITCODE_ATTEN3
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ADC2_AVE_INITCODE_ATTEN2
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ADC2_AVE_INITCODE_ATTEN1
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ADC2_AVE_INITCODE_ATTEN0
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ADC1_AVE_INITCODE_ATTEN3
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ADC1_AVE_INITCODE_ATTEN2
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ADC1_AVE_INITCODE_ATTEN1
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ADC1_AVE_INITCODE_ATTEN0
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_OPTIONAL_UNIQUE_ID
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_LP_DCDC_DBIAS_VOL_GAP
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_DSLP_LP_DBIAS
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_DSLP_DBG
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_LSLP_HP_DBIAS
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ACTIVE_LP_DBIAS
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ACTIVE_HP_DBIAS
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_LDO_VO4_C
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_LDO_VO4_VOS
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_LDO_VO4_K
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_LDO_VO3_C
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_LDO_VO3_VOS
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_LDO_VO3_K
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_LDO_VO2_MUL
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_LDO_VO1_MUL
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_LDO_VO2_DREF
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_LDO_VO1_DREF
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_SYS_DATA_PART1
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_PKG_VERSION
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_PSRAM_VENDOR
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_TEMP
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_PSRAM_CAP
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_BLK_VERSION_MAJOR
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_BLK_VERSION_MINOR
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_DISABLE_BLK_VERSION_MAJOR
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_DISABLE_WAFER_VERSION_MAJOR
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_WAFER_VERSION_MAJOR
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_WAFER_VERSION_MINOR
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_MAC
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_BLK1
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_KM_HUK_GEN_STATE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_SECURE_BOOT_DISABLE_FAST_WAKE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_SECURE_VERSION
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_FORCE_SEND_RESUME
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_UART_PRINT_CONTROL
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ENABLE_SECURITY_DOWNLOAD
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_DIS_USB_SERIAL_JTAG_DOWNLOAD_MODE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_DIS_USB_SERIAL_JTAG_ROM_PRINT
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_DIS_DIRECT_BOOT
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_DIS_DOWNLOAD_MODE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_FLASH_TPUW
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_DIS_USB_OTG_DOWNLOAD_MODE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_FLASH_ECC_EN
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_FLASH_PAGE_SIZE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_FLASH_TYPE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ECDSA_ENABLE_SOFT_K
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_SECURE_BOOT_AGGRESSIVE_REVOKE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_SECURE_BOOT_EN
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_CRYPT_DPA_ENABLE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_SEC_DPA_LEVEL
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_KEY_PURPOSE_5
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_KEY_PURPOSE_4
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_KEY_PURPOSE_3
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_KEY_PURPOSE_2
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_KEY_PURPOSE_1
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_KEY_PURPOSE_0
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_SECURE_BOOT_KEY_REVOKE2
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_SECURE_BOOT_KEY_REVOKE1
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_SECURE_BOOT_KEY_REVOKE0
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_SPI_BOOT_CRYPT_CNT
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_HP_PWR_SRC_SEL
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_DIS_SWD
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_DIS_WDT
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_PXA0_TIEH_SEL_3
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_PXA0_TIEH_SEL_2
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_PXA0_TIEH_SEL_1
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_PXA0_TIEH_SEL_0
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_HYS_EN_PAD
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_WDT_DELAY_SEL
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_DIS_DOWNLOAD_MANUAL_ENCRYPT
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_DIS_PAD_JTAG
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_JTAG_SEL_ENABLE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_DIS_TWAI
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_SPI_DOWNLOAD_MSPI_DIS
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_DIS_FORCE_DOWNLOAD
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_DIS_USB_JTAG
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_KM_DISABLE_DEPLOY_MODE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_LOCK_KM_KEY
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_XTS_KEY_LENGTH_256
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_FORCE_DISABLE_SW_INIT_KEY
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_FORCE_USE_KEY_MANAGER_KEY
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_KM_DEPLOY_ONLY_ONCE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_KM_RND_SWITCH_CYCLE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_RD_DIS
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.TEMPERATURE_SENSOR
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ADC2_CH5_ATTEN0_INITCODE_DIFF
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ADC2_CH4_ATTEN0_INITCODE_DIFF
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ADC2_CH3_ATTEN0_INITCODE_DIFF
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ADC2_CH2_ATTEN0_INITCODE_DIFF
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ADC2_CH1_ATTEN0_INITCODE_DIFF
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ADC2_CH0_ATTEN0_INITCODE_DIFF
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ADC1_CH7_ATTEN0_INITCODE_DIFF
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ADC1_CH6_ATTEN0_INITCODE_DIFF
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ADC1_CH5_ATTEN0_INITCODE_DIFF
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ADC1_CH4_ATTEN0_INITCODE_DIFF
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ADC1_CH3_ATTEN0_INITCODE_DIFF
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ADC1_CH2_ATTEN0_INITCODE_DIFF
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ADC1_CH1_ATTEN0_INITCODE_DIFF
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ADC1_CH0_ATTEN0_INITCODE_DIFF
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ADC2_HI_DOUT_ATTEN3
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ADC2_HI_DOUT_ATTEN2
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ADC2_HI_DOUT_ATTEN1
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ADC2_HI_DOUT_ATTEN0
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.KEY5  0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.KEY4  0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.KEY3  0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.KEY2  0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.KEY1  0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.KEY0  0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.USER_DATA_MAC_CUSTOM
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.USER_DATA
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ADC1_HI_DOUT_ATTEN3
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ADC1_HI_DOUT_ATTEN2
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ADC1_HI_DOUT_ATTEN1
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ADC1_HI_DOUT_ATTEN0
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ADC2_AVE_INITCODE_ATTEN3
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ADC2_AVE_INITCODE_ATTEN2
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ADC2_AVE_INITCODE_ATTEN1
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ADC2_AVE_INITCODE_ATTEN0
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ADC1_AVE_INITCODE_ATTEN3
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ADC1_AVE_INITCODE_ATTEN2
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ADC1_AVE_INITCODE_ATTEN1
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ADC1_AVE_INITCODE_ATTEN0
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.OPTIONAL_UNIQUE_ID
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.LP_DCDC_DBIAS_VOL_GAP
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DSLP_LP_DBIAS
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DSLP_DBG
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.LSLP_HP_DBIAS
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ACTIVE_LP_DBIAS
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ACTIVE_HP_DBIAS
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.LDO_VO4_C
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.LDO_VO4_VOS
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.LDO_VO4_K
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.LDO_VO3_C
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.LDO_VO3_VOS
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.LDO_VO3_K
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.LDO_VO2_MUL
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.LDO_VO1_MUL
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.LDO_VO2_DREF
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.LDO_VO1_DREF
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.PKG_VERSION
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.PSRAM_VENDOR
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.TEMP  0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.PSRAM_CAP
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.BLK_VERSION_MAJOR
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.BLK_VERSION_MINOR
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DISABLE_BLK_VERSION_MAJOR
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DISABLE_WAFER_VERSION_MAJOR
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WAFER_VERSION_MAJOR
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WAFER_VERSION_MINOR
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .rodata.MAC    0x00000000       0x18 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DIS_SWD
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DIS_WDT
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DCDC_VSET_EN
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.HP_PWR_SRC_SEL
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.KM_DISABLE_DEPLOY_MODE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.PXA0_TIEH_SEL_3
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.PXA0_TIEH_SEL_2
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.PXA0_TIEH_SEL_1
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.PXA0_TIEH_SEL_0
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DCDC_VSET
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.HYS_EN_PAD
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.SECURE_BOOT_DISABLE_FAST_WAKE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.SECURE_VERSION
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.FORCE_SEND_RESUME
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.UART_PRINT_CONTROL
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ENABLE_SECURITY_DOWNLOAD
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DIS_USB_SERIAL_JTAG_DOWNLOAD_MODE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.LOCK_KM_KEY
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DIS_USB_SERIAL_JTAG_ROM_PRINT
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DIS_DIRECT_BOOT
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DIS_DOWNLOAD_MODE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.FLASH_TPUW
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DIS_USB_OTG_DOWNLOAD_MODE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.FLASH_ECC_EN
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.FLASH_PAGE_SIZE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.FLASH_TYPE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.SECURE_BOOT_AGGRESSIVE_REVOKE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.SECURE_BOOT_EN
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.CRYPT_DPA_ENABLE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ECDSA_ENABLE_SOFT_K
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.SEC_DPA_LEVEL
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.KEY_PURPOSE_5
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.KEY_PURPOSE_4
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.KEY_PURPOSE_3
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.KEY_PURPOSE_2
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.KEY_PURPOSE_1
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.KEY_PURPOSE_0
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.SECURE_BOOT_KEY_REVOKE2
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.SECURE_BOOT_KEY_REVOKE1
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.SECURE_BOOT_KEY_REVOKE0
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.SPI_BOOT_CRYPT_CNT
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WDT_DELAY_SEL
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.XTS_KEY_LENGTH_256
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.FORCE_DISABLE_SW_INIT_KEY
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.FORCE_USE_KEY_MANAGER_KEY
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.KM_DEPLOY_ONLY_ONCE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.KM_RND_SWITCH_CYCLE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.KM_HUK_GEN_STATE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.USB_PHY_SEL
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DIS_DOWNLOAD_MANUAL_ENCRYPT
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DIS_PAD_JTAG
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.SOFT_DIS_JTAG
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.JTAG_SEL_ENABLE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DIS_TWAI
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.SPI_DOWNLOAD_MSPI_DIS
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DIS_FORCE_DOWNLOAD
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.POWERGLITCH_EN
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DIS_USB_JTAG
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.USB_OTG11_EXCHG_PINS
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.USB_DEVICE_EXCHG_PINS
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.RD_DIS_TEMPERATURE_SENSOR
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.RD_DIS_ADC2_CH5_ATTEN0_INITCODE_DIFF
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.RD_DIS_ADC2_CH4_ATTEN0_INITCODE_DIFF
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.RD_DIS_ADC2_CH3_ATTEN0_INITCODE_DIFF
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.RD_DIS_ADC2_CH2_ATTEN0_INITCODE_DIFF
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.RD_DIS_ADC2_CH1_ATTEN0_INITCODE_DIFF
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.RD_DIS_ADC2_CH0_ATTEN0_INITCODE_DIFF
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.RD_DIS_ADC1_CH7_ATTEN0_INITCODE_DIFF
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.RD_DIS_ADC1_CH6_ATTEN0_INITCODE_DIFF
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.RD_DIS_ADC1_CH5_ATTEN0_INITCODE_DIFF
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.RD_DIS_ADC1_CH4_ATTEN0_INITCODE_DIFF
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.RD_DIS_ADC1_CH3_ATTEN0_INITCODE_DIFF
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.RD_DIS_ADC1_CH2_ATTEN0_INITCODE_DIFF
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.RD_DIS_ADC1_CH1_ATTEN0_INITCODE_DIFF
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.RD_DIS_ADC1_CH0_ATTEN0_INITCODE_DIFF
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.RD_DIS_ADC2_HI_DOUT_ATTEN3
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.RD_DIS_ADC2_HI_DOUT_ATTEN2
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.RD_DIS_ADC2_HI_DOUT_ATTEN1
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.RD_DIS_ADC2_HI_DOUT_ATTEN0
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.RD_DIS_BLOCK_SYS_DATA2
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.RD_DIS_BLOCK_KEY5
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.RD_DIS_BLOCK_KEY4
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.RD_DIS_BLOCK_KEY3
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.RD_DIS_BLOCK_KEY2
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.RD_DIS_BLOCK_KEY1
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.RD_DIS_BLOCK_KEY0
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.RD_DIS
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_SOFT_DIS_JTAG
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_USB_PHY_SEL
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_USB_OTG11_EXCHG_PINS
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_USB_DEVICE_EXCHG_PINS
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_TEMPERATURE_SENSOR
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ADC2_CH5_ATTEN0_INITCODE_DIFF
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ADC2_CH4_ATTEN0_INITCODE_DIFF
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ADC2_CH3_ATTEN0_INITCODE_DIFF
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ADC2_CH2_ATTEN0_INITCODE_DIFF
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ADC2_CH1_ATTEN0_INITCODE_DIFF
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ADC2_CH0_ATTEN0_INITCODE_DIFF
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ADC1_CH7_ATTEN0_INITCODE_DIFF
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ADC1_CH6_ATTEN0_INITCODE_DIFF
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ADC1_CH5_ATTEN0_INITCODE_DIFF
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ADC1_CH4_ATTEN0_INITCODE_DIFF
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ADC1_CH3_ATTEN0_INITCODE_DIFF
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ADC1_CH2_ATTEN0_INITCODE_DIFF
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ADC1_CH1_ATTEN0_INITCODE_DIFF
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ADC1_CH0_ATTEN0_INITCODE_DIFF
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ADC2_HI_DOUT_ATTEN3
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ADC2_HI_DOUT_ATTEN2
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ADC2_HI_DOUT_ATTEN1
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ADC2_HI_DOUT_ATTEN0
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_BLOCK_SYS_DATA2
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_BLOCK_KEY5
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_BLOCK_KEY4
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_BLOCK_KEY3
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_BLOCK_KEY2
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_BLOCK_KEY1
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_BLOCK_KEY0
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_CUSTOM_MAC
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_BLOCK_USR_DATA
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ADC1_HI_DOUT_ATTEN3
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ADC1_HI_DOUT_ATTEN2
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ADC1_HI_DOUT_ATTEN1
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ADC1_HI_DOUT_ATTEN0
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ADC2_AVE_INITCODE_ATTEN3
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ADC2_AVE_INITCODE_ATTEN2
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ADC2_AVE_INITCODE_ATTEN1
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ADC2_AVE_INITCODE_ATTEN0
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ADC1_AVE_INITCODE_ATTEN3
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ADC1_AVE_INITCODE_ATTEN2
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ADC1_AVE_INITCODE_ATTEN1
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ADC1_AVE_INITCODE_ATTEN0
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_OPTIONAL_UNIQUE_ID
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_LP_DCDC_DBIAS_VOL_GAP
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_DSLP_LP_DBIAS
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_DSLP_DBG
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_LSLP_HP_DBIAS
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ACTIVE_LP_DBIAS
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ACTIVE_HP_DBIAS
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_LDO_VO4_C
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_LDO_VO4_VOS
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_LDO_VO4_K
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_LDO_VO3_C
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_LDO_VO3_VOS
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_LDO_VO3_K
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_LDO_VO2_MUL
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_LDO_VO1_MUL
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_LDO_VO2_DREF
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_LDO_VO1_DREF
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_SYS_DATA_PART1
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_PKG_VERSION
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_PSRAM_VENDOR
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_TEMP
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_PSRAM_CAP
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_BLK_VERSION_MAJOR
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_BLK_VERSION_MINOR
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_DISABLE_BLK_VERSION_MAJOR
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_DISABLE_WAFER_VERSION_MAJOR
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_WAFER_VERSION_MAJOR
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_WAFER_VERSION_MINOR
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_MAC
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_BLK1
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_KM_HUK_GEN_STATE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_SECURE_BOOT_DISABLE_FAST_WAKE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_SECURE_VERSION
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_FORCE_SEND_RESUME
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_UART_PRINT_CONTROL
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ENABLE_SECURITY_DOWNLOAD
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_DIS_USB_SERIAL_JTAG_DOWNLOAD_MODE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_DIS_USB_SERIAL_JTAG_ROM_PRINT
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_DIS_DIRECT_BOOT
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_DIS_DOWNLOAD_MODE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_FLASH_TPUW
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_DIS_USB_OTG_DOWNLOAD_MODE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_FLASH_ECC_EN
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_FLASH_PAGE_SIZE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_FLASH_TYPE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ECDSA_ENABLE_SOFT_K
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_SECURE_BOOT_AGGRESSIVE_REVOKE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_SECURE_BOOT_EN
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_CRYPT_DPA_ENABLE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_SEC_DPA_LEVEL
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_KEY_PURPOSE_5
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_KEY_PURPOSE_4
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_KEY_PURPOSE_3
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_KEY_PURPOSE_2
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_KEY_PURPOSE_1
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_KEY_PURPOSE_0
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_SECURE_BOOT_KEY_REVOKE2
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_SECURE_BOOT_KEY_REVOKE1
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_SECURE_BOOT_KEY_REVOKE0
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_SPI_BOOT_CRYPT_CNT
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_HP_PWR_SRC_SEL
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_DIS_SWD
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_DIS_WDT
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_PXA0_TIEH_SEL_3
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_PXA0_TIEH_SEL_2
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_PXA0_TIEH_SEL_1
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_PXA0_TIEH_SEL_0
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_HYS_EN_PAD
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_WDT_DELAY_SEL
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_DIS_DOWNLOAD_MANUAL_ENCRYPT
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_DIS_PAD_JTAG
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_JTAG_SEL_ENABLE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_DIS_TWAI
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_SPI_DOWNLOAD_MSPI_DIS
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_DIS_FORCE_DOWNLOAD
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_DIS_USB_JTAG
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_KM_DISABLE_DEPLOY_MODE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_LOCK_KM_KEY
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_XTS_KEY_LENGTH_256
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_FORCE_DISABLE_SW_INIT_KEY
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_FORCE_USE_KEY_MANAGER_KEY
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_KM_DEPLOY_ONLY_ONCE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_KM_RND_SWITCH_CYCLE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_RD_DIS
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .debug_info    0x00000000     0x3971 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .debug_abbrev  0x00000000      0x119 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .debug_aranges
                0x00000000       0x18 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .debug_line    0x00000000      0x24f esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .debug_str     0x00000000     0x4123 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .comment       0x00000000       0x30 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .note.GNU-stack
                0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .riscv.attributes
                0x00000000       0x6c esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .text          0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .data          0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .bss           0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .text.esp_efuse_get_pkg_ver
                0x00000000       0x2a esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .text.esp_efuse_set_rom_log_scheme
                0x00000000       0x3e esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .text.esp_efuse_disable_rom_download_mode
                0x00000000       0x10 esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .text.esp_efuse_enable_rom_secure_download_mode
                0x00000000       0x34 esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .debug_info    0x00000000      0x3bb esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .debug_abbrev  0x00000000      0x1a2 esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .debug_loc     0x00000000       0x1f esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .debug_aranges
                0x00000000       0x38 esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .debug_ranges  0x00000000       0x28 esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .debug_line    0x00000000      0x477 esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .debug_str     0x00000000      0x62e esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .comment       0x00000000       0x30 esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .note.GNU-stack
                0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .debug_frame   0x00000000       0x80 esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .riscv.attributes
                0x00000000       0x6c esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .text          0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .data          0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .bss           0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_read_field_blob
                0x00000000       0x6e esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .rodata.esp_efuse_read_field_bit.str1.4
                0x00000000       0x3b esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_read_field_bit
                0x00000000       0x4a esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_read_field_cnt
                0x00000000       0x50 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_write_field_blob
                0x00000000       0x74 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .rodata.esp_efuse_write_field_cnt.str1.4
                0x00000000       0x4e esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_write_field_cnt
                0x00000000       0xae esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_write_field_bit
                0x00000000       0x5a esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_get_field_size
                0x00000000       0x18 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_write_reg
                0x00000000       0x5a esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_read_block
                0x00000000       0x44 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_read_reg
                0x00000000       0x46 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_write_block
                0x00000000       0x44 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .rodata.esp_efuse_batch_write_begin.str1.4
                0x00000000       0x51 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_batch_write_begin
                0x00000000       0x72 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .rodata.esp_efuse_batch_write_cancel.str1.4
                0x00000000       0x5f esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_batch_write_cancel
                0x00000000       0x72 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .rodata.esp_efuse_batch_write_commit.str1.4
                0x00000000       0x37 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_batch_write_commit
                0x00000000       0x8e esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_check_errors
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .rodata.esp_efuse_destroy_block.str1.4
                0x00000000      0x120 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_destroy_block
                0x00000000      0x13c esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .rodata.__func__.0
                0x00000000       0x1c esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .rodata.__func__.1
                0x00000000       0x13 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .rodata.__func__.2
                0x00000000       0x19 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .sbss.s_batch_writing_mode
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .debug_info    0x00000000     0x1295 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .debug_abbrev  0x00000000      0x3f5 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .debug_loc     0x00000000      0xab2 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .debug_aranges
                0x00000000       0x98 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .debug_ranges  0x00000000       0xe0 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .debug_line    0x00000000     0x13d3 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .debug_str     0x00000000      0x9c5 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .comment       0x00000000       0x30 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .note.GNU-stack
                0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .debug_frame   0x00000000      0x220 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .riscv.attributes
                0x00000000       0x6c esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text          0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .data          0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .bss           0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.write_reg.str1.4
                0x00000000       0xb4 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.write_reg
                0x00000000       0x7a esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.esp_efuse_utility_process.str1.4
                0x00000000       0x5d esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_process
                0x00000000      0x174 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_reset
                0x00000000       0x50 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_burn_efuses
                0x00000000       0x2e esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_erase_virt_blocks
                0x00000000        0x2 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.esp_efuse_utility_update_virt_blocks.str1.4
                0x00000000       0x27 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_update_virt_blocks
                0x00000000       0x2a esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.esp_efuse_utility_debug_dump_single_block.str1.4
                0x00000000       0x12 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_debug_dump_single_block
                0x00000000       0x8e esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_debug_dump_pending
                0x00000000       0x46 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.esp_efuse_utility_debug_dump_blocks.str1.4
                0x00000000        0xd esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_debug_dump_blocks
                0x00000000       0x46 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_get_number_of_items
                0x00000000       0x10 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_read_reg
                0x00000000       0x68 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_fill_buff
                0x00000000       0xde esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_count_once
                0x00000000       0x62 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.esp_efuse_utility_write_cnt.str1.4
                0x00000000       0x31 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_write_cnt
                0x00000000       0xd4 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.esp_efuse_utility_write_reg.str1.4
                0x00000000       0x53 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_write_reg
                0x00000000       0x64 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_write_blob
                0x00000000       0x92 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.esp_efuse_utility_get_read_register_address.str1.4
                0x00000000       0x16 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_get_read_register_address
                0x00000000       0x3e esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.esp_efuse_utility_is_correct_written_data.str1.4
                0x00000000       0xba esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_is_correct_written_data
                0x00000000       0xd6 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.__func__.0
                0x00000000       0x2c esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.__func__.1
                0x00000000       0x1b esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.__func__.2
                0x00000000        0xa esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.__func__.3
                0x00000000        0xf esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.__func__.4
                0x00000000       0x1a esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .sbss.s_burn_counter
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_info    0x00000000     0x1456 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_abbrev  0x00000000      0x458 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_loc     0x00000000     0x1331 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_aranges
                0x00000000       0xa8 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_ranges  0x00000000      0x2d0 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_line    0x00000000     0x18e1 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_str     0x00000000      0xb28 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .comment       0x00000000       0x30 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .note.GNU-stack
                0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_frame   0x00000000      0x2bc esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .riscv.attributes
                0x00000000       0x6c esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text          0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .data          0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .bss           0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_block_is_empty
                0x00000000       0x38 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_set_write_protect
                0x00000000       0x6a esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_set_read_protect
                0x00000000       0x3e esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_get_coding_scheme
                0x00000000        0x6 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_get_purpose_field
                0x00000000       0x20 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_get_key
                0x00000000       0x20 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .rodata.esp_efuse_get_key_dis_read.str1.4
                0x00000000       0x8f esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_get_key_dis_read
                0x00000000       0x4a esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_set_key_dis_read
                0x00000000       0x28 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_get_key_dis_write
                0x00000000       0x4a esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_set_key_dis_write
                0x00000000       0x28 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_get_key_purpose
                0x00000000       0x48 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_set_key_purpose
                0x00000000       0x3c esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_get_keypurpose_dis_write
                0x00000000       0x4a esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_set_keypurpose_dis_write
                0x00000000       0x28 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_find_purpose
                0x00000000       0x42 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_key_block_unused
                0x00000000       0x5e esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_find_unused_key_block
                0x00000000       0x2a esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_count_unused_key_blocks
                0x00000000       0x30 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_write_key
                0x00000000       0xe0 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .rodata.esp_efuse_write_keys.str1.4
                0x00000000       0xda esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_write_keys
                0x00000000      0x156 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .rodata.esp_efuse_get_digest_revoke.str1.4
                0x00000000       0x42 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_get_digest_revoke
                0x00000000       0x48 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_set_digest_revoke
                0x00000000       0x26 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_get_write_protect_of_digest_revoke
                0x00000000       0x48 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_set_write_protect_of_digest_revoke
                0x00000000       0x26 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .rodata.esp_secure_boot_read_key_digests.str1.4
                0x00000000       0x24 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_secure_boot_read_key_digests
                0x00000000       0xa4 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .rodata.__func__.0
                0x00000000       0x21 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .rodata.__func__.1
                0x00000000       0x2d esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .rodata.__func__.2
                0x00000000       0x1c esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .rodata.__func__.3
                0x00000000       0x23 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .rodata.__func__.4
                0x00000000       0x1c esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .rodata.__func__.5
                0x00000000       0x1b esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .rodata.s_revoke_table
                0x00000000       0x24 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .rodata.s_table
                0x00000000       0x78 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .debug_info    0x00000000     0x1660 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .debug_abbrev  0x00000000      0x3f5 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .debug_loc     0x00000000      0xdc0 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .debug_aranges
                0x00000000       0xe0 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .debug_ranges  0x00000000      0x200 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .debug_line    0x00000000     0x14ff esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .debug_str     0x00000000     0x1190 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .comment       0x00000000       0x30 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .note.GNU-stack
                0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .debug_frame   0x00000000      0x324 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .riscv.attributes
                0x00000000       0x6c esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text          0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .data          0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .bss           0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_clear_program_registers
                0x00000000       0x18 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_check_errors
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.esp_efuse_utility_burn_chip_opt.str1.4
                0x00000000      0x1b0 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_burn_chip_opt
                0x00000000      0x2ae esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_burn_chip
                0x00000000        0xc esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.esp_efuse_utility_apply_new_coding_scheme.str1.4
                0x00000000       0x3f esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_apply_new_coding_scheme
                0x00000000       0xb4 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.range_write_addr_blocks
                0x00000000       0x58 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .bss.write_mass_blocks
                0x00000000      0x160 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.range_read_addr_blocks
                0x00000000       0x58 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_info    0x00000000      0x9b0 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_abbrev  0x00000000      0x2c8 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_loc     0x00000000      0x38c esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_aranges
                0x00000000       0x40 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_ranges  0x00000000      0x118 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_line    0x00000000      0xd24 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_str     0x00000000      0x87c esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .comment       0x00000000       0x30 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .note.GNU-stack
                0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_frame   0x00000000       0xdc esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .riscv.attributes
                0x00000000       0x6c esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text          0x00000000        0x0 esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
 .data          0x00000000        0x0 esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
 .bss           0x00000000        0x0 esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
 .text          0x00000000        0x0 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
 .data          0x00000000        0x0 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
 .bss           0x00000000        0x0 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
 .text          0x00000000        0x0 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .data          0x00000000        0x0 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .bss           0x00000000        0x0 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_clk_32k_bootstrap
                0x00000000        0x8 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_clk_32k_enabled
                0x00000000        0xe esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_clk_8m_enabled
                0x00000000        0xe esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_clk_lp_pll_enable
                0x00000000       0x2c esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_clk_lp_pll_src_set
                0x00000000       0x34 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_clk_cpu_freq_set_config_fast
                0x00000000       0x4a esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_clk_cpu_freq_set_xtal
                0x00000000       0x24 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_clk_cpu_set_to_default_config
                0x00000000       0x1c esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_clk_cpu_freq_set_xtal_for_sleep
                0x00000000       0x26 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_clk_apll_enable
                0x00000000       0x56 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .rodata.rtc_clk_apll_coeff_calc.str1.4
                0x00000000       0x5b esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_clk_apll_coeff_calc
                0x00000000      0x26a esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_clk_apll_coeff_set
                0x00000000       0xfc esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_dig_clk8m_enable
                0x00000000       0x1c esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_dig_clk8m_disable
                0x00000000       0x1a esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_dig_8m_enabled
                0x00000000        0xe esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .tcm.text.1    0x00000000       0x1e esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .tcm.text.2    0x00000000       0x22 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_clk_mpll_configure
                0x00000000      0x10a esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .tcm.text.3    0x00000000        0xa esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .tcm.data.0    0x00000000        0x4 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .srodata.cst4  0x00000000       0x20 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .srodata.cst8  0x00000000       0x20 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text          0x00000000        0x0 esp-idf/esp_rom/libesp_rom.a(esp_rom_regi2c_esp32p4.c.obj)
 .data          0x00000000        0x0 esp-idf/esp_rom/libesp_rom.a(esp_rom_regi2c_esp32p4.c.obj)
 .bss           0x00000000        0x0 esp-idf/esp_rom/libesp_rom.a(esp_rom_regi2c_esp32p4.c.obj)
 .iram1.1       0x00000000       0x4a esp-idf/esp_rom/libesp_rom.a(esp_rom_regi2c_esp32p4.c.obj)
 .text          0x00000000        0x0 esp-idf/esp_rom/libesp_rom.a(esp_rom_wdt.c.obj)
 .data          0x00000000        0x0 esp-idf/esp_rom/libesp_rom.a(esp_rom_wdt.c.obj)
 .bss           0x00000000        0x0 esp-idf/esp_rom/libesp_rom.a(esp_rom_wdt.c.obj)
 .text.wdt_hal_init
                0x00000000      0x1a4 esp-idf/esp_rom/libesp_rom.a(esp_rom_wdt.c.obj)
 .text.wdt_hal_deinit
                0x00000000       0x5c esp-idf/esp_rom/libesp_rom.a(esp_rom_wdt.c.obj)
 .debug_info    0x00000000     0x1e94 esp-idf/esp_rom/libesp_rom.a(esp_rom_wdt.c.obj)
 .debug_abbrev  0x00000000      0x377 esp-idf/esp_rom/libesp_rom.a(esp_rom_wdt.c.obj)
 .debug_loc     0x00000000      0x3ec esp-idf/esp_rom/libesp_rom.a(esp_rom_wdt.c.obj)
 .debug_aranges
                0x00000000       0x28 esp-idf/esp_rom/libesp_rom.a(esp_rom_wdt.c.obj)
 .debug_ranges  0x00000000      0x200 esp-idf/esp_rom/libesp_rom.a(esp_rom_wdt.c.obj)
 .debug_line    0x00000000      0xb76 esp-idf/esp_rom/libesp_rom.a(esp_rom_wdt.c.obj)
 .debug_str     0x00000000     0x1187 esp-idf/esp_rom/libesp_rom.a(esp_rom_wdt.c.obj)
 .comment       0x00000000       0x30 esp-idf/esp_rom/libesp_rom.a(esp_rom_wdt.c.obj)
 .note.GNU-stack
                0x00000000        0x0 esp-idf/esp_rom/libesp_rom.a(esp_rom_wdt.c.obj)
 .debug_frame   0x00000000       0x50 esp-idf/esp_rom/libesp_rom.a(esp_rom_wdt.c.obj)
 .riscv.attributes
                0x00000000       0x6c esp-idf/esp_rom/libesp_rom.a(esp_rom_wdt.c.obj)
 .text          0x00000000        0x0 esp-idf/log/liblog.a(log_timestamp.c.obj)
 .data          0x00000000        0x0 esp-idf/log/liblog.a(log_timestamp.c.obj)
 .bss           0x00000000        0x0 esp-idf/log/liblog.a(log_timestamp.c.obj)
 .text          0x00000000        0x0 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .data          0x00000000        0x0 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .bss           0x00000000        0x0 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .text.efuse_hal_get_mac
                0x00000000       0x14 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .iram1.3       0x00000000       0x10 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .text.efuse_hal_set_ecdsa_key
                0x00000000       0x2a esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .text          0x00000000        0x0 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .data          0x00000000        0x0 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .bss           0x00000000        0x0 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .text.efuse_hal_set_timing
                0x00000000        0x2 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .text.efuse_hal_read
                0x00000000       0x3a esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .text.efuse_hal_clear_program_registers
                0x00000000        0x8 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .text.efuse_hal_program
                0x00000000       0x5a esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .text.efuse_hal_rs_calculate
                0x00000000        0x8 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .text.efuse_hal_is_coding_error_in_block
                0x00000000       0x54 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .text          0x00000000        0x0 esp-idf/hal/libhal.a(lp_timer_hal.c.obj)
 .data          0x00000000        0x0 esp-idf/hal/libhal.a(lp_timer_hal.c.obj)
 .bss           0x00000000        0x0 esp-idf/hal/libhal.a(lp_timer_hal.c.obj)
 .iram1.1       0x00000000       0x46 esp-idf/hal/libhal.a(lp_timer_hal.c.obj)
 .iram1.3       0x00000000       0x14 esp-idf/hal/libhal.a(lp_timer_hal.c.obj)
 .iram1.4       0x00000000       0x14 esp-idf/hal/libhal.a(lp_timer_hal.c.obj)
 .text          0x00000000        0x0 esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .data          0x00000000        0x0 esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .bss           0x00000000        0x0 esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .text.mmu_hal_pages_to_bytes
                0x00000000        0x6 esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .text.mmu_hal_bytes_to_pages
                0x00000000        0x6 esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .text.mmu_hal_paddr_to_vaddr
                0x00000000       0xbe esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .text.mmu_hal_unmap_region
                0x00000000       0x8c esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .text.mmu_hal_vaddr_to_paddr
                0x00000000       0xba esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .text.mmu_hal_get_id_from_target
                0x00000000        0x8 esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .text.mmu_hal_get_id_from_vaddr
                0x00000000       0x2a esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .text          0x00000000        0x0 esp-idf/hal/libhal.a(cache_hal.c.obj)
 .data          0x00000000        0x0 esp-idf/hal/libhal.a(cache_hal.c.obj)
 .bss           0x00000000        0x0 esp-idf/hal/libhal.a(cache_hal.c.obj)
 .text.s_get_cache_state
                0x00000000       0x66 esp-idf/hal/libhal.a(cache_hal.c.obj)
 .text.cache_hal_suspend
                0x00000000       0x72 esp-idf/hal/libhal.a(cache_hal.c.obj)
 .text.cache_hal_resume
                0x00000000       0x88 esp-idf/hal/libhal.a(cache_hal.c.obj)
 .text.cache_hal_is_cache_enabled
                0x00000000        0x8 esp-idf/hal/libhal.a(cache_hal.c.obj)
 .text.cache_hal_vaddr_to_cache_level_id
                0x00000000       0x40 esp-idf/hal/libhal.a(cache_hal.c.obj)
 .text.cache_hal_invalidate_addr
                0x00000000       0x7a esp-idf/hal/libhal.a(cache_hal.c.obj)
 .text.cache_hal_writeback_addr
                0x00000000       0x6c esp-idf/hal/libhal.a(cache_hal.c.obj)
 .text.cache_hal_freeze
                0x00000000       0x5a esp-idf/hal/libhal.a(cache_hal.c.obj)
 .text.cache_hal_unfreeze
                0x00000000       0x50 esp-idf/hal/libhal.a(cache_hal.c.obj)
 .text.cache_hal_get_cache_line_size
                0x00000000       0x2c esp-idf/hal/libhal.a(cache_hal.c.obj)
 .text          0x00000000       0x28 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_lshrdi3.o)
 .data          0x00000000        0x0 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_lshrdi3.o)
 .bss           0x00000000        0x0 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_lshrdi3.o)
 .debug_info    0x00000000      0x1b6 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_lshrdi3.o)
 .debug_abbrev  0x00000000      0x10c /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_lshrdi3.o)
 .debug_loclists
                0x00000000       0x6b /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_lshrdi3.o)
 .debug_aranges
                0x00000000       0x20 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_lshrdi3.o)
 .debug_line    0x00000000      0x107 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_lshrdi3.o)
 .debug_str     0x00000000      0x1e6 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_lshrdi3.o)
 .debug_line_str
                0x00000000      0x1b6 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_lshrdi3.o)
 .comment       0x00000000       0x30 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_lshrdi3.o)
 .note.GNU-stack
                0x00000000        0x0 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_lshrdi3.o)
 .debug_frame   0x00000000       0x20 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_lshrdi3.o)
 .riscv.attributes
                0x00000000       0x61 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_lshrdi3.o)
 .text          0x00000000       0x28 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_ashldi3.o)
 .data          0x00000000        0x0 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_ashldi3.o)
 .bss           0x00000000        0x0 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_ashldi3.o)
 .debug_info    0x00000000      0x1b6 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_ashldi3.o)
 .debug_abbrev  0x00000000      0x10c /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_ashldi3.o)
 .debug_loclists
                0x00000000       0x6b /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_ashldi3.o)
 .debug_aranges
                0x00000000       0x20 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_ashldi3.o)
 .debug_line    0x00000000      0x107 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_ashldi3.o)
 .debug_str     0x00000000      0x1e6 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_ashldi3.o)
 .debug_line_str
                0x00000000      0x1b6 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_ashldi3.o)
 .comment       0x00000000       0x30 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_ashldi3.o)
 .note.GNU-stack
                0x00000000        0x0 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_ashldi3.o)
 .debug_frame   0x00000000       0x20 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_ashldi3.o)
 .riscv.attributes
                0x00000000       0x61 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_ashldi3.o)
 .text          0x00000000       0x42 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_popcountsi2.o)
 .data          0x00000000        0x0 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_popcountsi2.o)
 .bss           0x00000000        0x0 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_popcountsi2.o)
 .debug_info    0x00000000       0xe6 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_popcountsi2.o)
 .debug_abbrev  0x00000000       0x65 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_popcountsi2.o)
 .debug_loclists
                0x00000000       0xd6 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_popcountsi2.o)
 .debug_aranges
                0x00000000       0x20 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_popcountsi2.o)
 .debug_line    0x00000000       0xe9 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_popcountsi2.o)
 .debug_str     0x00000000      0x1ad /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_popcountsi2.o)
 .debug_line_str
                0x00000000      0x1b6 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_popcountsi2.o)
 .comment       0x00000000       0x30 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_popcountsi2.o)
 .note.GNU-stack
                0x00000000        0x0 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_popcountsi2.o)
 .debug_frame   0x00000000       0x20 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_popcountsi2.o)
 .riscv.attributes
                0x00000000       0x61 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_popcountsi2.o)
 .text          0x00000000      0x35a /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_udivdi3.o)
 .data          0x00000000        0x0 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_udivdi3.o)
 .bss           0x00000000        0x0 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_udivdi3.o)
 .debug_info    0x00000000      0x776 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_udivdi3.o)
 .debug_abbrev  0x00000000      0x1a6 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_udivdi3.o)
 .debug_loclists
                0x00000000      0x6ad /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_udivdi3.o)
 .debug_aranges
                0x00000000       0x20 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_udivdi3.o)
 .debug_rnglists
                0x00000000       0x8f /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_udivdi3.o)
 .debug_line    0x00000000      0x97b /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_udivdi3.o)
 .debug_str     0x00000000      0x25c /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_udivdi3.o)
 .debug_line_str
                0x00000000      0x1b6 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_udivdi3.o)
 .comment       0x00000000       0x30 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_udivdi3.o)
 .note.GNU-stack
                0x00000000        0x0 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_udivdi3.o)
 .eh_frame      0x00000000       0x28 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_udivdi3.o)
 .riscv.attributes
                0x00000000       0x61 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_udivdi3.o)
 .text          0x00000000      0x848 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(adddf3.o)
 .data          0x00000000        0x0 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(adddf3.o)
 .bss           0x00000000        0x0 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(adddf3.o)
 .debug_info    0x00000000      0x54a /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(adddf3.o)
 .debug_abbrev  0x00000000      0x18a /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(adddf3.o)
 .debug_loclists
                0x00000000     0x104b /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(adddf3.o)
 .debug_aranges
                0x00000000       0x20 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(adddf3.o)
 .debug_rnglists
                0x00000000      0x18e /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(adddf3.o)
 .debug_line    0x00000000     0x1b27 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(adddf3.o)
 .debug_str     0x00000000      0x296 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(adddf3.o)
 .debug_line_str
                0x00000000      0x1c2 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(adddf3.o)
 .comment       0x00000000       0x30 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(adddf3.o)
 .note.GNU-stack
                0x00000000        0x0 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(adddf3.o)
 .debug_frame   0x00000000       0x4c /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(adddf3.o)
 .riscv.attributes
                0x00000000       0x65 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(adddf3.o)
 .text          0x00000000      0x6a4 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(divdf3.o)
 .data          0x00000000        0x0 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(divdf3.o)
 .bss           0x00000000        0x0 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(divdf3.o)
 .rodata        0x00000000       0x3c /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(divdf3.o)
 .debug_info    0x00000000      0x6c2 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(divdf3.o)
 .debug_abbrev  0x00000000      0x17f /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(divdf3.o)
 .debug_loclists
                0x00000000      0xca3 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(divdf3.o)
 .debug_aranges
                0x00000000       0x20 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(divdf3.o)
 .debug_rnglists
                0x00000000       0xb9 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(divdf3.o)
 .debug_line    0x00000000     0x14bb /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(divdf3.o)
 .debug_str     0x00000000      0x358 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(divdf3.o)
 .debug_line_str
                0x00000000      0x1cc /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(divdf3.o)
 .comment       0x00000000       0x30 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(divdf3.o)
 .note.GNU-stack
                0x00000000        0x0 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(divdf3.o)
 .debug_frame   0x00000000       0x60 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(divdf3.o)
 .riscv.attributes
                0x00000000       0x65 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(divdf3.o)
 .text          0x00000000      0x5a8 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(muldf3.o)
 .data          0x00000000        0x0 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(muldf3.o)
 .bss           0x00000000        0x0 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(muldf3.o)
 .debug_info    0x00000000      0x6e0 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(muldf3.o)
 .debug_abbrev  0x00000000      0x191 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(muldf3.o)
 .debug_loclists
                0x00000000      0x96a /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(muldf3.o)
 .debug_aranges
                0x00000000       0x20 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(muldf3.o)
 .debug_rnglists
                0x00000000      0x144 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(muldf3.o)
 .debug_line    0x00000000     0x1289 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(muldf3.o)
 .debug_str     0x00000000      0x3b0 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(muldf3.o)
 .debug_line_str
                0x00000000      0x1cc /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(muldf3.o)
 .comment       0x00000000       0x30 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(muldf3.o)
 .note.GNU-stack
                0x00000000        0x0 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(muldf3.o)
 .debug_frame   0x00000000       0x60 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(muldf3.o)
 .riscv.attributes
                0x00000000       0x65 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(muldf3.o)
 .text          0x00000000       0xbe /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(fixdfsi.o)
 .data          0x00000000        0x0 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(fixdfsi.o)
 .bss           0x00000000        0x0 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(fixdfsi.o)
 .debug_info    0x00000000      0x1c3 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(fixdfsi.o)
 .debug_abbrev  0x00000000      0x145 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(fixdfsi.o)
 .debug_loclists
                0x00000000      0x12d /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(fixdfsi.o)
 .debug_aranges
                0x00000000       0x20 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(fixdfsi.o)
 .debug_rnglists
                0x00000000       0x2c /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(fixdfsi.o)
 .debug_line    0x00000000      0x319 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(fixdfsi.o)
 .debug_str     0x00000000      0x1a3 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(fixdfsi.o)
 .debug_line_str
                0x00000000      0x1cf /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(fixdfsi.o)
 .comment       0x00000000       0x30 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(fixdfsi.o)
 .note.GNU-stack
                0x00000000        0x0 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(fixdfsi.o)
 .debug_frame   0x00000000       0x20 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(fixdfsi.o)
 .riscv.attributes
                0x00000000       0x65 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(fixdfsi.o)
 .text          0x00000000       0x98 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(fixunsdfsi.o)
 .data          0x00000000        0x0 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(fixunsdfsi.o)
 .bss           0x00000000        0x0 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(fixunsdfsi.o)
 .debug_info    0x00000000      0x1ab /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(fixunsdfsi.o)
 .debug_abbrev  0x00000000      0x13b /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(fixunsdfsi.o)
 .debug_loclists
                0x00000000       0xe1 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(fixunsdfsi.o)
 .debug_aranges
                0x00000000       0x20 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(fixunsdfsi.o)
 .debug_rnglists
                0x00000000       0x1b /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(fixunsdfsi.o)
 .debug_line    0x00000000      0x2d5 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(fixunsdfsi.o)
 .debug_str     0x00000000      0x19f /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(fixunsdfsi.o)
 .debug_line_str
                0x00000000      0x1d8 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(fixunsdfsi.o)
 .comment       0x00000000       0x30 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(fixunsdfsi.o)
 .note.GNU-stack
                0x00000000        0x0 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(fixunsdfsi.o)
 .debug_frame   0x00000000       0x20 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(fixunsdfsi.o)
 .riscv.attributes
                0x00000000       0x65 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(fixunsdfsi.o)
 .text          0x00000000       0x74 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(floatsidf.o)
 .data          0x00000000        0x0 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(floatsidf.o)
 .bss           0x00000000        0x0 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(floatsidf.o)
 .debug_info    0x00000000      0x268 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(floatsidf.o)
 .debug_abbrev  0x00000000      0x164 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(floatsidf.o)
 .debug_loclists
                0x00000000       0x95 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(floatsidf.o)
 .debug_aranges
                0x00000000       0x20 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(floatsidf.o)
 .debug_rnglists
                0x00000000       0x2b /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(floatsidf.o)
 .debug_line    0x00000000      0x225 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(floatsidf.o)
 .debug_str     0x00000000      0x240 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(floatsidf.o)
 .debug_line_str
                0x00000000      0x1d5 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(floatsidf.o)
 .comment       0x00000000       0x30 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(floatsidf.o)
 .note.GNU-stack
                0x00000000        0x0 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(floatsidf.o)
 .debug_frame   0x00000000       0x40 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(floatsidf.o)
 .riscv.attributes
                0x00000000       0x61 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(floatsidf.o)
 .text          0x00000000       0x5a /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(floatunsidf.o)
 .data          0x00000000        0x0 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(floatunsidf.o)
 .bss           0x00000000        0x0 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(floatunsidf.o)
 .debug_info    0x00000000      0x25d /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(floatunsidf.o)
 .debug_abbrev  0x00000000      0x163 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(floatunsidf.o)
 .debug_loclists
                0x00000000       0xb3 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(floatunsidf.o)
 .debug_aranges
                0x00000000       0x20 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(floatunsidf.o)
 .debug_rnglists
                0x00000000       0x2b /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(floatunsidf.o)
 .debug_line    0x00000000      0x223 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(floatunsidf.o)
 .debug_str     0x00000000      0x23b /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(floatunsidf.o)
 .debug_line_str
                0x00000000      0x1db /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(floatunsidf.o)
 .comment       0x00000000       0x30 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(floatunsidf.o)
 .note.GNU-stack
                0x00000000        0x0 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(floatunsidf.o)
 .debug_frame   0x00000000       0x3c /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(floatunsidf.o)
 .riscv.attributes
                0x00000000       0x61 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(floatunsidf.o)
 .text          0x00000000       0xcc /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(extendsfdf2.o)
 .data          0x00000000        0x0 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(extendsfdf2.o)
 .bss           0x00000000        0x0 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(extendsfdf2.o)
 .debug_info    0x00000000      0x251 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(extendsfdf2.o)
 .debug_abbrev  0x00000000      0x14a /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(extendsfdf2.o)
 .debug_loclists
                0x00000000      0x139 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(extendsfdf2.o)
 .debug_aranges
                0x00000000       0x20 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(extendsfdf2.o)
 .debug_rnglists
                0x00000000       0x1a /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(extendsfdf2.o)
 .debug_line    0x00000000      0x3ab /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(extendsfdf2.o)
 .debug_str     0x00000000      0x1cc /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(extendsfdf2.o)
 .debug_line_str
                0x00000000      0x1da /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(extendsfdf2.o)
 .comment       0x00000000       0x30 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(extendsfdf2.o)
 .note.GNU-stack
                0x00000000        0x0 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(extendsfdf2.o)
 .debug_frame   0x00000000       0x44 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(extendsfdf2.o)
 .riscv.attributes
                0x00000000       0x65 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(extendsfdf2.o)
 .text          0x00000000        0x0 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_clz.o)
 .data          0x00000000        0x0 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_clz.o)
 .bss           0x00000000        0x0 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_clz.o)
 .rodata        0x00000000      0x100 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_clz.o)
 .debug_info    0x00000000       0xed /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_clz.o)
 .debug_abbrev  0x00000000       0x70 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_clz.o)
 .debug_aranges
                0x00000000       0x18 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_clz.o)
 .debug_line    0x00000000       0x3f /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_clz.o)
 .debug_str     0x00000000      0x1a9 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_clz.o)
 .debug_line_str
                0x00000000      0x1b6 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_clz.o)
 .comment       0x00000000       0x30 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_clz.o)
 .note.GNU-stack
                0x00000000        0x0 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_clz.o)
 .riscv.attributes
                0x00000000       0x61 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_clz.o)
 .text          0x00000000       0x3c /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_clzsi2.o)
 .data          0x00000000        0x0 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_clzsi2.o)
 .bss           0x00000000        0x0 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_clzsi2.o)
 .debug_info    0x00000000      0x157 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_clzsi2.o)
 .debug_abbrev  0x00000000       0xca /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_clzsi2.o)
 .debug_loclists
                0x00000000       0x44 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_clzsi2.o)
 .debug_aranges
                0x00000000       0x20 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_clzsi2.o)
 .debug_rnglists
                0x00000000       0x16 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_clzsi2.o)
 .debug_line    0x00000000       0xdb /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_clzsi2.o)
 .debug_str     0x00000000      0x1c6 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_clzsi2.o)
 .debug_line_str
                0x00000000      0x1b6 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_clzsi2.o)
 .comment       0x00000000       0x30 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_clzsi2.o)
 .note.GNU-stack
                0x00000000        0x0 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_clzsi2.o)
 .debug_frame   0x00000000       0x20 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_clzsi2.o)
 .riscv.attributes
                0x00000000       0x61 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_clzsi2.o)
 .text          0x00000000       0x48 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-memcmp.o)
 .data          0x00000000        0x0 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-memcmp.o)
 .bss           0x00000000        0x0 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-memcmp.o)
 .debug_info    0x00000000      0x10f /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-memcmp.o)
 .debug_abbrev  0x00000000       0x8a /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-memcmp.o)
 .debug_loclists
                0x00000000      0x130 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-memcmp.o)
 .debug_aranges
                0x00000000       0x20 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-memcmp.o)
 .debug_line    0x00000000      0x157 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-memcmp.o)
 .debug_str     0x00000000      0x115 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-memcmp.o)
 .debug_line_str
                0x00000000      0x277 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-memcmp.o)
 .comment       0x00000000       0x30 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-memcmp.o)
 .note.GNU-stack
                0x00000000        0x0 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-memcmp.o)
 .debug_frame   0x00000000       0x20 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-memcmp.o)
 .riscv.attributes
                0x00000000       0x61 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-memcmp.o)
 .text          0x00000000        0x0 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-impure.o)
 .data          0x00000000       0xf0 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-impure.o)
 .bss           0x00000000        0x0 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-impure.o)
 .sdata         0x00000000        0x4 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-impure.o)
 .debug_info    0x00000000      0x84c /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-impure.o)
 .debug_abbrev  0x00000000      0x174 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-impure.o)
 .debug_aranges
                0x00000000       0x18 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-impure.o)
 .debug_line    0x00000000       0x51 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-impure.o)
 .debug_str     0x00000000      0x4e5 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-impure.o)
 .debug_line_str
                0x00000000      0x27f /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-impure.o)
 .comment       0x00000000       0x30 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-impure.o)
 .note.GNU-stack
                0x00000000        0x0 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-impure.o)
 .riscv.attributes
                0x00000000       0x61 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-impure.o)
 .text          0x00000000       0xa8 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-memset.o)
 .data          0x00000000        0x0 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-memset.o)
 .bss           0x00000000        0x0 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-memset.o)
 .debug_line    0x00000000      0x18e /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-memset.o)
 .debug_line_str
                0x00000000       0xde /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-memset.o)
 .debug_info    0x00000000       0x33 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-memset.o)
 .debug_abbrev  0x00000000       0x28 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-memset.o)
 .debug_aranges
                0x00000000       0x20 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-memset.o)
 .debug_str     0x00000000       0xf3 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-memset.o)
 .riscv.attributes
                0x00000000       0x5f /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-memset.o)
 .text          0x00000000       0xe8 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-memcpy.o)
 .data          0x00000000        0x0 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-memcpy.o)
 .bss           0x00000000        0x0 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-memcpy.o)
 .debug_info    0x00000000      0x256 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-memcpy.o)
 .debug_abbrev  0x00000000      0x107 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-memcpy.o)
 .debug_loclists
                0x00000000      0x1d6 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-memcpy.o)
 .debug_aranges
                0x00000000       0x20 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-memcpy.o)
 .debug_line    0x00000000      0x31c /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-memcpy.o)
 .debug_str     0x00000000      0x136 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-memcpy.o)
 .debug_line_str
                0x00000000      0x344 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-memcpy.o)
 .comment       0x00000000       0x30 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-memcpy.o)
 .note.GNU-stack
                0x00000000        0x0 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-memcpy.o)
 .debug_frame   0x00000000       0x20 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-memcpy.o)
 .riscv.attributes
                0x00000000       0x61 /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-memcpy.o)

Memory Configuration

Name             Origin             Length             Attributes
iram_seg         0x4ff29ed0         0x00002d00         xrw
iram_loader_seg  0x4ff2cbd0         0x00007000         xrw
dram_seg         0x4ff33bd0         0x00005000         rw
*default*        0x00000000         0xffffffff

Linker script and memory map

                0x00000000                        IDF_TARGET_ESP32P4 = 0x0
LOAD CMakeFiles/bootloader.elf.dir/project_elf_src_esp32p4.c.obj
LOAD esp-idf/hal/libhal.a
LOAD esp-idf/riscv/libriscv.a
LOAD esp-idf/soc/libsoc.a
LOAD esp-idf/micro-ecc/libmicro-ecc.a
LOAD esp-idf/spi_flash/libspi_flash.a
LOAD esp-idf/esp_bootloader_format/libesp_bootloader_format.a
LOAD esp-idf/bootloader_support/libbootloader_support.a
LOAD esp-idf/efuse/libefuse.a
LOAD esp-idf/esp_system/libesp_system.a
LOAD esp-idf/esp_hw_support/libesp_hw_support.a
LOAD esp-idf/esp_common/libesp_common.a
LOAD esp-idf/esp_rom/libesp_rom.a
LOAD esp-idf/log/liblog.a
LOAD esp-idf/main/libmain.a
LOAD esp-idf/hal/libhal.a
LOAD esp-idf/riscv/libriscv.a
LOAD esp-idf/soc/libsoc.a
LOAD esp-idf/micro-ecc/libmicro-ecc.a
LOAD esp-idf/spi_flash/libspi_flash.a
LOAD esp-idf/esp_bootloader_format/libesp_bootloader_format.a
LOAD esp-idf/bootloader_support/libbootloader_support.a
LOAD esp-idf/efuse/libefuse.a
LOAD esp-idf/esp_system/libesp_system.a
LOAD esp-idf/esp_hw_support/libesp_hw_support.a
LOAD esp-idf/esp_common/libesp_common.a
LOAD esp-idf/esp_rom/libesp_rom.a
LOAD esp-idf/log/liblog.a
LOAD esp-idf/hal/libhal.a
LOAD esp-idf/riscv/libriscv.a
LOAD esp-idf/soc/libsoc.a
LOAD esp-idf/micro-ecc/libmicro-ecc.a
LOAD esp-idf/spi_flash/libspi_flash.a
LOAD esp-idf/esp_bootloader_format/libesp_bootloader_format.a
LOAD esp-idf/bootloader_support/libbootloader_support.a
LOAD esp-idf/efuse/libefuse.a
LOAD esp-idf/esp_system/libesp_system.a
LOAD esp-idf/esp_hw_support/libesp_hw_support.a
LOAD esp-idf/esp_common/libesp_common.a
LOAD esp-idf/esp_rom/libesp_rom.a
LOAD esp-idf/log/liblog.a
LOAD esp-idf/hal/libhal.a
LOAD esp-idf/riscv/libriscv.a
LOAD esp-idf/soc/libsoc.a
LOAD esp-idf/micro-ecc/libmicro-ecc.a
LOAD esp-idf/spi_flash/libspi_flash.a
LOAD esp-idf/esp_bootloader_format/libesp_bootloader_format.a
LOAD esp-idf/bootloader_support/libbootloader_support.a
LOAD esp-idf/efuse/libefuse.a
LOAD esp-idf/esp_system/libesp_system.a
LOAD esp-idf/esp_hw_support/libesp_hw_support.a
LOAD esp-idf/esp_common/libesp_common.a
LOAD esp-idf/esp_rom/libesp_rom.a
LOAD esp-idf/log/liblog.a
LOAD esp-idf/hal/libhal.a
LOAD esp-idf/riscv/libriscv.a
LOAD esp-idf/soc/libsoc.a
LOAD esp-idf/micro-ecc/libmicro-ecc.a
LOAD esp-idf/spi_flash/libspi_flash.a
LOAD esp-idf/esp_bootloader_format/libesp_bootloader_format.a
LOAD esp-idf/bootloader_support/libbootloader_support.a
LOAD esp-idf/efuse/libefuse.a
LOAD esp-idf/esp_system/libesp_system.a
LOAD esp-idf/esp_hw_support/libesp_hw_support.a
LOAD esp-idf/esp_common/libesp_common.a
LOAD esp-idf/esp_rom/libesp_rom.a
LOAD esp-idf/log/liblog.a
LOAD /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a
LOAD /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a
LOAD /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libnosys.a
LOAD /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a
LOAD /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a
                0x4fc00018                        rtc_get_reset_reason = 0x4fc00018
                0x4fc0001c                        rtc_get_wakeup_cause = 0x4fc0001c
                0x4fc00020                        pmu_enable_unhold_pads = 0x4fc00020
                0x4fc00024                        ets_printf = 0x4fc00024
                0x4fc00028                        ets_install_putc1 = 0x4fc00028
                0x4fc0002c                        ets_install_putc2 = 0x4fc0002c
                0x4fc00030                        ets_install_uart_printf = 0x4fc00030
                0x4fc00034                        ets_install_usb_printf = 0x4fc00034
                0x4fc00038                        ets_get_printf_channel = 0x4fc00038
                0x4fc0003c                        ets_delay_us = 0x4fc0003c
                0x4fc00040                        ets_get_cpu_frequency = 0x4fc00040
                0x4fc00044                        ets_update_cpu_frequency = 0x4fc00044
                0x4fc00048                        ets_install_lock = 0x4fc00048
                0x4fc0004c                        UartRxString = 0x4fc0004c
                0x4fc00050                        UartGetCmdLn = 0x4fc00050
                0x4fc00054                        uart_tx_one_char = 0x4fc00054
                0x4fc00058                        uart_tx_one_char2 = 0x4fc00058
                0x4fc0005c                        uart_tx_one_char3 = 0x4fc0005c
                0x4fc00060                        uart_rx_one_char = 0x4fc00060
                0x4fc00064                        uart_rx_one_char_block = 0x4fc00064
                0x4fc00068                        uart_rx_intr_handler = 0x4fc00068
                0x4fc0006c                        uart_rx_readbuff = 0x4fc0006c
                0x4fc00070                        uartAttach = 0x4fc00070
                0x4fc00074                        uart_tx_flush = 0x4fc00074
                0x4fc00078                        uart_tx_wait_idle = 0x4fc00078
                0x4fc0007c                        uart_div_modify = 0x4fc0007c
                0x4fc00080                        ets_write_char_uart = 0x4fc00080
                0x4fc00084                        uart_tx_switch = 0x4fc00084
                0x4fc00088                        uart_buff_switch = 0x4fc00088
                0x4fc0008c                        roundup2 = 0x4fc0008c
                0x4fc00090                        multofup = 0x4fc00090
                0x4fc00094                        software_reset = 0x4fc00094
                0x4fc00098                        software_reset_cpu = 0x4fc00098
                0x4fc0009c                        ets_clk_assist_debug_clock_enable = 0x4fc0009c
                0x4fc000a0                        clear_super_wdt_reset_flag = 0x4fc000a0
                0x4fc000a4                        disable_default_watchdog = 0x4fc000a4
                0x4fc000a8                        ets_set_appcpu_boot_addr = 0x4fc000a8
                0x4fc000ac                        send_packet = 0x4fc000ac
                0x4fc000b0                        recv_packet = 0x4fc000b0
                0x4fc000b4                        GetUartDevice = 0x4fc000b4
                0x4fc000b8                        UartDwnLdProc = 0x4fc000b8
                0x4fc000bc                        GetSecurityInfoProc = 0x4fc000bc
                0x4fc000c0                        Uart_Init = 0x4fc000c0
                0x4fc000c4                        ets_set_user_start = 0x4fc000c4
                0x4fc1fffc                        ets_rom_layout_p = 0x4fc1fffc
                0x4ff3fff4                        ets_ops_table_ptr = 0x4ff3fff4
                0x4ff3fff8                        g_saved_pc = 0x4ff3fff8
                0x4fc000c8                        mz_adler32 = 0x4fc000c8
                0x4fc000cc                        mz_free = 0x4fc000cc
                0x4fc000d0                        tdefl_compress = 0x4fc000d0
                0x4fc000d4                        tdefl_compress_buffer = 0x4fc000d4
                0x4fc000d8                        tdefl_compress_mem_to_heap = 0x4fc000d8
                0x4fc000dc                        tdefl_compress_mem_to_mem = 0x4fc000dc
                0x4fc000e0                        tdefl_compress_mem_to_output = 0x4fc000e0
                0x4fc000e4                        tdefl_get_adler32 = 0x4fc000e4
                0x4fc000e8                        tdefl_get_prev_return_status = 0x4fc000e8
                0x4fc000ec                        tdefl_init = 0x4fc000ec
                0x4fc000f0                        tdefl_write_image_to_png_file_in_memory = 0x4fc000f0
                0x4fc000f4                        tdefl_write_image_to_png_file_in_memory_ex = 0x4fc000f4
                0x4fc000f8                        tinfl_decompress = 0x4fc000f8
                0x4fc000fc                        tinfl_decompress_mem_to_callback = 0x4fc000fc
                0x4fc00100                        tinfl_decompress_mem_to_heap = 0x4fc00100
                0x4fc00104                        tinfl_decompress_mem_to_mem = 0x4fc00104
                0x4fc00108                        esp_rom_spi_cmd_config = 0x4fc00108
                0x4fc0010c                        esp_rom_spi_cmd_start = 0x4fc0010c
                0x4fc00110                        esp_rom_spi_set_op_mode = 0x4fc00110
                0x4fc00114                        esp_rom_spi_set_dtr_swap_mode = 0x4fc00114
                0x4fc00118                        esp_rom_spiflash_wait_idle = 0x4fc00118
                0x4fc0011c                        esp_rom_spiflash_write_encrypted = 0x4fc0011c
                0x4fc00120                        esp_rom_spiflash_write_encrypted_dest = 0x4fc00120
                0x4fc00124                        esp_rom_spiflash_write_encrypted_enable = 0x4fc00124
                0x4fc00128                        esp_rom_spiflash_write_encrypted_disable = 0x4fc00128
                0x4fc0012c                        esp_rom_spiflash_erase_chip = 0x4fc0012c
                0x4fc00130                        _esp_rom_spiflash_erase_sector = 0x4fc00130
                0x4fc00134                        _esp_rom_spiflash_erase_block = 0x4fc00134
                0x4fc00138                        _esp_rom_spiflash_write = 0x4fc00138
                0x4fc0013c                        _esp_rom_spiflash_read = 0x4fc0013c
                0x4fc00140                        _esp_rom_spiflash_unlock = 0x4fc00140
                0x4fc00144                        _SPIEraseArea = 0x4fc00144
                0x4fc00148                        _SPI_write_enable = 0x4fc00148
                0x4fc0014c                        esp_rom_spiflash_erase_sector = 0x4fc0014c
                0x4fc00150                        esp_rom_spiflash_erase_block = 0x4fc00150
                0x4fc00154                        esp_rom_spiflash_write = 0x4fc00154
                0x4fc00158                        esp_rom_spiflash_read = 0x4fc00158
                0x4fc0015c                        esp_rom_spiflash_unlock = 0x4fc0015c
                0x4fc00160                        SPIEraseArea = 0x4fc00160
                0x4fc00164                        SPI_write_enable = 0x4fc00164
                0x4fc00168                        esp_rom_spiflash_config_param = 0x4fc00168
                0x4fc0016c                        esp_rom_spiflash_read_user_cmd = 0x4fc0016c
                0x4fc00170                        esp_rom_spiflash_select_qio_pins = 0x4fc00170
                0x4fc00174                        esp_rom_spi_flash_auto_sus_res = 0x4fc00174
                0x4fc00178                        esp_rom_spi_flash_send_resume = 0x4fc00178
                0x4fc0017c                        esp_rom_spi_flash_update_id = 0x4fc0017c
                0x4fc00180                        esp_rom_spiflash_config_clk = 0x4fc00180
                0x4fc00184                        esp_rom_spiflash_config_readmode = 0x4fc00184
                0x4fc00188                        esp_rom_spiflash_read_status = 0x4fc00188
                0x4fc0018c                        esp_rom_spiflash_read_statushigh = 0x4fc0018c
                0x4fc00190                        esp_rom_spiflash_write_status = 0x4fc00190
                0x4fc00194                        esp_rom_spiflash_write_disable = 0x4fc00194
                0x4fc00198                        spi_cache_mode_switch = 0x4fc00198
                0x4fc0019c                        spi_common_set_dummy_output = 0x4fc0019c
                0x4fc001a0                        spi_common_set_flash_cs_timing = 0x4fc001a0
                0x4fc001a4                        esp_rom_spi_set_address_bit_len = 0x4fc001a4
                0x4fc001a8                        SPILock = 0x4fc001a8
                0x4fc001ac                        SPIMasterReadModeCnfig = 0x4fc001ac
                0x4fc001b0                        SPI_Common_Command = 0x4fc001b0
                0x4fc001b4                        SPI_WakeUp = 0x4fc001b4
                0x4fc001b8                        SPI_block_erase = 0x4fc001b8
                0x4fc001bc                        SPI_chip_erase = 0x4fc001bc
                0x4fc001c0                        SPI_init = 0x4fc001c0
                0x4fc001c4                        SPI_page_program = 0x4fc001c4
                0x4fc001c8                        SPI_read_data = 0x4fc001c8
                0x4fc001cc                        SPI_sector_erase = 0x4fc001cc
                0x4fc001d0                        SelectSpiFunction = 0x4fc001d0
                0x4fc001d4                        SetSpiDrvs = 0x4fc001d4
                0x4fc001d8                        Wait_SPI_Idle = 0x4fc001d8
                0x4fc001dc                        spi_dummy_len_fix = 0x4fc001dc
                0x4fc001e0                        Disable_QMode = 0x4fc001e0
                0x4fc001e4                        Enable_QMode = 0x4fc001e4
                0x4fc001e8                        spi_flash_attach = 0x4fc001e8
                0x4fc001ec                        spi_flash_get_chip_size = 0x4fc001ec
                0x4fc001f0                        spi_flash_guard_set = 0x4fc001f0
                0x4fc001f4                        spi_flash_guard_get = 0x4fc001f4
                0x4fc001f8                        spi_flash_read_encrypted = 0x4fc001f8
                0x4ff3ffec                        rom_spiflash_legacy_funcs = 0x4ff3ffec
                0x4ff3ffe8                        rom_spiflash_legacy_data = 0x4ff3ffe8
                0x4ff3fff0                        g_flash_guard_ops = 0x4ff3fff0
                0x4fc003c4                        Cache_Get_L1_ICache_Line_Size = 0x4fc003c4
                0x4fc003c8                        Cache_Get_L1_DCache_Line_Size = 0x4fc003c8
                0x4fc003cc                        Cache_Get_L2_Cache_Line_Size = 0x4fc003cc
                0x4fc003d0                        Cache_Get_Mode = 0x4fc003d0
                0x4fc003d4                        Cache_Set_L2_Cache_Mode = 0x4fc003d4
                0x4fc003d8                        Cache_Address_Through_Cache = 0x4fc003d8
                0x4fc003dc                        ROM_Boot_Cache_Init = 0x4fc003dc
                0x4fc003e0                        Cache_Sync_Addr = 0x4fc003e0
                0x4fc003e4                        Cache_Invalidate_Addr = 0x4fc003e4
                0x4fc003e8                        Cache_Invalidate_Addr_Gid = 0x4fc003e8
                0x4fc003ec                        Cache_Clean_Addr = 0x4fc003ec
                0x4fc003f0                        Cache_Clean_Addr_Gid = 0x4fc003f0
                0x4fc003f4                        Cache_WriteBack_Addr = 0x4fc003f4
                0x4fc003f8                        Cache_WriteBack_Addr_Gid = 0x4fc003f8
                0x4fc003fc                        Cache_WriteBack_Invalidate_Addr = 0x4fc003fc
                0x4fc00400                        Cache_WriteBack_Invalidate_Addr_Gid = 0x4fc00400
                0x4fc00404                        Cache_Invalidate_All = 0x4fc00404
                0x4fc00408                        Cache_Invalidate_All_Gid = 0x4fc00408
                0x4fc0040c                        Cache_Clean_All = 0x4fc0040c
                0x4fc00410                        Cache_Clean_All_Gid = 0x4fc00410
                0x4fc00414                        Cache_WriteBack_All = 0x4fc00414
                0x4fc00418                        Cache_WriteBack_All_Gid = 0x4fc00418
                0x4fc0041c                        Cache_WriteBack_Invalidate_All = 0x4fc0041c
                0x4fc00420                        Cache_WriteBack_Invalidate_All_Gid = 0x4fc00420
                0x4fc00424                        Cache_Mask_All = 0x4fc00424
                0x4fc00428                        Cache_Suspend_L1_CORE0_ICache_Autoload = 0x4fc00428
                0x4fc0042c                        Cache_Resume_L1_CORE0_ICache_Autoload = 0x4fc0042c
                0x4fc00430                        Cache_Suspend_L1_CORE1_ICache_Autoload = 0x4fc00430
                0x4fc00434                        Cache_Resume_L1_CORE1_ICache_Autoload = 0x4fc00434
                0x4fc00438                        Cache_Suspend_L1_DCache_Autoload = 0x4fc00438
                0x4fc0043c                        Cache_Resume_L1_DCache_Autoload = 0x4fc0043c
                0x4fc00440                        Cache_Suspend_L2_Cache_Autoload = 0x4fc00440
                0x4fc00444                        Cache_Resume_L2_Cache_Autoload = 0x4fc00444
                0x4fc00448                        Cache_Start_L1_CORE0_ICache_Preload = 0x4fc00448
                0x4fc0044c                        Cache_L1_CORE0_ICache_Preload_Done = 0x4fc0044c
                0x4fc00450                        Cache_End_L1_CORE0_ICache_Preload = 0x4fc00450
                0x4fc00454                        Cache_Start_L1_CORE1_ICache_Preload = 0x4fc00454
                0x4fc00458                        Cache_L1_CORE1_ICache_Preload_Done = 0x4fc00458
                0x4fc0045c                        Cache_End_L1_CORE1_ICache_Preload = 0x4fc0045c
                0x4fc00460                        Cache_Start_L1_DCache_Preload = 0x4fc00460
                0x4fc00464                        Cache_L1_DCache_Preload_Done = 0x4fc00464
                0x4fc00468                        Cache_End_L1_DCache_Preload = 0x4fc00468
                0x4fc0046c                        Cache_Start_L2_Cache_Preload = 0x4fc0046c
                0x4fc00470                        Cache_L2_Cache_Preload_Done = 0x4fc00470
                0x4fc00474                        Cache_End_L2_Cache_Preload = 0x4fc00474
                0x4fc00478                        Cache_Config_L1_CORE0_ICache_Autoload = 0x4fc00478
                0x4fc0047c                        Cache_Enable_L1_CORE0_ICache_Autoload = 0x4fc0047c
                0x4fc00480                        Cache_Disable_L1_CORE0_ICache_Autoload = 0x4fc00480
                0x4fc00484                        Cache_Config_L1_CORE1_ICache_Autoload = 0x4fc00484
                0x4fc00488                        Cache_Enable_L1_CORE1_ICache_Autoload = 0x4fc00488
                0x4fc0048c                        Cache_Disable_L1_CORE1_ICache_Autoload = 0x4fc0048c
                0x4fc00490                        Cache_Config_L1_DCache_Autoload = 0x4fc00490
                0x4fc00494                        Cache_Enable_L1_DCache_Autoload = 0x4fc00494
                0x4fc00498                        Cache_Disable_L1_DCache_Autoload = 0x4fc00498
                0x4fc0049c                        Cache_Config_L2_Cache_Autoload = 0x4fc0049c
                0x4fc004a0                        Cache_Enable_L2_Cache_Autoload = 0x4fc004a0
                0x4fc004a4                        Cache_Disable_L2_Cache_Autoload = 0x4fc004a4
                0x4fc004a8                        Cache_Enable_L1_CORE0_ICache_PreLock = 0x4fc004a8
                0x4fc004ac                        Cache_Disable_L1_CORE0_ICache_PreLock = 0x4fc004ac
                0x4fc004b0                        Cache_Enable_L1_CORE1_ICache_PreLock = 0x4fc004b0
                0x4fc004b4                        Cache_Disable_L1_CORE1_ICache_PreLock = 0x4fc004b4
                0x4fc004b8                        Cache_Enable_L1_DCache_PreLock = 0x4fc004b8
                0x4fc004bc                        Cache_Disable_L1_DCache_PreLock = 0x4fc004bc
                0x4fc004c0                        Cache_Enable_L2_Cache_PreLock = 0x4fc004c0
                0x4fc004c4                        Cache_Disable_L2_Cache_PreLock = 0x4fc004c4
                0x4fc004c8                        Cache_Lock_Addr = 0x4fc004c8
                0x4fc004cc                        Cache_Unlock_Addr = 0x4fc004cc
                0x4fc004d0                        Cache_Disable_L1_CORE0_ICache = 0x4fc004d0
                0x4fc004d4                        Cache_Enable_L1_CORE0_ICache = 0x4fc004d4
                0x4fc004d8                        Cache_Suspend_L1_CORE0_ICache = 0x4fc004d8
                0x4fc004dc                        Cache_Resume_L1_CORE0_ICache = 0x4fc004dc
                0x4fc004e0                        Cache_Disable_L1_CORE1_ICache = 0x4fc004e0
                0x4fc004e4                        Cache_Enable_L1_CORE1_ICache = 0x4fc004e4
                0x4fc004e8                        Cache_Suspend_L1_CORE1_ICache = 0x4fc004e8
                0x4fc004ec                        Cache_Resume_L1_CORE1_ICache = 0x4fc004ec
                0x4fc004f0                        Cache_Disable_L1_DCache = 0x4fc004f0
                0x4fc004f4                        Cache_Enable_L1_DCache = 0x4fc004f4
                0x4fc004f8                        Cache_Suspend_L1_DCache = 0x4fc004f8
                0x4fc004fc                        Cache_Resume_L1_DCache = 0x4fc004fc
                0x4fc00500                        Cache_Disable_L2_Cache = 0x4fc00500
                0x4fc00504                        Cache_Enable_L2_Cache = 0x4fc00504
                0x4fc00508                        Cache_Suspend_L2_Cache = 0x4fc00508
                0x4fc0050c                        Cache_Resume_L2_Cache = 0x4fc0050c
                0x4fc00510                        Cache_FLASH_MMU_Init = 0x4fc00510
                0x4fc00514                        Cache_PSRAM_MMU_Init = 0x4fc00514
                0x4fc00518                        Cache_FLASH_MMU_Set = 0x4fc00518
                0x4fc0051c                        Cache_FLASH_MMU_Set_Secure = 0x4fc0051c
                0x4fc00520                        Cache_PSRAM_MMU_Set = 0x4fc00520
                0x4fc00524                        Cache_PSRAM_MMU_Set_Secure = 0x4fc00524
                0x4fc00528                        Cache_Count_Flash_Pages = 0x4fc00528
                0x4fc0052c                        Cache_Flash_To_SPIRAM_Copy = 0x4fc0052c
                0x4fc00530                        Cache_Travel_Tag_Memory = 0x4fc00530
                0x4fc00534                        Cache_Travel_Tag_Memory2 = 0x4fc00534
                0x4fc00538                        Cache_Get_Virtual_Addr = 0x4fc00538
                0x4fc0053c                        Cache_Set_IDROM_MMU_Size = 0x4fc0053c
                0x4fc00540                        flash2spiram_instruction_offset = 0x4fc00540
                0x4fc00544                        flash2spiram_rodata_offset = 0x4fc00544
                0x4fc00548                        flash_instr_rodata_start_page = 0x4fc00548
                0x4fc0054c                        flash_instr_rodata_end_page = 0x4fc0054c
                0x4fc00550                        Cache_Set_IDROM_MMU_Info = 0x4fc00550
                0x4fc00554                        Cache_Get_IROM_MMU_End = 0x4fc00554
                0x4fc00558                        Cache_Get_DROM_MMU_End = 0x4fc00558
                0x4ff3ffdc                        rom_cache_op_cb = 0x4ff3ffdc
                0x4ff3ffd8                        rom_cache_internal_table_ptr = 0x4ff3ffd8
                0x4fc0055c                        ets_clk_get_xtal_freq = 0x4fc0055c
                0x4fc00560                        ets_clk_get_cpu_freq = 0x4fc00560
                0x4fc00564                        gpio_set_output_level = 0x4fc00564
                0x4fc00568                        gpio_get_input_level = 0x4fc00568
                0x4fc0056c                        gpio_matrix_in = 0x4fc0056c
                0x4fc00570                        gpio_matrix_out = 0x4fc00570
                0x4fc00574                        gpio_bypass_matrix_in = 0x4fc00574
                0x4fc00580                        gpio_pad_input_disable = 0x4fc00580
                0x4fc00584                        gpio_pad_input_enable = 0x4fc00584
                0x4fc00588                        gpio_pad_pulldown = 0x4fc00588
                0x4fc0058c                        gpio_pad_pullup = 0x4fc0058c
                0x4fc00590                        gpio_pad_select_gpio = 0x4fc00590
                0x4fc00594                        gpio_pad_set_drv = 0x4fc00594
                0x4fc00598                        gpio_pad_unhold = 0x4fc00598
                0x4fc0059c                        gpio_pad_hold = 0x4fc0059c
                0x4fc005a0                        gpio_lppad_select_mux = 0x4fc005a0
                0x4fc005a4                        gpio_ded_pad_set_drv = 0x4fc005a4
                0x4fc005a8                        gpio_ded_pad_pullup = 0x4fc005a8
                0x4fc005ac                        gpio_ded_pad_pulldown = 0x4fc005ac
                0x4fc005b0                        gpio_ded_pad_hold = 0x4fc005b0
                0x4fc005b4                        gpio_ded_pad_unhold = 0x4fc005b4
                0x4fc005b8                        esprv_intc_int_set_priority = 0x4fc005b8
                0x4fc005bc                        esprv_intc_int_set_threshold = 0x4fc005bc
                0x4fc005c0                        esprv_intc_int_enable = 0x4fc005c0
                0x4fc005c4                        esprv_intc_int_disable = 0x4fc005c4
                [!provide]                        PROVIDE (intr_handler_set = 0x4fc005cc)
                0x4fc005d0                        intr_matrix_set = 0x4fc005d0
                0x4fc005d4                        ets_intr_lock = 0x4fc005d4
                0x4fc005d8                        ets_intr_unlock = 0x4fc005d8
                0x4fc005dc                        ets_isr_attach = 0x4fc005dc
                0x4fc005e0                        ets_isr_mask = 0x4fc005e0
                0x4fc005e4                        ets_isr_unmask = 0x4fc005e4
                0x4fc005e8                        md5_vector = 0x4fc005e8
                0x4fc005ec                        MD5Init = 0x4fc005ec
                0x4fc005f0                        MD5Update = 0x4fc005f0
                0x4fc005f4                        MD5Final = 0x4fc005f4
                0x4fc005f8                        crc32_le = 0x4fc005f8
                0x4fc005fc                        crc16_le = 0x4fc005fc
                0x4fc00600                        crc8_le = 0x4fc00600
                0x4fc00604                        crc32_be = 0x4fc00604
                0x4fc00608                        crc16_be = 0x4fc00608
                0x4fc0060c                        crc8_be = 0x4fc0060c
                0x4fc00610                        esp_crc8 = 0x4fc00610
                0x4fc00614                        ets_sha_enable = 0x4fc00614
                0x4fc00618                        ets_sha_disable = 0x4fc00618
                0x4fc0061c                        ets_sha_get_state = 0x4fc0061c
                0x4fc00620                        ets_sha_init = 0x4fc00620
                0x4fc00624                        ets_sha_process = 0x4fc00624
                0x4fc00628                        ets_sha_starts = 0x4fc00628
                0x4fc0062c                        ets_sha_update = 0x4fc0062c
                0x4fc00630                        ets_sha_finish = 0x4fc00630
                0x4fc00634                        ets_sha_clone = 0x4fc00634
                0x4fc00638                        ets_hmac_enable = 0x4fc00638
                0x4fc0063c                        ets_hmac_disable = 0x4fc0063c
                0x4fc00640                        ets_hmac_calculate_message = 0x4fc00640
                0x4fc00644                        ets_hmac_calculate_downstream = 0x4fc00644
                0x4fc00648                        ets_hmac_invalidate_downstream = 0x4fc00648
                0x4fc0064c                        ets_jtag_enable_temporarily = 0x4fc0064c
                0x4fc00650                        ets_aes_enable = 0x4fc00650
                0x4fc00654                        ets_aes_disable = 0x4fc00654
                0x4fc00658                        ets_aes_setkey = 0x4fc00658
                0x4fc0065c                        ets_aes_block = 0x4fc0065c
                0x4fc00660                        ets_aes_setkey_dec = 0x4fc00660
                0x4fc00664                        ets_aes_setkey_enc = 0x4fc00664
                0x4fc00668                        ets_bigint_enable = 0x4fc00668
                0x4fc0066c                        ets_bigint_disable = 0x4fc0066c
                0x4fc00670                        ets_bigint_multiply = 0x4fc00670
                0x4fc00674                        ets_bigint_modmult = 0x4fc00674
                0x4fc00678                        ets_bigint_modexp = 0x4fc00678
                0x4fc0067c                        ets_bigint_wait_finish = 0x4fc0067c
                0x4fc00680                        ets_bigint_getz = 0x4fc00680
                0x4fc00684                        ets_ds_enable = 0x4fc00684
                0x4fc00688                        ets_ds_disable = 0x4fc00688
                0x4fc0068c                        ets_ds_start_sign = 0x4fc0068c
                0x4fc00690                        ets_ds_is_busy = 0x4fc00690
                0x4fc00694                        ets_ds_finish_sign = 0x4fc00694
                0x4fc00698                        ets_ds_encrypt_params = 0x4fc00698
                0x4fc0069c                        ets_mgf1_sha256 = 0x4fc0069c
                0x4fc1fff8                        crc32_le_table_ptr = 0x4fc1fff8
                0x4fc1fff4                        crc16_le_table_ptr = 0x4fc1fff4
                0x4fc1fff0                        crc8_le_table_ptr = 0x4fc1fff0
                0x4fc1ffec                        crc32_be_table_ptr = 0x4fc1ffec
                0x4fc1ffe8                        crc16_be_table_ptr = 0x4fc1ffe8
                0x4fc1ffe4                        crc8_be_table_ptr = 0x4fc1ffe4
                0x4fc006a0                        ets_efuse_read = 0x4fc006a0
                0x4fc006a4                        ets_efuse_program = 0x4fc006a4
                0x4fc006a8                        ets_efuse_clear_program_registers = 0x4fc006a8
                0x4fc006ac                        ets_efuse_write_key = 0x4fc006ac
                0x4fc006b0                        ets_efuse_get_read_register_address = 0x4fc006b0
                0x4fc006b4                        ets_efuse_get_key_purpose = 0x4fc006b4
                0x4fc006b8                        ets_efuse_key_block_unused = 0x4fc006b8
                0x4fc006bc                        ets_efuse_find_unused_key_block = 0x4fc006bc
                0x4fc006c0                        ets_efuse_rs_calculate = 0x4fc006c0
                0x4fc006c4                        ets_efuse_count_unused_key_blocks = 0x4fc006c4
                0x4fc006c8                        ets_efuse_secure_boot_enabled = 0x4fc006c8
                0x4fc006cc                        ets_efuse_secure_boot_aggressive_revoke_enabled = 0x4fc006cc
                0x4fc006d0                        ets_efuse_cache_encryption_enabled = 0x4fc006d0
                0x4fc006d4                        ets_efuse_download_modes_disabled = 0x4fc006d4
                0x4fc006d8                        ets_efuse_find_purpose = 0x4fc006d8
                0x4fc006dc                        ets_efuse_force_send_resume = 0x4fc006dc
                0x4fc006e0                        ets_efuse_get_flash_delay_us = 0x4fc006e0
                0x4fc006e4                        ets_efuse_get_uart_print_control = 0x4fc006e4
                0x4fc006e8                        ets_efuse_direct_boot_mode_disabled = 0x4fc006e8
                0x4fc006ec                        ets_efuse_security_download_modes_enabled = 0x4fc006ec
                0x4fc006f0                        ets_efuse_jtag_disabled = 0x4fc006f0
                0x4fc006f4                        ets_efuse_usb_print_is_disabled = 0x4fc006f4
                0x4fc006f8                        ets_efuse_usb_download_mode_disabled = 0x4fc006f8
                0x4fc006fc                        ets_efuse_usb_device_disabled = 0x4fc006fc
                0x4fc00700                        ets_efuse_get_km_huk_gen_state = 0x4fc00700
                0x4fc00704                        ets_efuse_get_km_deploy_only_once = 0x4fc00704
                0x4fc00708                        ets_efuse_get_force_use_km_key = 0x4fc00708
                0x4fc0070c                        ets_efuse_xts_key_length_256 = 0x4fc0070c
                0x4fc00710                        ets_efuse_get_km_key_lock = 0x4fc00710
                0x4fc00714                        esp_rom_check_recover_key = 0x4fc00714
                0x4fc00718                        esp_rom_km_huk_conf = 0x4fc00718
                0x4fc0071c                        esp_rom_km_huk_risk = 0x4fc0071c
                0x4fc00720                        ets_emsa_pss_verify = 0x4fc00720
                0x4fc00724                        ets_rsa_pss_verify = 0x4fc00724
                0x4fc00728                        ets_ecdsa_verify = 0x4fc00728
                0x4fc0072c                        ets_secure_boot_verify_bootloader_with_keys = 0x4fc0072c
                0x4fc00730                        ets_secure_boot_verify_signature = 0x4fc00730
                0x4fc00734                        ets_secure_boot_read_key_digests = 0x4fc00734
                0x4fc00738                        ets_secure_boot_revoke_public_key_digest = 0x4fc00738
                0x4fc008b0                        usb_serial_device_rx_one_char = 0x4fc008b0
                0x4fc008b4                        usb_serial_device_rx_one_char_block = 0x4fc008b4
                0x4fc008b8                        usb_serial_device_tx_flush = 0x4fc008b8
                0x4fc008bc                        usb_serial_device_tx_one_char = 0x4fc008bc
                0x4fc008c0                        Uart_Init_USB = 0x4fc008c0
                0x4fc008c4                        usb_serial_otg_rx_one_char = 0x4fc008c4
                0x4fc008c8                        usb_serial_otg_rx_one_char_block = 0x4fc008c8
                0x4fc008cc                        usb_serial_otg_tx_flush = 0x4fc008cc
                0x4fc008d0                        usb_serial_otg_tx_one_char = 0x4fc008d0
                0x4ff3ffd4                        uart_acm_dev = 0x4ff3ffd4
                0x4fc008d4                        cdc_acm_class_handle_req = 0x4fc008d4
                0x4fc008d8                        cdc_acm_init = 0x4fc008d8
                0x4fc008dc                        cdc_acm_fifo_fill = 0x4fc008dc
                0x4fc008e0                        cdc_acm_rx_fifo_cnt = 0x4fc008e0
                0x4fc008e4                        cdc_acm_fifo_read = 0x4fc008e4
                0x4fc008e8                        cdc_acm_irq_tx_enable = 0x4fc008e8
                0x4fc008ec                        cdc_acm_irq_tx_disable = 0x4fc008ec
                0x4fc008f0                        cdc_acm_irq_state_enable = 0x4fc008f0
                0x4fc008f4                        cdc_acm_irq_state_disable = 0x4fc008f4
                0x4fc008f8                        cdc_acm_irq_tx_ready = 0x4fc008f8
                0x4fc008fc                        cdc_acm_irq_rx_enable = 0x4fc008fc
                0x4fc00900                        cdc_acm_irq_rx_disable = 0x4fc00900
                0x4fc00904                        cdc_acm_irq_rx_ready = 0x4fc00904
                0x4fc00908                        cdc_acm_irq_is_pending = 0x4fc00908
                0x4fc0090c                        cdc_acm_irq_callback_set = 0x4fc0090c
                0x4fc00910                        cdc_acm_line_ctrl_set = 0x4fc00910
                0x4fc00914                        cdc_acm_line_ctrl_get = 0x4fc00914
                0x4fc00918                        cdc_acm_poll_out = 0x4fc00918
                0x4fc0091c                        chip_usb_dw_did_persist = 0x4fc0091c
                0x4fc00920                        chip_usb_dw_init = 0x4fc00920
                0x4fc00924                        chip_usb_detach = 0x4fc00924
                0x4fc00928                        chip_usb_dw_prepare_persist = 0x4fc00928
                0x4fc0092c                        chip_usb_get_persist_flags = 0x4fc0092c
                0x4fc00930                        chip_usb_set_persist_flags = 0x4fc00930
                0x4fc00934                        cpio_start = 0x4fc00934
                0x4fc00938                        cpio_feed = 0x4fc00938
                0x4fc0093c                        cpio_done = 0x4fc0093c
                0x4fc00940                        cpio_destroy = 0x4fc00940
                0x4fc00944                        dfu_flash_init = 0x4fc00944
                0x4fc00948                        dfu_flash_erase = 0x4fc00948
                0x4fc0094c                        dfu_flash_program = 0x4fc0094c
                0x4fc00950                        dfu_flash_read = 0x4fc00950
                0x4fc00954                        dfu_flash_attach = 0x4fc00954
                0x4fc00958                        dfu_cpio_callback = 0x4fc00958
                0x4fc0095c                        dfu_updater_get_err = 0x4fc0095c
                0x4fc00960                        dfu_updater_clear_err = 0x4fc00960
                0x4fc00964                        dfu_updater_enable = 0x4fc00964
                0x4fc00968                        dfu_updater_begin = 0x4fc00968
                0x4fc0096c                        dfu_updater_feed = 0x4fc0096c
                0x4fc00970                        dfu_updater_end = 0x4fc00970
                0x4fc00974                        dfu_updater_set_raw_addr = 0x4fc00974
                0x4fc00978                        dfu_updater_flash_read = 0x4fc00978
                0x4fc0097c                        usb_dc_prepare_persist = 0x4fc0097c
                0x4fc00980                        usb_dw_isr_handler = 0x4fc00980
                0x4fc00984                        usb_dc_attach = 0x4fc00984
                0x4fc00988                        usb_dc_detach = 0x4fc00988
                0x4fc0098c                        usb_dc_reset = 0x4fc0098c
                0x4fc00990                        usb_dc_set_address = 0x4fc00990
                0x4fc00994                        usb_dc_ep_check_cap = 0x4fc00994
                0x4fc00998                        usb_dc_ep_configure = 0x4fc00998
                0x4fc0099c                        usb_dc_ep_set_stall = 0x4fc0099c
                0x4fc009a0                        usb_dc_ep_clear_stall = 0x4fc009a0
                0x4fc009a4                        usb_dc_ep_halt = 0x4fc009a4
                0x4fc009a8                        usb_dc_ep_is_stalled = 0x4fc009a8
                0x4fc009ac                        usb_dc_ep_enable = 0x4fc009ac
                0x4fc009b0                        usb_dc_ep_disable = 0x4fc009b0
                0x4fc009b4                        usb_dc_ep_flush = 0x4fc009b4
                0x4fc009b8                        usb_dc_ep_write_would_block = 0x4fc009b8
                0x4fc009bc                        usb_dc_ep_write = 0x4fc009bc
                0x4fc009c0                        usb_dc_ep_read_wait = 0x4fc009c0
                0x4fc009c4                        usb_dc_ep_read_continue = 0x4fc009c4
                0x4fc009c8                        usb_dc_ep_read = 0x4fc009c8
                0x4fc009cc                        usb_dc_ep_set_callback = 0x4fc009cc
                0x4fc009d0                        usb_dc_set_status_callback = 0x4fc009d0
                0x4fc009d4                        usb_dc_ep_mps = 0x4fc009d4
                0x4fc009d8                        usb_dc_check_poll_for_interrupts = 0x4fc009d8
                0x4fc009dc                        mac_addr_to_serial_str_desc = 0x4fc009dc
                0x4fc009e0                        usb_set_current_descriptor = 0x4fc009e0
                0x4fc009e4                        usb_get_descriptor = 0x4fc009e4
                0x4fc009e8                        usb_dev_resume = 0x4fc009e8
                0x4fc009ec                        usb_dev_get_configuration = 0x4fc009ec
                0x4fc009f0                        usb_set_config = 0x4fc009f0
                0x4fc009f4                        usb_deconfig = 0x4fc009f4
                0x4fc009f8                        usb_enable = 0x4fc009f8
                0x4fc009fc                        usb_disable = 0x4fc009fc
                0x4fc00a00                        usb_write_would_block = 0x4fc00a00
                0x4fc00a04                        usb_write = 0x4fc00a04
                0x4fc00a08                        usb_read = 0x4fc00a08
                0x4fc00a0c                        usb_ep_set_stall = 0x4fc00a0c
                0x4fc00a10                        usb_ep_clear_stall = 0x4fc00a10
                0x4fc00a14                        usb_ep_read_wait = 0x4fc00a14
                0x4fc00a18                        usb_ep_read_continue = 0x4fc00a18
                0x4fc00a1c                        usb_transfer_ep_callback = 0x4fc00a1c
                0x4fc00a20                        usb_transfer = 0x4fc00a20
                0x4fc00a24                        usb_cancel_transfer = 0x4fc00a24
                0x4fc00a28                        usb_transfer_sync = 0x4fc00a28
                0x4fc00a2c                        usb_dfu_set_detach_cb = 0x4fc00a2c
                0x4fc00a30                        dfu_class_handle_req = 0x4fc00a30
                0x4fc00a34                        dfu_status_cb = 0x4fc00a34
                0x4fc00a38                        dfu_custom_handle_req = 0x4fc00a38
                0x4fc00a3c                        usb_dfu_init = 0x4fc00a3c
                0x4fc00a40                        usb_dfu_force_detach = 0x4fc00a40
                0x4fc00a44                        usb_dev_deinit = 0x4fc00a44
                0x4fc00a48                        usb_dw_ctrl_deinit = 0x4fc00a48
                0x4ff3ffc8                        s_usb_osglue = 0x4ff3ffc8
                0x4fc005f8                        PROVIDE (esp_rom_crc32_le = crc32_le)
                [!provide]                        PROVIDE (esp_rom_crc16_le = crc16_le)
                [!provide]                        PROVIDE (esp_rom_crc8_le = crc8_le)
                [!provide]                        PROVIDE (esp_rom_crc32_be = crc32_be)
                [!provide]                        PROVIDE (esp_rom_crc16_be = crc16_be)
                [!provide]                        PROVIDE (esp_rom_crc8_be = crc8_be)
                [!provide]                        PROVIDE (esp_rom_gpio_pad_select_gpio = gpio_pad_select_gpio)
                [!provide]                        PROVIDE (esp_rom_gpio_pad_pullup_only = gpio_pad_pullup)
                0x4fc00594                        PROVIDE (esp_rom_gpio_pad_set_drv = gpio_pad_set_drv)
                [!provide]                        PROVIDE (esp_rom_gpio_pad_unhold = gpio_pad_unhold)
                [!provide]                        PROVIDE (esp_rom_gpio_connect_in_signal = gpio_matrix_in)
                [!provide]                        PROVIDE (esp_rom_gpio_connect_out_signal = gpio_matrix_out)
                [!provide]                        PROVIDE (esp_rom_efuse_mac_address_crc8 = esp_crc8)
                [!provide]                        PROVIDE (esp_rom_efuse_is_secure_boot_enabled = ets_efuse_secure_boot_enabled)
                [!provide]                        PROVIDE (esp_rom_uart_flush_tx = uart_tx_flush)
                [!provide]                        PROVIDE (esp_rom_uart_tx_one_char = uart_tx_one_char2)
                [!provide]                        PROVIDE (esp_rom_uart_tx_wait_idle = uart_tx_wait_idle)
                [!provide]                        PROVIDE (esp_rom_uart_rx_one_char = uart_rx_one_char)
                [!provide]                        PROVIDE (esp_rom_uart_rx_string = UartRxString)
                [!provide]                        PROVIDE (esp_rom_uart_set_as_console = uart_tx_switch)
                [!provide]                        PROVIDE (esp_rom_uart_putc = ets_write_char_uart)
                [!provide]                        PROVIDE (esp_rom_usb_serial_putc = uart_tx_one_char3)
                0x4fc00074                        PROVIDE (esp_rom_output_flush_tx = uart_tx_flush)
                [!provide]                        PROVIDE (esp_rom_output_tx_one_char = uart_tx_one_char)
                0x4fc00078                        PROVIDE (esp_rom_output_tx_wait_idle = uart_tx_wait_idle)
                [!provide]                        PROVIDE (esp_rom_output_rx_one_char = uart_rx_one_char)
                [!provide]                        PROVIDE (esp_rom_output_rx_string = UartRxString)
                [!provide]                        PROVIDE (esp_rom_output_set_as_console = uart_tx_switch)
                [!provide]                        PROVIDE (esp_rom_output_putc = ets_write_char_uart)
                0x4fc005ec                        PROVIDE (esp_rom_md5_init = MD5Init)
                0x4fc005f0                        PROVIDE (esp_rom_md5_update = MD5Update)
                0x4fc005f4                        PROVIDE (esp_rom_md5_final = MD5Final)
                0x4fc00094                        PROVIDE (esp_rom_software_reset_system = software_reset)
                [!provide]                        PROVIDE (esp_rom_software_reset_cpu = software_reset_cpu)
                0x4fc00024                        PROVIDE (esp_rom_printf = ets_printf)
                0x4fc00030                        PROVIDE (esp_rom_install_uart_printf = ets_install_uart_printf)
                0x4fc0003c                        PROVIDE (esp_rom_delay_us = ets_delay_us)
                0x4fc00018                        PROVIDE (esp_rom_get_reset_reason = rtc_get_reset_reason)
                [!provide]                        PROVIDE (esp_rom_route_intr_matrix = intr_matrix_set)
                0x4fc00040                        PROVIDE (esp_rom_get_cpu_ticks_per_us = ets_get_cpu_frequency)
                0x4fc00044                        PROVIDE (esp_rom_set_cpu_ticks_per_us = ets_update_cpu_frequency)
                [!provide]                        PROVIDE (esp_rom_spiflash_attach = spi_flash_attach)
                [!provide]                        PROVIDE (esp_rom_spiflash_clear_bp = esp_rom_spiflash_unlock)
                [!provide]                        PROVIDE (esp_rom_spiflash_write_enable = SPI_write_enable)
                [!provide]                        PROVIDE (esp_rom_spiflash_erase_area = SPIEraseArea)
                [!provide]                        PROVIDE (esp_rom_spiflash_fix_dummylen = spi_dummy_len_fix)
                [!provide]                        PROVIDE (esp_rom_spiflash_set_drvs = SetSpiDrvs)
                [!provide]                        PROVIDE (esp_rom_spiflash_select_padsfunc = SelectSpiFunction)
                [!provide]                        PROVIDE (esp_rom_spiflash_common_cmd = SPI_Common_Command)
                0x4fc00868                        __adddf3 = 0x4fc00868
                0x4fc0086c                        __eqdf2 = 0x4fc0086c
                0x4fc00870                        __fixdfdi = 0x4fc00870
                0x4fc00874                        __fixdfsi = 0x4fc00874
                0x4fc0087c                        __fixunsdfsi = 0x4fc0087c
                0x4fc00884                        __floatdidf = 0x4fc00884
                0x4fc00888                        __floatsidf = 0x4fc00888
                0x4fc0088c                        __floatundidf = 0x4fc0088c
                0x4fc00890                        __floatunsidf = 0x4fc00890
                0x4fc00894                        __gedf2 = 0x4fc00894
                0x4fc00898                        __gtdf2 = 0x4fc00898
                0x4fc0089c                        __ledf2 = 0x4fc0089c
                0x4fc008a0                        __ltdf2 = 0x4fc008a0
                0x4fc008a4                        __muldf3 = 0x4fc008a4
                0x4fc008a8                        __nedf2 = 0x4fc008a8
                0x4fc008ac                        __subdf3 = 0x4fc008ac
                0x4fc007ac                        __fixsfdi = 0x4fc007ac
                0x4fc007b4                        __fixunssfdi = 0x4fc007b4
                0x4fc0073c                        __absvdi2 = 0x4fc0073c
                0x4fc00740                        __absvsi2 = 0x4fc00740
                0x4fc00748                        __addvdi3 = 0x4fc00748
                0x4fc0074c                        __addvsi3 = 0x4fc0074c
                0x4fc00750                        __ashldi3 = 0x4fc00750
                0x4fc00754                        __ashrdi3 = 0x4fc00754
                0x4fc00758                        __bswapdi2 = 0x4fc00758
                0x4fc0075c                        __bswapsi2 = 0x4fc0075c
                0x4fc00760                        __clear_cache = 0x4fc00760
                0x4fc00764                        __clrsbdi2 = 0x4fc00764
                0x4fc00768                        __clrsbsi2 = 0x4fc00768
                0x4fc0076c                        __clzdi2 = 0x4fc0076c
                0x4fc00770                        __clzsi2 = 0x4fc00770
                0x4fc00774                        __cmpdi2 = 0x4fc00774
                0x4fc00778                        __ctzdi2 = 0x4fc00778
                0x4fc0077c                        __ctzsi2 = 0x4fc0077c
                0x4fc00780                        __divdc3 = 0x4fc00780
                0x4fc00784                        __divdf3 = 0x4fc00784
                0x4fc00788                        __divdi3 = 0x4fc00788
                0x4fc0078c                        __divsc3 = 0x4fc0078c
                0x4fc00790                        __divsi3 = 0x4fc00790
                0x4fc00798                        __extendsfdf2 = 0x4fc00798
                0x4fc0079c                        __ffsdi2 = 0x4fc0079c
                0x4fc007a0                        __ffssi2 = 0x4fc007a0
                0x4fc007b8                        __fixunssfsi = 0x4fc007b8
                0x4fc007c0                        __floatdisf = 0x4fc007c0
                0x4fc007cc                        __floatundisf = 0x4fc007cc
                0x4fc007d4                        __gcc_bcmp = 0x4fc007d4
                0x4fc007e4                        __lshrdi3 = 0x4fc007e4
                0x4fc007ec                        __moddi3 = 0x4fc007ec
                0x4fc007f0                        __modsi3 = 0x4fc007f0
                0x4fc007f4                        __muldc3 = 0x4fc007f4
                0x4fc007fc                        __muldi3 = 0x4fc007fc
                0x4fc00800                        __mulsc3 = 0x4fc00800
                0x4fc00804                        __mulsi3 = 0x4fc00804
                0x4fc00808                        __mulvdi3 = 0x4fc00808
                0x4fc0080c                        __mulvsi3 = 0x4fc0080c
                0x4fc00814                        __negdf2 = 0x4fc00814
                0x4fc00818                        __negdi2 = 0x4fc00818
                0x4fc0081c                        __negvdi2 = 0x4fc0081c
                0x4fc00820                        __negvsi2 = 0x4fc00820
                0x4fc00824                        __paritysi2 = 0x4fc00824
                0x4fc00828                        __popcountdi2 = 0x4fc00828
                0x4fc0082c                        __popcountsi2 = 0x4fc0082c
                0x4fc00830                        __powidf2 = 0x4fc00830
                0x4fc00838                        __subvdi3 = 0x4fc00838
                0x4fc0083c                        __subvsi3 = 0x4fc0083c
                0x4fc00840                        __ucmpdi2 = 0x4fc00840
                0x4fc00844                        __udivdi3 = 0x4fc00844
                0x4fc00848                        __udivmoddi4 = 0x4fc00848
                0x4fc0084c                        __udivsi3 = 0x4fc0084c
                0x4fc00850                        __udiv_w_sdiv = 0x4fc00850
                0x4fc00854                        __umoddi3 = 0x4fc00854
                0x4fc00858                        __umodsi3 = 0x4fc00858
                0x4fc0085c                        __unorddf2 = 0x4fc0085c
                0x4fc00860                        __extenddftf2 = 0x4fc00860
                0x4fc00864                        __trunctfdf2 = 0x4fc00864
                0x4fc001fc                        wdt_hal_init = 0x4fc001fc
                0x4fc00200                        wdt_hal_deinit = 0x4fc00200
                0x4fc00204                        wdt_hal_config_stage = 0x4fc00204
                0x4fc00208                        wdt_hal_write_protect_disable = 0x4fc00208
                0x4fc0020c                        wdt_hal_write_protect_enable = 0x4fc0020c
                0x4fc00210                        wdt_hal_enable = 0x4fc00210
                0x4fc00214                        wdt_hal_disable = 0x4fc00214
                0x4fc00218                        wdt_hal_handle_intr = 0x4fc00218
                0x4fc0021c                        wdt_hal_feed = 0x4fc0021c
                0x4fc00220                        wdt_hal_set_flashboot_en = 0x4fc00220
                0x4fc00224                        wdt_hal_is_enabled = 0x4fc00224
                0x4fc00230                        systimer_hal_set_tick_rate_ops = 0x4fc00230
                0x4fc00234                        systimer_hal_get_counter_value = 0x4fc00234
                0x4fc00238                        systimer_hal_get_time = 0x4fc00238
                0x4fc0023c                        systimer_hal_set_alarm_target = 0x4fc0023c
                0x4fc00240                        systimer_hal_set_alarm_period = 0x4fc00240
                0x4fc00244                        systimer_hal_get_alarm_value = 0x4fc00244
                0x4fc00248                        systimer_hal_enable_alarm_int = 0x4fc00248
                0x4fc0024c                        systimer_hal_on_apb_freq_update = 0x4fc0024c
                0x4fc00250                        systimer_hal_counter_value_advance = 0x4fc00250
                0x4fc00254                        systimer_hal_enable_counter = 0x4fc00254
                0x4fc00258                        systimer_hal_select_alarm_mode = 0x4fc00258
                0x4fc0025c                        systimer_hal_connect_alarm_counter = 0x4fc0025c
                0x4fc00260                        systimer_hal_counter_can_stall_by_cpu = 0x4fc00260
                0x4fc00010                        _rom_chip_id = 0x4fc00010
                0x4fc00014                        _rom_eco_version = 0x4fc00014
                0x4fc00264                        esp_rom_newlib_init_common_mutexes = 0x4fc00264
                0x4fc00268                        memset = 0x4fc00268
                0x4fc00288                        strlen = 0x4fc00288
                0x4fc0028c                        strstr = 0x4fc0028c
                0x4fc00290                        bzero = 0x4fc00290
                0x4fc00298                        sbrk = 0x4fc00298
                0x4fc0029c                        isalnum = 0x4fc0029c
                0x4fc002a0                        isalpha = 0x4fc002a0
                0x4fc002a4                        isascii = 0x4fc002a4
                0x4fc002a8                        isblank = 0x4fc002a8
                0x4fc002ac                        iscntrl = 0x4fc002ac
                0x4fc002b0                        isdigit = 0x4fc002b0
                0x4fc002b4                        islower = 0x4fc002b4
                0x4fc002b8                        isgraph = 0x4fc002b8
                0x4fc002bc                        isprint = 0x4fc002bc
                0x4fc002c0                        ispunct = 0x4fc002c0
                0x4fc002c4                        isspace = 0x4fc002c4
                0x4fc002c8                        isupper = 0x4fc002c8
                0x4fc002cc                        toupper = 0x4fc002cc
                0x4fc002d0                        tolower = 0x4fc002d0
                0x4fc002d4                        toascii = 0x4fc002d4
                0x4fc002d8                        memccpy = 0x4fc002d8
                0x4fc002dc                        memchr = 0x4fc002dc
                0x4fc002e0                        memrchr = 0x4fc002e0
                0x4fc002e4                        strcasecmp = 0x4fc002e4
                0x4fc002e8                        strcasestr = 0x4fc002e8
                0x4fc002ec                        strcat = 0x4fc002ec
                0x4fc002f4                        strchr = 0x4fc002f4
                0x4fc002f8                        strcspn = 0x4fc002f8
                0x4fc002fc                        strcoll = 0x4fc002fc
                0x4fc00300                        strlcat = 0x4fc00300
                0x4fc00304                        strlcpy = 0x4fc00304
                0x4fc00308                        strlwr = 0x4fc00308
                0x4fc0030c                        strncasecmp = 0x4fc0030c
                0x4fc00310                        strncat = 0x4fc00310
                0x4fc00318                        strnlen = 0x4fc00318
                0x4fc0031c                        strrchr = 0x4fc0031c
                0x4fc00320                        strsep = 0x4fc00320
                0x4fc00324                        strspn = 0x4fc00324
                0x4fc00328                        strtok_r = 0x4fc00328
                0x4fc0032c                        strupr = 0x4fc0032c
                0x4fc00330                        longjmp = 0x4fc00330
                0x4fc00334                        setjmp = 0x4fc00334
                0x4fc00338                        abs = 0x4fc00338
                0x4fc0033c                        div = 0x4fc0033c
                0x4fc00340                        labs = 0x4fc00340
                0x4fc00344                        ldiv = 0x4fc00344
                0x4fc00348                        qsort = 0x4fc00348
                0x4fc00358                        utoa = 0x4fc00358
                0x4fc0035c                        itoa = 0x4fc0035c
                0x4ff3ffe4                        syscall_table_ptr = 0x4ff3ffe4
                0x4ff3ffe0                        _global_impure_ptr = 0x4ff3ffe0
                0x4fc0026c                        memcpy = 0x4fc0026c
                0x4fc00270                        memmove = 0x4fc00270
                0x4fc00274                        memcmp = 0x4fc00274
                0x4fc00278                        strcpy = 0x4fc00278
                0x4fc0027c                        strncpy = 0x4fc0027c
                0x4fc00280                        strcmp = 0x4fc00280
                0x4fc00284                        strncmp = 0x4fc00284
                0x4fc00294                        _isatty_r = 0x4fc00294
                0x4fc002f0                        strdup = 0x4fc002f0
                0x4fc00314                        strndup = 0x4fc00314
                0x4fc0034c                        rand_r = 0x4fc0034c
                0x4fc00350                        rand = 0x4fc00350
                0x4fc00354                        srand = 0x4fc00354
                0x4fc00360                        atoi = 0x4fc00360
                0x4fc00364                        atol = 0x4fc00364
                0x4fc00368                        strtol = 0x4fc00368
                0x4fc0036c                        strtoul = 0x4fc0036c
                0x4fc00370                        fflush = 0x4fc00370
                0x4fc00374                        _fflush_r = 0x4fc00374
                0x4fc00378                        _fwalk = 0x4fc00378
                0x4fc0037c                        _fwalk_reent = 0x4fc0037c
                0x4fc00380                        __smakebuf_r = 0x4fc00380
                0x4fc00384                        __swhatbuf_r = 0x4fc00384
                0x4fc00388                        __swbuf_r = 0x4fc00388
                0x4fc0038c                        __swbuf = 0x4fc0038c
                0x4fc00390                        __swsetup_r = 0x4fc00390
                [!provide]                        PROVIDE (esprv_int_set_priority = esprv_intc_int_set_priority)
                [!provide]                        PROVIDE (esprv_int_set_threshold = esprv_intc_int_set_threshold)
                [!provide]                        PROVIDE (esprv_int_enable = esprv_intc_int_enable)
                [!provide]                        PROVIDE (esprv_int_disable = esprv_intc_int_disable)
                [!provide]                        PROVIDE (esprv_int_set_type = esprv_intc_int_set_type)
                0x500ca000                        PROVIDE (UART0 = 0x500ca000)
                [!provide]                        PROVIDE (UART1 = 0x500cb000)
                [!provide]                        PROVIDE (UART2 = 0x500cc000)
                [!provide]                        PROVIDE (UART3 = 0x500cd000)
                [!provide]                        PROVIDE (UART4 = 0x500ce000)
                0x5008c000                        PROVIDE (SPIMEM0 = 0x5008c000)
                0x5008d000                        PROVIDE (SPIMEM1 = 0x5008d000)
                [!provide]                        PROVIDE (SPIMEM2 = 0x5008e000)
                [!provide]                        PROVIDE (SPIMEM3 = 0x5008f000)
                [!provide]                        PROVIDE (I2C0 = 0x500c4000)
                [!provide]                        PROVIDE (I2C1 = 0x500c5000)
                [!provide]                        PROVIDE (UHCI0 = 0x500df000)
                [!provide]                        PROVIDE (RMT = 0x500a2000)
                [!provide]                        PROVIDE (RMTMEM = 0x500a2800)
                [!provide]                        PROVIDE (AXI_ICM = 0x500a4000)
                [!provide]                        PROVIDE (AXI_ICM_QOS = 0x500a4400)
                [!provide]                        PROVIDE (HP_PERI_PMS = 0x500a5000)
                [!provide]                        PROVIDE (LP2HP_PERI_PMS = 0x500a5800)
                [!provide]                        PROVIDE (DMA_PMS = 0x500a6000)
                [!provide]                        PROVIDE (LEDC = 0x500d3000)
                [!provide]                        PROVIDE (LEDC_GAMMA_RAM = 0x500d3400)
                0x500c2000                        PROVIDE (TIMERG0 = 0x500c2000)
                0x500c3000                        PROVIDE (TIMERG1 = 0x500c3000)
                [!provide]                        PROVIDE (SYSTIMER = 0x500e2000)
                [!provide]                        PROVIDE (TWAI0 = 0x500d7000)
                [!provide]                        PROVIDE (I2S0 = 0x500c6000)
                [!provide]                        PROVIDE (I2S1 = 0x500c7000)
                [!provide]                        PROVIDE (I2S2 = 0x500c8000)
                [!provide]                        PROVIDE (TWAI1 = 0x500d8000)
                [!provide]                        PROVIDE (TWAI2 = 0x500d9000)
                [!provide]                        PROVIDE (ADC = 0x500de000)
                [!provide]                        PROVIDE (USB_SERIAL_JTAG = 0x500d2000)
                [!provide]                        PROVIDE (SDMMC = 0x50083000)
                [!provide]                        PROVIDE (BITSCRAMBLER = 0x500a3000)
                [!provide]                        PROVIDE (INTMTX = 0x500d6000)
                [!provide]                        PROVIDE (PCNT = 0x500c9000)
                [!provide]                        PROVIDE (SOC_ETM = 0x500d5000)
                [!provide]                        PROVIDE (MCPWM0 = 0x500c0000)
                [!provide]                        PROVIDE (MCPWM1 = 0x500c1000)
                [!provide]                        PROVIDE (PARL_IO = 0x500cf000)
                [!provide]                        PROVIDE (PVT_MONITOR = 0x5009e000)
                [!provide]                        PROVIDE (GPSPI2 = 0x500d0000)
                [!provide]                        PROVIDE (GPSPI3 = 0x500d1000)
                [!provide]                        PROVIDE (AES = 0x50090000)
                [!provide]                        PROVIDE (SHA = 0x50091000)
                [!provide]                        PROVIDE (RSA = 0x50092000)
                [!provide]                        PROVIDE (ECC = 0x50093000)
                [!provide]                        PROVIDE (DS = 0x50094000)
                [!provide]                        PROVIDE (HMAC = 0x50095000)
                [!provide]                        PROVIDE (ECDSA = 0x50096000)
                [!provide]                        PROVIDE (GPIO = 0x500e0000)
                [!provide]                        PROVIDE (GPIO_EXT = 0x500e0f00)
                [!provide]                        PROVIDE (SDM = 0x500e0f00)
                [!provide]                        PROVIDE (GLITCH_FILTER = 0x500e0f30)
                [!provide]                        PROVIDE (GPIO_ETM = 0x500e0f60)
                [!provide]                        PROVIDE (IO_MUX = 0x500e1000)
                [!provide]                        PROVIDE (MSPI_IOMUX = 0x500e1200)
                [!provide]                        PROVIDE (HP_SYSTEM = 0x500e5000)
                0x500e6000                        PROVIDE (HP_SYS_CLKRST = 0x500e6000)
                0x50115000                        PROVIDE (PMU = 0x50115000)
                [!provide]                        PROVIDE (LP_SYS = 0x50110000)
                0x50111000                        PROVIDE (LP_AON_CLKRST = 0x50111000)
                0x5012d000                        PROVIDE (EFUSE = 0x5012d000)
                0x50120000                        PROVIDE (LPPERI = 0x50120000)
                0x50112000                        PROVIDE (LP_TIMER = 0x50112000)
                0x50121000                        PROVIDE (LP_UART = 0x50121000)
                [!provide]                        PROVIDE (LP_I2C = 0x50122000)
                [!provide]                        PROVIDE (LP_SPI = 0x50123000)
                0x50116000                        PROVIDE (LP_WDT = 0x50116000)
                [!provide]                        PROVIDE (LP_I2S = 0x50125000)
                [!provide]                        PROVIDE (LP_ADC = 0x50127000)
                [!provide]                        PROVIDE (LP_TOUCH = 0x50128000)
                [!provide]                        PROVIDE (LP_GPIO = 0x5012a000)
                [!provide]                        PROVIDE (LP_PERI_PMS = 0x5012e000)
                [!provide]                        PROVIDE (HP2LP_PERI_PMS = 0x5012e800)
                0x50124000                        PROVIDE (I2C_ANA_MST = 0x50124000)
                0x50113000                        PROVIDE (LP_ANA_PERI = 0x50113000)
                [!provide]                        PROVIDE (AHB_DMA = 0x50085000)
                [!provide]                        PROVIDE (AXI_DMA = 0x5008a000)
                [!provide]                        PROVIDE (LCD_CAM = 0x500dc000)
                [!provide]                        PROVIDE (LP_IOMUX = 0x5012b000)
                [!provide]                        PROVIDE (LP_TSENS = 0x5012f000)
                [!provide]                        PROVIDE (MIPI_CSI_BRIDGE = 0x5009f800)
                [!provide]                        PROVIDE (MIPI_CSI_HOST = 0x5009f000)
                [!provide]                        PROVIDE (MIPI_DSI_BRIDGE = 0x500a0800)
                [!provide]                        PROVIDE (MIPI_DSI_HOST = 0x500a0000)
                [!provide]                        PROVIDE (MIPI_CSI_MEM = 0x50104000)
                [!provide]                        PROVIDE (MIPI_DSI_MEM = 0x50105000)
                [!provide]                        PROVIDE (ISP = 0x500a1000)
                [!provide]                        PROVIDE (DW_GDMA = 0x50081000)
                [!provide]                        PROVIDE (PAU = 0x50082000)
                [!provide]                        PROVIDE (I3C_MST = 0x500da000)
                [!provide]                        PROVIDE (I3C_MST_MEM = 0x500da000)
                [!provide]                        PROVIDE (I3C_SLV = 0x500db000)
                [!provide]                        PROVIDE (JPEG = 0x50086000)
                [!provide]                        PROVIDE (PPA = 0x50087000)
                [!provide]                        PROVIDE (DMA2D = 0x50088000)
                [!provide]                        PROVIDE (USB_WRAP = 0x50080000)
                [!provide]                        PROVIDE (USB_DWC_HS = 0x50000000)
                [!provide]                        PROVIDE (USB_DWC_FS = 0x50040000)
                [!provide]                        PROVIDE (USB_UTMI = 0x5009c000)
                [!provide]                        PROVIDE (EMAC_MAC = 0x50098000)
                [!provide]                        PROVIDE (EMAC_PTP = 0x50098700)
                [!provide]                        PROVIDE (EMAC_DMA = 0x50099000)
                [!provide]                        PROVIDE (CACHE = 0x3ff10000)
                [!provide]                        PROVIDE (TRACE0 = 0x3ff04000)
                [!provide]                        PROVIDE (TRACE1 = 0x3ff05000)
                0x4ff3abd0                        bootloader_usable_dram_end = 0x4ff3abd0
                0x00002000                        bootloader_stack_overhead = 0x2000
                0x00005000                        bootloader_dram_seg_len = 0x5000
                0x00007000                        bootloader_iram_loader_seg_len = 0x7000
                0x00002d00                        bootloader_iram_seg_len = 0x2d00
                0x4ff38bd0                        bootloader_dram_seg_end = (bootloader_usable_dram_end - bootloader_stack_overhead)
                0x4ff33bd0                        bootloader_dram_seg_start = (bootloader_dram_seg_end - bootloader_dram_seg_len)
                0x4ff2cbd0                        bootloader_iram_loader_seg_start = (bootloader_dram_seg_start - bootloader_iram_loader_seg_len)
                0x4ff29ed0                        bootloader_iram_seg_start = (bootloader_iram_loader_seg_start - bootloader_iram_seg_len)
                0x00000001                        ASSERT ((bootloader_iram_loader_seg_start == 0x4ff2cbd0), bootloader_iram_loader_seg_start inconsistent with SRAM_DRAM_END)

.iram_loader.text
                0x4ff2cbd0     0x3362
                0x4ff2cbd0                        . = ALIGN (0x10)
                0x4ff2cbd0                        _loader_text_start = ABSOLUTE (.)
 *(.stub .gnu.warning .gnu.linkonce.literal.* .gnu.linkonce.t.*.literal .gnu.linkonce.t.*)
 *(.iram1 .iram1.*)
 .iram1.0       0x4ff2cbd0        0x4 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
                0x4ff2cbd0                esp_flash_encryption_enabled
 .iram1.5       0x4ff2cbd4       0x50 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .iram1.1       0x4ff2cc24      0x22e esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                0x4ff2cc24                bootloader_flash_execute_command_common
 .iram1.2       0x4ff2ce52        0xe esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                0x4ff2ce52                bootloader_execute_flash_command
 .iram1.0       0x4ff2ce60      0x156 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                0x4ff2ce60                bootloader_flash_unlock
                0x4ff2ce60                bootloader_flash_unlock_default
 .iram1.3       0x4ff2cfb6       0x40 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                0x4ff2cfb6                bootloader_flash_read_sfdp
 .iram1.4       0x4ff2cff6       0x34 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                0x4ff2cff6                bootloader_read_flash_id
 .iram1.6       0x4ff2d02a       0xca esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                0x4ff2d02a                bootloader_flash_xmc_startup
 .iram1.0       0x4ff2d0f4       0x1a esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32p4.c.obj)
                0x4ff2d0f4                bootloader_flash_update_id
 .iram1.1       0x4ff2d10e       0x20 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32p4.c.obj)
                0x4ff2d10e                bootloader_flash_cs_timing_config
 .iram1.2       0x4ff2d12e       0x48 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32p4.c.obj)
                0x4ff2d12e                bootloader_init_mspi_clock
 .iram1.4       0x4ff2d176       0x62 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32p4.c.obj)
                0x4ff2d176                bootloader_configure_spi_pins
 .iram1.0       0x4ff2d1d8       0xe2 esp-idf/esp_rom/libesp_rom.a(esp_rom_regi2c_esp32p4.c.obj)
 .iram1.2       0x4ff2d2ba       0x8e esp-idf/esp_rom/libesp_rom.a(esp_rom_regi2c_esp32p4.c.obj)
                0x4ff2d2ba                esp_rom_regi2c_read_mask
                0x4ff2d2ba                regi2c_read_mask_impl
 .iram1.3       0x4ff2d348       0x4c esp-idf/esp_rom/libesp_rom.a(esp_rom_regi2c_esp32p4.c.obj)
                0x4ff2d348                regi2c_write_impl
                0x4ff2d348                esp_rom_regi2c_write
 .iram1.4       0x4ff2d394       0xce esp-idf/esp_rom/libesp_rom.a(esp_rom_regi2c_esp32p4.c.obj)
                0x4ff2d394                esp_rom_regi2c_write_mask
                0x4ff2d394                regi2c_write_mask_impl
 .iram1.0       0x4ff2d462       0x1e esp-idf/hal/libhal.a(efuse_hal.c.obj)
                0x4ff2d462                efuse_hal_chip_revision
 .iram1.1       0x4ff2d480       0x20 esp-idf/hal/libhal.a(efuse_hal.c.obj)
                0x4ff2d480                efuse_hal_blk_version
 .iram1.2       0x4ff2d4a0       0x10 esp-idf/hal/libhal.a(efuse_hal.c.obj)
                0x4ff2d4a0                efuse_hal_get_disable_wafer_version_major
 .iram1.4       0x4ff2d4b0       0x10 esp-idf/hal/libhal.a(efuse_hal.c.obj)
                0x4ff2d4b0                efuse_hal_get_disable_blk_version_major
 .iram1.5       0x4ff2d4c0       0x1e esp-idf/hal/libhal.a(efuse_hal.c.obj)
                0x4ff2d4c0                efuse_hal_flash_encryption_enabled
 .iram1.0       0x4ff2d4de       0x10 esp-idf/hal/libhal.a(efuse_hal.c.obj)
                0x4ff2d4de                efuse_hal_get_major_chip_version
 .iram1.1       0x4ff2d4ee        0xe esp-idf/hal/libhal.a(efuse_hal.c.obj)
                0x4ff2d4ee                efuse_hal_get_minor_chip_version
 .iram1.2       0x4ff2d4fc       0x28 esp-idf/hal/libhal.a(lp_timer_hal.c.obj)
                0x4ff2d4fc                lp_timer_hal_get_cycle_count
 *liblog.a:(.literal .text .literal.* .text.*)
 .text.esp_log_early_timestamp
                0x4ff2d524       0x26 esp-idf/log/liblog.a(log_timestamp.c.obj)
                0x4ff2d524                esp_log_timestamp
                0x4ff2d524                esp_log_early_timestamp
 *libgcc.a:(.literal .text .literal.* .text.*)
 *libbootloader_support.a:bootloader_clock_loader.*(.literal .text .literal.* .text.*)
 *libbootloader_support.a:bootloader_common_loader.*(.literal .text .literal.* .text.*)
 .text.bootloader_common_check_chip_revision_validity
                0x4ff2d54a       0xc2 esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                0x4ff2d54a                bootloader_common_check_chip_revision_validity
 .text.bootloader_common_ota_select_crc
                0x4ff2d60c        0xe esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                0x4ff2d60c                bootloader_common_ota_select_crc
 .text.bootloader_common_ota_select_invalid
                0x4ff2d61a       0x16 esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                0x4ff2d61a                bootloader_common_ota_select_invalid
 .text.bootloader_common_ota_select_valid
                0x4ff2d630       0x2a esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                0x4ff2d630                bootloader_common_ota_select_valid
 .text.bootloader_common_check_efuse_blk_validity
                0x4ff2d65a       0xb4 esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                0x4ff2d65a                bootloader_common_check_efuse_blk_validity
 .text.bootloader_common_check_chip_validity
                0x4ff2d70e       0x64 esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                0x4ff2d70e                bootloader_common_check_chip_validity
 .text.bootloader_common_select_otadata
                0x4ff2d772       0x3e esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                0x4ff2d772                bootloader_common_select_otadata
 .text.bootloader_common_get_active_otadata
                0x4ff2d7b0       0x2e esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                0x4ff2d7b0                bootloader_common_get_active_otadata
 *libbootloader_support.a:bootloader_flash.*(.literal .text .literal.* .text.*)
 .text.spi_to_esp_err
                0x4ff2d7de       0x24 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .text.bootloader_mmap_get_free_pages
                0x4ff2d802        0x6 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                0x4ff2d802                bootloader_mmap_get_free_pages
 .text.bootloader_mmap
                0x4ff2d808       0xfc esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                0x4ff2d808                bootloader_mmap
 .text.bootloader_munmap
                0x4ff2d904       0x28 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                0x4ff2d904                bootloader_munmap
 .text.bootloader_flash_read
                0x4ff2d92c      0x178 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                0x4ff2d92c                bootloader_flash_read
 .text.bootloader_flash_erase_sector
                0x4ff2daa4       0x12 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                0x4ff2daa4                bootloader_flash_erase_sector
 .text.bootloader_flash_write
                0x4ff2dab6       0xd0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                0x4ff2dab6                bootloader_flash_write
 .text.bootloader_enable_wp
                0x4ff2db86        0xc esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                0x4ff2db86                bootloader_enable_wp
 .text.bootloader_flash_get_spi_mode
                0x4ff2db92       0x3e esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                0x4ff2db92                bootloader_flash_get_spi_mode
 *libbootloader_support.a:bootloader_random.*(.literal .text .literal.* .text.*)
 .text.bootloader_fill_random
                0x4ff2dbd0       0x80 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
                0x4ff2dbd0                bootloader_fill_random
 *libbootloader_support.a:bootloader_random*.*(.literal.bootloader_random_disable .text.bootloader_random_disable)
 .text.bootloader_random_disable
                0x4ff2dc50       0x50 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32p4.c.obj)
                0x4ff2dc50                bootloader_random_disable
 *libbootloader_support.a:bootloader_random*.*(.literal.bootloader_random_enable .text.bootloader_random_enable)
 .text.bootloader_random_enable
                0x4ff2dca0      0x11e esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32p4.c.obj)
                0x4ff2dca0                bootloader_random_enable
 *libbootloader_support.a:bootloader_efuse.*(.literal .text .literal.* .text.*)
 *libbootloader_support.a:bootloader_utility.*(.literal .text .literal.* .text.*)
 .text.log_invalid_app_partition
                0x4ff2ddbe       0x84 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .text.load_image
                0x4ff2de42      0x17c esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .text.index_to_partition
                0x4ff2dfbe       0x46 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .text.try_load_partition
                0x4ff2e004       0x44 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .text.set_actual_ota_seq
                0x4ff2e048       0xae esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .text.bootloader_common_read_otadata
                0x4ff2e0f6       0xa2 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
                0x4ff2e0f6                bootloader_common_read_otadata
 .text.bootloader_utility_load_partition_table
                0x4ff2e198      0x27e esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
                0x4ff2e198                bootloader_utility_load_partition_table
 .text.bootloader_utility_get_selected_boot_partition
                0x4ff2e416      0x112 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
                0x4ff2e416                bootloader_utility_get_selected_boot_partition
 .text.bootloader_reset
                0x4ff2e528       0x1c esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
                0x4ff2e528                bootloader_reset
 .text.bootloader_utility_load_boot_image
                0x4ff2e544      0x11c esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
                0x4ff2e544                bootloader_utility_load_boot_image
 .text.bootloader_debug_buffer
                0x4ff2e660        0x2 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
                0x4ff2e660                bootloader_debug_buffer
 *libbootloader_support.a:bootloader_sha.*(.literal .text .literal.* .text.*)
 .text.bootloader_sha256_start
                0x4ff2e662       0x2c esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
                0x4ff2e662                bootloader_sha256_start
 .text.bootloader_sha256_data
                0x4ff2e68e       0x34 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
                0x4ff2e68e                bootloader_sha256_data
 .text.bootloader_sha256_finish
                0x4ff2e6c2       0x46 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
                0x4ff2e6c2                bootloader_sha256_finish
 *libbootloader_support.a:bootloader_console_loader.*(.literal .text .literal.* .text.*)
 .text.bootloader_console_deinit
                0x4ff2e708        0xa esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
                0x4ff2e708                bootloader_console_deinit
 *libbootloader_support.a:bootloader_panic.*(.literal .text .literal.* .text.*)
 .text.__assert_func
                0x4ff2e712       0x20 esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
                0x4ff2e712                __assert_func
 .text.unlikely.abort
                0x4ff2e732       0x22 esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
                0x4ff2e732                abort
 *libbootloader_support.a:bootloader_soc.*(.literal .text .literal.* .text.*)
 .text.bootloader_ana_super_wdt_reset_config
                0x4ff2e754        0x2 esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
                0x4ff2e754                bootloader_ana_super_wdt_reset_config
 .text.bootloader_ana_clock_glitch_reset_config
                0x4ff2e756        0x2 esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
                0x4ff2e756                bootloader_ana_clock_glitch_reset_config
 *libbootloader_support.a:esp_image_format.*(.literal .text .literal.* .text.*)
 .text.bootloader_util_regions_overlap
                0x4ff2e758       0x4c esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .text.should_load
                0x4ff2e7a4       0x4e esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .text.process_segments
                0x4ff2e7f2      0x48a esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .text.image_load
                0x4ff2ec7c      0x3d8 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .text.bootloader_load_image
                0x4ff2f054        0x8 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                0x4ff2f054                bootloader_load_image
 *libbootloader_support.a:flash_encrypt.*(.literal .text .literal.* .text.*)
 *libbootloader_support.a:flash_encryption_secure_features.*(.literal .text .literal.* .text.*)
 *libbootloader_support.a:flash_partitions.*(.literal .text .literal.* .text.*)
 .text.esp_partition_table_verify
                0x4ff2f05c      0x18a esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
                0x4ff2f05c                esp_partition_table_verify
 *libbootloader_support.a:secure_boot.*(.literal .text .literal.* .text.*)
 *libbootloader_support.a:secure_boot_secure_features.*(.literal .text .literal.* .text.*)
 *libbootloader_support.a:secure_boot_signatures_bootloader.*(.literal .text .literal.* .text.*)
 *libmicro-ecc.a:*.*(.literal .text .literal.* .text.*)
 *libspi_flash.a:*.*(.literal .text .literal.* .text.*)
 *libhal.a:wdt_hal_iram.*(.literal .text .literal.* .text.*)
 *libhal.a:mmu_hal.*(.literal .text .literal.* .text.*)
 .text.mmu_ll_check_valid_paddr_region
                0x4ff2f1e6       0x2a esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .text.mmu_hal_unmap_all
                0x4ff2f210       0x46 esp-idf/hal/libhal.a(mmu_hal.c.obj)
                0x4ff2f210                mmu_hal_unmap_all
 .text.mmu_hal_init
                0x4ff2f256        0x2 esp-idf/hal/libhal.a(mmu_hal.c.obj)
                0x4ff2f256                mmu_hal_init
 .text.mmu_hal_check_valid_ext_vaddr_region
                0x4ff2f258       0x2c esp-idf/hal/libhal.a(mmu_hal.c.obj)
                0x4ff2f258                mmu_hal_check_valid_ext_vaddr_region
 .text.mmu_hal_map_region
                0x4ff2f284       0xda esp-idf/hal/libhal.a(mmu_hal.c.obj)
                0x4ff2f284                mmu_hal_map_region
 *libhal.a:cache_hal.*(.literal .text .literal.* .text.*)
 .text.s_cache_hal_init_ctx
                0x4ff2f35e       0x4e esp-idf/hal/libhal.a(cache_hal.c.obj)
                0x4ff2f35e                s_cache_hal_init_ctx
 .text.cache_hal_init
                0x4ff2f3ac       0x30 esp-idf/hal/libhal.a(cache_hal.c.obj)
                0x4ff2f3ac                cache_hal_init
 .text.s_update_cache_state
                0x4ff2f3dc       0x58 esp-idf/hal/libhal.a(cache_hal.c.obj)
                0x4ff2f3dc                s_update_cache_state
 .text.cache_hal_disable
                0x4ff2f434       0x68 esp-idf/hal/libhal.a(cache_hal.c.obj)
                0x4ff2f434                cache_hal_disable
 .text.cache_hal_enable
                0x4ff2f49c       0x7e esp-idf/hal/libhal.a(cache_hal.c.obj)
                0x4ff2f49c                cache_hal_enable
 *libhal.a:efuse_hal.*(.literal .text .literal.* .text.*)
 *libhal.a:key_mgr_hal.*(.literal.key_mgr_hal_set_key_usage .text.key_mgr_hal_set_key_usage)
 *libesp_hw_support.a:rtc_clk.*(.literal .text .literal.* .text.*)
 .text.rtc_clk_cpll_disable
                0x4ff2f51a       0x26 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_clk_cpu_freq_to_8m
                0x4ff2f540       0xae esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_clk_cpu_freq_to_xtal
                0x4ff2f5ee       0xcc esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_clk_cpu_freq_to_cpll_mhz
                0x4ff2f6ba      0x268 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_clk_32k_enable.part.0
                0x4ff2f922       0x54 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_clk_32k_enable
                0x4ff2f976       0x1a esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x4ff2f976                rtc_clk_32k_enable
 .text.rtc_clk_rc32k_enable
                0x4ff2f990       0x2c esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x4ff2f990                rtc_clk_rc32k_enable
 .text.rtc_clk_8m_enable
                0x4ff2f9bc       0x2c esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x4ff2f9bc                rtc_clk_8m_enable
 .text.rtc_clk_slow_src_set
                0x4ff2f9e8       0x44 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x4ff2f9e8                rtc_clk_slow_src_set
 .text.rtc_clk_slow_src_get
                0x4ff2fa2c       0x28 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x4ff2fa2c                rtc_clk_slow_src_get
 .text.rtc_clk_slow_freq_get_hz
                0x4ff2fa54       0x24 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x4ff2fa54                rtc_clk_slow_freq_get_hz
 .text.rtc_clk_fast_src_set
                0x4ff2fa78       0x42 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x4ff2fa78                rtc_clk_fast_src_set
 .text.rtc_clk_fast_src_get
                0x4ff2faba       0x2a esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x4ff2faba                rtc_clk_fast_src_get
 .text.rtc_clk_set_cpu_switch_to_pll
                0x4ff2fae4        0x2 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x4ff2fae4                rtc_clk_set_cpu_switch_to_pll
 .text.rtc_clk_xtal_freq_get
                0x4ff2fae6       0x54 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x4ff2fae6                rtc_clk_xtal_freq_get
 .text.rtc_clk_cpu_freq_mhz_to_config
                0x4ff2fb3a       0x7c esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x4ff2fb3a                rtc_clk_cpu_freq_mhz_to_config
 .text.rtc_clk_cpu_freq_set_config
                0x4ff2fbb6      0x158 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x4ff2fbb6                rtc_clk_cpu_freq_set_config
 .text.rtc_clk_hp_root_get_freq_mhz
                0x4ff2fd0e       0x9c esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_clk_cpu_freq_get_config
                0x4ff2fdaa       0x96 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x4ff2fdaa                rtc_clk_cpu_freq_get_config
 .text.rtc_clk_xtal_freq_update
                0x4ff2fe40       0x26 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x4ff2fe40                rtc_clk_xtal_freq_update
 .text.rtc_clk_apb_freq_get
                0x4ff2fe66       0xc0 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x4ff2fe66                rtc_clk_apb_freq_get
 *libesp_hw_support.a:rtc_time.*(.literal .text .literal.* .text.*)
 *libesp_hw_support.a:regi2c_ctrl.*(.literal .text .literal.* .text.*)
 *libefuse.a:*.*(.literal .text .literal.* .text.*)
 *libriscv.a:rv_utils.*(.literal .text .literal.* .text.*)
 .text.rv_utils_dbgr_is_attached
                0x4ff2ff26        0xc esp-idf/riscv/libriscv.a(rv_utils.c.obj)
                0x4ff2ff26                rv_utils_dbgr_is_attached
 *(.fini.literal)
 *(.fini)
 *(.gnu.version)
                0x4ff2ff32                        _loader_text_end = ABSOLUTE (.)

.iram.text      0x4ff29ed0        0x0
                0x4ff29ed0                        . = ALIGN (0x10)
 *(.entry.text)
 *(.init.literal)
 *(.init)

.dram0.bss      0x4ff33bd0      0x110
                0x4ff33bd0                        . = ALIGN (0x8)
                0x4ff33bd0                        _dram_start = ABSOLUTE (.)
                0x4ff33bd0                        _bss_start = ABSOLUTE (.)
 *(.dynsbss)
 *(.sbss)
 *(.sbss.*)
 .sbss.ota_has_initial_contents
                0x4ff33bd0        0x1 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 *fill*         0x4ff33bd1        0x3 
 .sbss.ram_obfs_value
                0x4ff33bd4        0x8 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .sbss.mapped   0x4ff33bdc        0x1 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 *fill*         0x4ff33bdd        0x3 
 .sbss.s_cur_cpll_freq
                0x4ff33be0        0x4 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .sbss.ctx      0x4ff33be4        0x8 esp-idf/hal/libhal.a(cache_hal.c.obj)
 *(.gnu.linkonce.sb.*)
 *(.scommon)
 *(.sbss2)
 *(.sbss2.*)
 *(.gnu.linkonce.sb2.*)
 *(.dynbss)
 *(.bss)
 *(.bss.*)
 .bss.ctx       0x4ff33bec       0xd8 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .bss.bootloader_image_hdr
                0x4ff33cc4       0x18 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                0x4ff33cc4                bootloader_image_hdr
 *(.gnu.linkonce.b.*)
 *(COMMON)
                0x4ff33ce0                        . = ALIGN (0x8)
 *fill*         0x4ff33cdc        0x4 
                0x4ff33ce0                        _bss_end = ABSOLUTE (.)

.dram0.bootdesc
                0x4ff33ce0       0x50
                0x4ff33ce0                        _data_start = ABSOLUTE (.)
 *(.data_bootloader_desc .data_bootloader_desc.*)
 .data_bootloader_desc
                0x4ff33ce0       0x50 esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
                0x4ff33ce0                esp_bootloader_desc

.dram0.data     0x4ff33d30        0xc
 *(.dram1 .dram1.*)
 .dram1.0       0x4ff33d30        0x4 esp-idf/hal/libhal.a(lp_timer_hal.c.obj)
 *(.data)
 *(.data.*)
 *(.gnu.linkonce.d.*)
 *(.data1)
 *(.sdata)
 *(.sdata.*)
 .sdata.s_bootloader_partition_offset
                0x4ff33d34        0x4 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .sdata.current_read_mapping
                0x4ff33d38        0x4 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 *(.gnu.linkonce.s.*)
 *(.gnu.linkonce.s2.*)
 *(.jcr)
                0x4ff33d3c                        _data_end = ABSOLUTE (.)

.dram0.rodata   0x4ff33d3c     0x15f0
                0x4ff33d3c                        _rodata_start = ABSOLUTE (.)
 *(.rodata)
 *(.rodata.*)
 .rodata.__assert_func.str1.4
                0x4ff33d3c     0x14c2 esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
                                 0x22 (size before relaxing)
 .rodata.abort.str1.4
                0x4ff351fe       0x22 esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
 .rodata.call_start_cpu0.str1.4
                0x4ff351fe       0x31 esp-idf/main/libmain.a(bootloader_start.c.obj)
 .rodata.log_invalid_app_partition.str1.4
                0x4ff351fe       0x99 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .rodata.load_image.str1.4
                0x4ff351fe       0x93 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .rodata.try_load_partition.str1.4
                0x4ff351fe       0x37 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .rodata.set_actual_ota_seq.str1.4
                0x4ff351fe       0x6e esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .rodata.bootloader_common_read_otadata.str1.4
                0x4ff351fe       0x7e esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .rodata.bootloader_utility_load_partition_table.str1.4
                0x4ff351fe      0x1fc esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .rodata.bootloader_utility_get_selected_boot_partition.str1.4
                0x4ff351fe       0xec esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .rodata.bootloader_utility_load_boot_image.str1.4
                0x4ff351fe       0xc3 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 *fill*         0x4ff351fe        0x2 
 .rodata.__func__.0
                0x4ff35200       0x10 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .rodata.esp_partition_table_verify.str1.4
                0x4ff35210      0x131 esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
 .rodata.bootloader_util_regions_overlap.str1.4
                0x4ff35210       0x5e esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .rodata.process_segments.str1.4
                0x4ff35210      0x276 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .rodata.image_load.str1.4
                0x4ff35210      0x156 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .rodata.__func__.0
                0x4ff35210       0x20 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .rodata.__func__.1
                0x4ff35230       0x16 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .rodata.bootloader_sha256_data.str1.4
                0x4ff35246       0x49 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 *fill*         0x4ff35246        0x2 
 .rodata.__func__.0
                0x4ff35248       0x19 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 *fill*         0x4ff35261        0x3 
 .rodata.__func__.1
                0x4ff35264       0x17 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .rodata.bootloader_init.str1.4
                0x4ff3527b       0xf4 esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32p4.c.obj)
 *fill*         0x4ff3527b        0x1 
 .rodata.__func__.0
                0x4ff3527c       0x10 esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32p4.c.obj)
 .rodata.bootloader_common_check_chip_revision_validity.str1.4
                0x4ff3528c       0xa3 esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .rodata.bootloader_common_check_efuse_blk_validity.str1.4
                0x4ff3528c       0x98 esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .rodata.bootloader_common_check_chip_validity.str1.4
                0x4ff3528c       0x35 esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .rodata.bootloader_fill_random.str1.4
                0x4ff3528c       0x4c esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .rodata.__func__.0
                0x4ff3528c       0x17 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .rodata.bootloader_mmap.str1.4
                0x4ff352a3       0x8d esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .rodata.bootloader_flash_read.str1.4
                0x4ff352a3       0xc4 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .rodata.str1.4
                0x4ff352a3       0xc8 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .rodata.bootloader_flash_write.str1.4
                0x4ff352a3       0xcc esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 *fill*         0x4ff352a3        0x1 
 .rodata.__func__.1
                0x4ff352a4       0x1b esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 *fill*         0x4ff352bf        0x1 
 .rodata.__func__.0
                0x4ff352c0       0x28 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .rodata.bootloader_init_spi_flash.str1.4
                0x4ff352e8       0xf9 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32p4.c.obj)
 .rodata.bootloader_read_bootloader_header.str1.4
                0x4ff352e8       0x3d esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .rodata.bootloader_check_bootloader_validity.str1.4
                0x4ff352e8       0x4e esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .rodata.bootloader_enable_random.str1.4
                0x4ff352e8       0x32 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .rodata.bootloader_print_banner.str1.4
                0x4ff352e8       0x72 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .rodata.rtc_clk_init.str1.4
                0x4ff352e8       0x39 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
 .rodata.rtc_clk_xtal_freq_get.str1.4
                0x4ff352e8       0x43 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .rodata.rtc_clk_hp_root_get_freq_mhz.str1.4
                0x4ff352e8       0x21 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .rodata.str1.4
                0x4ff352e8       0x76 esp-idf/esp_rom/libesp_rom.a(esp_rom_regi2c_esp32p4.c.obj)
 .rodata.__func__.2
                0x4ff352e8       0x17 esp-idf/esp_rom/libesp_rom.a(esp_rom_regi2c_esp32p4.c.obj)
 *fill*         0x4ff352ff        0x1 
 .rodata.__func__.1
                0x4ff35300       0x16 esp-idf/esp_rom/libesp_rom.a(esp_rom_regi2c_esp32p4.c.obj)
 *fill*         0x4ff35316        0x2 
 .rodata.__func__.0
                0x4ff35318       0x14 esp-idf/esp_rom/libesp_rom.a(esp_rom_regi2c_esp32p4.c.obj)
 *(.gnu.linkonce.r.*)
 *(.rodata1)
 *(.sdata2 .sdata2.* .srodata .srodata.*)
                0x4ff3532c                        __XT_EXCEPTION_TABLE_ = ABSOLUTE (.)
 *(.xt_except_table)
 *(.gcc_except_table)
 *(.gnu.linkonce.e.*)
 *(.gnu.version_r)
 *(.eh_frame_hdr)
 *(.eh_frame)
                0x4ff3532c                        . = ((. + 0x3) & 0xfffffffffffffffc)
                0x4ff3532c                        __init_array_start = ABSOLUTE (.)
 *crtbegin.*(.ctors)
 *(EXCLUDE_FILE(*crtend.*) .ctors)
 *(SORT_BY_NAME(.ctors.*))
 *(.ctors)
                0x4ff3532c                        __init_array_end = ABSOLUTE (.)
 *crtbegin.*(.dtors)
 *(EXCLUDE_FILE(*crtend.*) .dtors)
 *(SORT_BY_NAME(.dtors.*))
 *(.dtors)
                0x4ff3532c                        __XT_EXCEPTION_DESCS_ = ABSOLUTE (.)
 *(.xt_except_desc)
 *(.gnu.linkonce.h.*)
                0x4ff3532c                        __XT_EXCEPTION_DESCS_END__ = ABSOLUTE (.)
 *(.xt_except_desc_end)
 *(.dynamic)
 *(.gnu.version_d)
                0x4ff3532c                        _rodata_end = ABSOLUTE (.)
                0x4ff3532c                        _lit4_start = ABSOLUTE (.)
 *(*.lit4)
 *(.lit4.*)
 *(.gnu.linkonce.lit4.*)
                0x4ff3532c                        _lit4_end = ABSOLUTE (.)
                0x4ff3532c                        . = ALIGN (0x4)
                0x4ff3532c                        _dram_end = ABSOLUTE (.)

.iram.text      0x4ff29ed0      0xd62
                0x4ff29ed0                        _stext = .
                0x4ff29ed0                        _text_start = ABSOLUTE (.)
 *(.literal .text .literal.* .text.* .stub .gnu.warning .gnu.linkonce.literal.* .gnu.linkonce.t.*.literal .gnu.linkonce.t.*)
 .text.esp_bootloader_get_description
                0x4ff29ed0        0xa esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
                0x4ff29ed0                esp_bootloader_get_description
 .text.call_start_cpu0
                0x4ff29eda       0x82 esp-idf/main/libmain.a(bootloader_start.c.obj)
                0x4ff29eda                call_start_cpu0
 .text.bootloader_init
                0x4ff29f5c      0x1e2 esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32p4.c.obj)
                0x4ff29f5c                bootloader_init
 .text.bootloader_clock_configure
                0x4ff2a13e      0x12a esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
                0x4ff2a13e                bootloader_clock_configure
 .text.bootloader_init_mem
                0x4ff2a268        0x2 esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
                0x4ff2a268                bootloader_init_mem
 .text.bootloader_init_spi_flash
                0x4ff2a26a      0x23c esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32p4.c.obj)
                0x4ff2a26a                bootloader_init_spi_flash
 .text.bootloader_clear_bss_section
                0x4ff2a4a6       0x22 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                0x4ff2a4a6                bootloader_clear_bss_section
 .text.bootloader_read_bootloader_header
                0x4ff2a4c8       0x3e esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                0x4ff2a4c8                bootloader_read_bootloader_header
 .text.bootloader_check_bootloader_validity
                0x4ff2a506       0x84 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                0x4ff2a506                bootloader_check_bootloader_validity
 .text.bootloader_config_wdt
                0x4ff2a58a       0xb0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                0x4ff2a58a                bootloader_config_wdt
 .text.bootloader_enable_random
                0x4ff2a63a       0x2a esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                0x4ff2a63a                bootloader_enable_random
 .text.bootloader_print_banner
                0x4ff2a664       0x70 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                0x4ff2a664                bootloader_print_banner
 .text.bootloader_console_init
                0x4ff2a6d4       0xe4 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
                0x4ff2a6d4                bootloader_console_init
 .text.esp_cpu_configure_region_protection
                0x4ff2a7b8      0x266 esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
                0x4ff2a7b8                esp_cpu_configure_region_protection
 .text.rtc_clk_init
                0x4ff2aa1e      0x204 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
                0x4ff2aa1e                rtc_clk_init
 *(.iram .iram.*)
 *(.fini.literal)
 *(.fini)
 *(.gnu.version)
                0x4ff2ac32                        . = (. + 0x10)
 *fill*         0x4ff2ac22       0x10 
                0x4ff2ac32                        _text_end = ABSOLUTE (.)
                0x4ff2ac32                        _etext = .

.riscv.attributes
                0x00000000       0x70
 *(.riscv.attributes)
 .riscv.attributes
                0x00000000       0x6c esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
 .riscv.attributes
                0x0000006c       0x6c esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
 .riscv.attributes
                0x000000d8       0x6c esp-idf/main/libmain.a(bootloader_start.c.obj)
 .riscv.attributes
                0x00000144       0x6c esp-idf/riscv/libriscv.a(rv_utils.c.obj)
 .riscv.attributes
                0x000001b0       0x6c esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .riscv.attributes
                0x0000021c       0x6c esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
 .riscv.attributes
                0x00000288       0x6c esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .riscv.attributes
                0x000002f4       0x6c esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .riscv.attributes
                0x00000360       0x6c esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
 .riscv.attributes
                0x000003cc       0x6c esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
 .riscv.attributes
                0x00000438       0x6c esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32p4.c.obj)
 .riscv.attributes
                0x000004a4       0x6c esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .riscv.attributes
                0x00000510       0x6c esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
 .riscv.attributes
                0x0000057c       0x6c esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
 .riscv.attributes
                0x000005e8       0x70 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .riscv.attributes
                0x00000658       0x6c esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .riscv.attributes
                0x000006c4       0x6c esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32p4.c.obj)
 .riscv.attributes
                0x00000730       0x6c esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .riscv.attributes
                0x0000079c       0x6c esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32p4.c.obj)
 .riscv.attributes
                0x00000808       0x6c esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .riscv.attributes
                0x00000874       0x6c esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
 .riscv.attributes
                0x000008e0       0x70 esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
 .riscv.attributes
                0x00000950       0x70 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
 .riscv.attributes
                0x000009c0       0x6c esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .riscv.attributes
                0x00000a2c       0x6c esp-idf/esp_rom/libesp_rom.a(esp_rom_regi2c_esp32p4.c.obj)
 .riscv.attributes
                0x00000a98       0x70 esp-idf/log/liblog.a(log_timestamp.c.obj)
 .riscv.attributes
                0x00000b08       0x6c esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .riscv.attributes
                0x00000b74       0x6c esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .riscv.attributes
                0x00000be0       0x6c esp-idf/hal/libhal.a(lp_timer_hal.c.obj)
 .riscv.attributes
                0x00000c4c       0x6c esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .riscv.attributes
                0x00000cb8       0x6c esp-idf/hal/libhal.a(cache_hal.c.obj)

.debug
 *(.debug)

.line
 *(.line)

.debug_srcinfo
 *(.debug_srcinfo)

.debug_sfnames
 *(.debug_sfnames)

.debug_aranges  0x00000000      0x8d0
 *(.debug_aranges)
 .debug_aranges
                0x00000000       0x20 esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
 .debug_aranges
                0x00000020       0x28 esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
 .debug_aranges
                0x00000048       0x28 esp-idf/main/libmain.a(bootloader_start.c.obj)
 .debug_aranges
                0x00000070       0x20 esp-idf/riscv/libriscv.a(rv_utils.c.obj)
 .debug_aranges
                0x00000090       0x90 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .debug_aranges
                0x00000120       0x20 esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
 .debug_aranges
                0x00000140       0x80 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .debug_aranges
                0x000001c0       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .debug_aranges
                0x000001f0       0x20 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
 .debug_aranges
                0x00000210       0x28 esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
 .debug_aranges
                0x00000238       0x20 esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32p4.c.obj)
 .debug_aranges
                0x00000258       0x58 esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .debug_aranges
                0x000002b0       0x20 esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
 .debug_aranges
                0x000002d0       0x20 esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
 .debug_aranges
                0x000002f0       0x20 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .debug_aranges
                0x00000310       0x40 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .debug_aranges
                0x00000350       0x28 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32p4.c.obj)
 .debug_aranges
                0x00000378       0xb8 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .debug_aranges
                0x00000430       0x50 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32p4.c.obj)
 .debug_aranges
                0x00000480       0x48 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .debug_aranges
                0x000004c8       0x20 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
 .debug_aranges
                0x000004e8       0x20 esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
 .debug_aranges
                0x00000508       0x20 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
 .debug_aranges
                0x00000528      0x158 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .debug_aranges
                0x00000680       0x40 esp-idf/esp_rom/libesp_rom.a(esp_rom_regi2c_esp32p4.c.obj)
 .debug_aranges
                0x000006c0       0x20 esp-idf/log/liblog.a(log_timestamp.c.obj)
 .debug_aranges
                0x000006e0       0x58 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_aranges
                0x00000738       0x58 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_aranges
                0x00000790       0x38 esp-idf/hal/libhal.a(lp_timer_hal.c.obj)
 .debug_aranges
                0x000007c8       0x78 esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .debug_aranges
                0x00000840       0x90 esp-idf/hal/libhal.a(cache_hal.c.obj)

.debug_pubnames
 *(.debug_pubnames)

.debug_info     0x00000000    0x46828
 *(.debug_info .gnu.linkonce.wi.*)
 .debug_info    0x00000000      0x18e esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
 .debug_info    0x0000018e      0x1f6 esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
 .debug_info    0x00000384      0xd59 esp-idf/main/libmain.a(bootloader_start.c.obj)
 .debug_info    0x000010dd       0xd1 esp-idf/riscv/libriscv.a(rv_utils.c.obj)
 .debug_info    0x000011ae     0x239a esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .debug_info    0x00003548      0x678 esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
 .debug_info    0x00003bc0     0x2c22 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .debug_info    0x000067e2      0x45a esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .debug_info    0x00006c3c       0xc6 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
 .debug_info    0x00006d02       0xcb esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
 .debug_info    0x00006dcd     0x68a9 esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32p4.c.obj)
 .debug_info    0x0000d676      0xea6 esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .debug_info    0x0000e51c      0x35f esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
 .debug_info    0x0000e87b       0x9f esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
 .debug_info    0x0000e91a      0x2b2 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .debug_info    0x0000ebcc      0xad5 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .debug_info    0x0000f6a1      0x3bb esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32p4.c.obj)
 .debug_info    0x0000fa5c     0x6342 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .debug_info    0x00015d9e     0x4ad7 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32p4.c.obj)
 .debug_info    0x0001a875     0x23b0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .debug_info    0x0001cc25     0x5714 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
 .debug_info    0x00022339      0x3a9 esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
 .debug_info    0x000226e2     0x3de2 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
 .debug_info    0x000264c4     0x7e87 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .debug_info    0x0002e34b      0xc35 esp-idf/esp_rom/libesp_rom.a(esp_rom_regi2c_esp32p4.c.obj)
 .debug_info    0x0002ef80      0x12c esp-idf/log/liblog.a(log_timestamp.c.obj)
 .debug_info    0x0002f0ac     0x6ac4 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_info    0x00035b70     0x6af3 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_info    0x0003c663      0xc03 esp-idf/hal/libhal.a(lp_timer_hal.c.obj)
 .debug_info    0x0003d266     0x777c esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .debug_info    0x000449e2     0x1e46 esp-idf/hal/libhal.a(cache_hal.c.obj)

.debug_abbrev   0x00000000     0x56b1
 *(.debug_abbrev)
 .debug_abbrev  0x00000000       0xaa esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
 .debug_abbrev  0x000000aa      0x11b esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
 .debug_abbrev  0x000001c5      0x33b esp-idf/main/libmain.a(bootloader_start.c.obj)
 .debug_abbrev  0x00000500       0x7b esp-idf/riscv/libriscv.a(rv_utils.c.obj)
 .debug_abbrev  0x0000057b      0x51d esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .debug_abbrev  0x00000a98      0x23e esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
 .debug_abbrev  0x00000cd6      0x5b1 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .debug_abbrev  0x00001287      0x1ca esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .debug_abbrev  0x00001451       0x89 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
 .debug_abbrev  0x000014da       0x7a esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
 .debug_abbrev  0x00001554      0x3d8 esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32p4.c.obj)
 .debug_abbrev  0x0000192c      0x2ce esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .debug_abbrev  0x00001bfa      0x12e esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
 .debug_abbrev  0x00001d28       0x65 esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
 .debug_abbrev  0x00001d8d      0x194 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .debug_abbrev  0x00001f21      0x33d esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .debug_abbrev  0x0000225e      0x197 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32p4.c.obj)
 .debug_abbrev  0x000023f5      0x59c esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .debug_abbrev  0x00002991      0x45c esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32p4.c.obj)
 .debug_abbrev  0x00002ded      0x36d esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .debug_abbrev  0x0000315a      0x32f esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
 .debug_abbrev  0x00003489      0x169 esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
 .debug_abbrev  0x000035f2      0x486 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
 .debug_abbrev  0x00003a78      0x69a esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .debug_abbrev  0x00004112      0x29d esp-idf/esp_rom/libesp_rom.a(esp_rom_regi2c_esp32p4.c.obj)
 .debug_abbrev  0x000043af       0xe5 esp-idf/log/liblog.a(log_timestamp.c.obj)
 .debug_abbrev  0x00004494      0x2d3 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_abbrev  0x00004767      0x368 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_abbrev  0x00004acf      0x2cf esp-idf/hal/libhal.a(lp_timer_hal.c.obj)
 .debug_abbrev  0x00004d9e      0x4a7 esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .debug_abbrev  0x00005245      0x46c esp-idf/hal/libhal.a(cache_hal.c.obj)

.debug_line     0x00000000    0x1642f
 *(.debug_line)
 .debug_line    0x00000000      0x1fa esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
 .debug_line    0x000001fa      0x3cd esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
 .debug_line    0x000005c7      0x65e esp-idf/main/libmain.a(bootloader_start.c.obj)
 .debug_line    0x00000c25      0x1dd esp-idf/riscv/libriscv.a(rv_utils.c.obj)
 .debug_line    0x00000e02     0x2672 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .debug_line    0x00003474      0x7bf esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
 .debug_line    0x00003c33     0x2752 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .debug_line    0x00006385      0x42d esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .debug_line    0x000067b2      0x1d8 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
 .debug_line    0x0000698a       0xa1 esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
 .debug_line    0x00006a2b      0xb21 esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32p4.c.obj)
 .debug_line    0x0000754c      0xb53 esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .debug_line    0x0000809f      0x4e1 esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
 .debug_line    0x00008580       0xd3 esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
 .debug_line    0x00008653      0x4b0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .debug_line    0x00008b03      0xfb6 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .debug_line    0x00009ab9      0x56a esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32p4.c.obj)
 .debug_line    0x0000a023     0x2079 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .debug_line    0x0000c09c      0xe10 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32p4.c.obj)
 .debug_line    0x0000ceac      0xc81 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .debug_line    0x0000db2d      0x554 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
 .debug_line    0x0000e081      0x9f6 esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
 .debug_line    0x0000ea77      0x8e2 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
 .debug_line    0x0000f359     0x2f88 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .debug_line    0x000122e1      0x831 esp-idf/esp_rom/libesp_rom.a(esp_rom_regi2c_esp32p4.c.obj)
 .debug_line    0x00012b12      0x2b4 esp-idf/log/liblog.a(log_timestamp.c.obj)
 .debug_line    0x00012dc6      0x503 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_line    0x000132c9      0x643 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_line    0x0001390c      0x424 esp-idf/hal/libhal.a(lp_timer_hal.c.obj)
 .debug_line    0x00013d30     0x121f esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .debug_line    0x00014f4f     0x14e0 esp-idf/hal/libhal.a(cache_hal.c.obj)

.debug_frame    0x00000000     0x1854
 *(.debug_frame)
 .debug_frame   0x00000000       0x20 esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
 .debug_frame   0x00000020       0x40 esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
 .debug_frame   0x00000060       0x38 esp-idf/main/libmain.a(bootloader_start.c.obj)
 .debug_frame   0x00000098       0x20 esp-idf/riscv/libriscv.a(rv_utils.c.obj)
 .debug_frame   0x000000b8      0x264 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .debug_frame   0x0000031c       0x58 esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
 .debug_frame   0x00000374      0x1c8 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .debug_frame   0x0000053c       0x68 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .debug_frame   0x000005a4       0x20 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
 .debug_frame   0x000005c4       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
 .debug_frame   0x000005f4       0x34 esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32p4.c.obj)
 .debug_frame   0x00000628      0x120 esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .debug_frame   0x00000748       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
 .debug_frame   0x00000778       0x20 esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
 .debug_frame   0x00000798       0x44 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .debug_frame   0x000007dc       0xb8 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .debug_frame   0x00000894       0x54 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32p4.c.obj)
 .debug_frame   0x000008e8      0x2e8 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .debug_frame   0x00000bd0       0xdc esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32p4.c.obj)
 .debug_frame   0x00000cac       0xc4 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .debug_frame   0x00000d70       0x34 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
 .debug_frame   0x00000da4       0x34 esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
 .debug_frame   0x00000dd8       0x3c esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
 .debug_frame   0x00000e14      0x43c esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .debug_frame   0x00001250       0xdc esp-idf/esp_rom/libesp_rom.a(esp_rom_regi2c_esp32p4.c.obj)
 .debug_frame   0x0000132c       0x30 esp-idf/log/liblog.a(log_timestamp.c.obj)
 .debug_frame   0x0000135c       0xa0 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_frame   0x000013fc       0x9c esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_frame   0x00001498       0x60 esp-idf/hal/libhal.a(lp_timer_hal.c.obj)
 .debug_frame   0x000014f8      0x168 esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .debug_frame   0x00001660      0x1f4 esp-idf/hal/libhal.a(cache_hal.c.obj)

.debug_str      0x00000000    0x1790d
 *(.debug_str)
 .debug_str     0x00000000    0x1790d esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
                                0x313 (size before relaxing)
 .debug_str     0x0001790d      0x306 esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
 .debug_str     0x0001790d      0xaf8 esp-idf/main/libmain.a(bootloader_start.c.obj)
 .debug_str     0x0001790d      0x299 esp-idf/riscv/libriscv.a(rv_utils.c.obj)
 .debug_str     0x0001790d     0x23d3 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .debug_str     0x0001790d      0x584 esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
 .debug_str     0x0001790d     0x2452 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .debug_str     0x0001790d      0x441 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .debug_str     0x0001790d      0x2a8 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
 .debug_str     0x0001790d      0x2bd esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
 .debug_str     0x0001790d     0x738b esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32p4.c.obj)
 .debug_str     0x0001790d     0x1bd7 esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .debug_str     0x0001790d      0x710 esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
 .debug_str     0x0001790d      0x291 esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
 .debug_str     0x0001790d      0x377 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .debug_str     0x0001790d      0xb47 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .debug_str     0x0001790d      0x385 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32p4.c.obj)
 .debug_str     0x0001790d     0x456b esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .debug_str     0x0001790d     0x5ec0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32p4.c.obj)
 .debug_str     0x0001790d     0x272f esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .debug_str     0x0001790d     0x5237 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
 .debug_str     0x0001790d      0x38b esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
 .debug_str     0x0001790d     0x36a1 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
 .debug_str     0x0001790d     0x77fa esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .debug_str     0x0001790d      0x8e4 esp-idf/esp_rom/libesp_rom.a(esp_rom_regi2c_esp32p4.c.obj)
 .debug_str     0x0001790d      0x2e4 esp-idf/log/liblog.a(log_timestamp.c.obj)
 .debug_str     0x0001790d     0x5085 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_str     0x0001790d     0x50b9 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_str     0x0001790d      0x81e esp-idf/hal/libhal.a(lp_timer_hal.c.obj)
 .debug_str     0x0001790d     0x5255 esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .debug_str     0x0001790d      0xe47 esp-idf/hal/libhal.a(cache_hal.c.obj)

.debug_loc      0x00000000     0xaca5
 *(.debug_loc)
 .debug_loc     0x00000000       0xc8 esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
 .debug_loc     0x000000c8       0xc7 esp-idf/main/libmain.a(bootloader_start.c.obj)
 .debug_loc     0x0000018f      0xff1 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .debug_loc     0x00001180      0x1d9 esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
 .debug_loc     0x00001359     0x2442 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .debug_loc     0x0000379b      0x17d esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .debug_loc     0x00003918       0xb8 esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32p4.c.obj)
 .debug_loc     0x000039d0      0x49c esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .debug_loc     0x00003e6c      0x19c esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .debug_loc     0x00004008      0x1b2 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .debug_loc     0x000041ba       0x89 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32p4.c.obj)
 .debug_loc     0x00004243     0x182f esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .debug_loc     0x00005a72      0x1c6 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32p4.c.obj)
 .debug_loc     0x00005c38      0x134 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .debug_loc     0x00005d6c       0x65 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
 .debug_loc     0x00005dd1       0x48 esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
 .debug_loc     0x00005e19      0x143 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
 .debug_loc     0x00005f5c     0x196e esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .debug_loc     0x000078ca      0x5a6 esp-idf/esp_rom/libesp_rom.a(esp_rom_regi2c_esp32p4.c.obj)
 .debug_loc     0x00007e70       0x9b esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_loc     0x00007f0b      0x15f esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_loc     0x0000806a      0x175 esp-idf/hal/libhal.a(lp_timer_hal.c.obj)
 .debug_loc     0x000081df     0x12aa esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .debug_loc     0x00009489     0x181c esp-idf/hal/libhal.a(cache_hal.c.obj)

.debug_macinfo
 *(.debug_macinfo)

.debug_pubtypes
 *(.debug_pubtypes)

.debug_ranges   0x00000000     0x1da0
 *(.debug_ranges)
 .debug_ranges  0x00000000       0x10 esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
 .debug_ranges  0x00000010       0x18 esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
 .debug_ranges  0x00000028       0x30 esp-idf/main/libmain.a(bootloader_start.c.obj)
 .debug_ranges  0x00000058       0x10 esp-idf/riscv/libriscv.a(rv_utils.c.obj)
 .debug_ranges  0x00000068      0x210 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .debug_ranges  0x00000278       0x70 esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
 .debug_ranges  0x000002e8      0x3f0 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .debug_ranges  0x000006d8       0x20 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .debug_ranges  0x000006f8       0x10 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
 .debug_ranges  0x00000708       0x18 esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
 .debug_ranges  0x00000720       0x98 esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32p4.c.obj)
 .debug_ranges  0x000007b8       0x48 esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .debug_ranges  0x00000800       0x10 esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
 .debug_ranges  0x00000810       0x10 esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
 .debug_ranges  0x00000820       0x40 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .debug_ranges  0x00000860       0xb0 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .debug_ranges  0x00000910       0x40 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32p4.c.obj)
 .debug_ranges  0x00000950      0x250 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .debug_ranges  0x00000ba0      0x100 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32p4.c.obj)
 .debug_ranges  0x00000ca0       0x50 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .debug_ranges  0x00000cf0       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
 .debug_ranges  0x00000d20       0x28 esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
 .debug_ranges  0x00000d48       0x48 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
 .debug_ranges  0x00000d90      0x830 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .debug_ranges  0x000015c0       0x30 esp-idf/esp_rom/libesp_rom.a(esp_rom_regi2c_esp32p4.c.obj)
 .debug_ranges  0x000015f0       0x28 esp-idf/log/liblog.a(log_timestamp.c.obj)
 .debug_ranges  0x00001618       0x98 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_ranges  0x000016b0       0x88 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_ranges  0x00001738       0x88 esp-idf/hal/libhal.a(lp_timer_hal.c.obj)
 .debug_ranges  0x000017c0      0x238 esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .debug_ranges  0x000019f8      0x3a8 esp-idf/hal/libhal.a(cache_hal.c.obj)

.debug_weaknames
 *(.debug_weaknames)

.debug_funcnames
 *(.debug_funcnames)

.debug_typenames
 *(.debug_typenames)

.debug_varnames
 *(.debug_varnames)

.debug_gnu_pubnames
 *(.debug_gnu_pubnames)

.debug_gnu_pubtypes
 *(.debug_gnu_pubtypes)

.debug_types
 *(.debug_types)

.debug_addr
 *(.debug_addr)

.debug_line_str
 *(.debug_line_str)

.debug_loclists
 *(.debug_loclists)

.debug_macro
 *(.debug_macro)

.debug_names
 *(.debug_names)

.debug_rnglists
 *(.debug_rnglists)

.debug_str_offsets
 *(.debug_str_offsets)

.comment        0x00000000       0x2f
 *(.comment)
 .comment       0x00000000       0x2f esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
                                 0x30 (size before relaxing)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
 .comment       0x0000002f       0x30 esp-idf/main/libmain.a(bootloader_start.c.obj)
 .comment       0x0000002f       0x30 esp-idf/riscv/libriscv.a(rv_utils.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32p4.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32p4.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32p4.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
 .comment       0x0000002f       0x30 esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
 .comment       0x0000002f       0x30 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
 .comment       0x0000002f       0x30 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .comment       0x0000002f       0x30 esp-idf/esp_rom/libesp_rom.a(esp_rom_regi2c_esp32p4.c.obj)
 .comment       0x0000002f       0x30 esp-idf/log/liblog.a(log_timestamp.c.obj)
 .comment       0x0000002f       0x30 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .comment       0x0000002f       0x30 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .comment       0x0000002f       0x30 esp-idf/hal/libhal.a(lp_timer_hal.c.obj)
 .comment       0x0000002f       0x30 esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .comment       0x0000002f       0x30 esp-idf/hal/libhal.a(cache_hal.c.obj)

.note.GNU-stack
                0x00000000        0x0
 *(.note.GNU-stack)
 .note.GNU-stack
                0x00000000        0x0 esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
 .note.GNU-stack
                0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
 .note.GNU-stack
                0x00000000        0x0 esp-idf/main/libmain.a(bootloader_start.c.obj)
 .note.GNU-stack
                0x00000000        0x0 esp-idf/riscv/libriscv.a(rv_utils.c.obj)
 .note.GNU-stack
                0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .note.GNU-stack
                0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
 .note.GNU-stack
                0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .note.GNU-stack
                0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .note.GNU-stack
                0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
 .note.GNU-stack
                0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
 .note.GNU-stack
                0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32p4.c.obj)
 .note.GNU-stack
                0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .note.GNU-stack
                0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
 .note.GNU-stack
                0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
 .note.GNU-stack
                0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .note.GNU-stack
                0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .note.GNU-stack
                0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32p4.c.obj)
 .note.GNU-stack
                0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .note.GNU-stack
                0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32p4.c.obj)
 .note.GNU-stack
                0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .note.GNU-stack
                0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
 .note.GNU-stack
                0x00000000        0x0 esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
 .note.GNU-stack
                0x00000000        0x0 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
 .note.GNU-stack
                0x00000000        0x0 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .note.GNU-stack
                0x00000000        0x0 esp-idf/esp_rom/libesp_rom.a(esp_rom_regi2c_esp32p4.c.obj)
 .note.GNU-stack
                0x00000000        0x0 esp-idf/log/liblog.a(log_timestamp.c.obj)
 .note.GNU-stack
                0x00000000        0x0 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .note.GNU-stack
                0x00000000        0x0 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .note.GNU-stack
                0x00000000        0x0 esp-idf/hal/libhal.a(lp_timer_hal.c.obj)
 .note.GNU-stack
                0x00000000        0x0 esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .note.GNU-stack
                0x00000000        0x0 esp-idf/hal/libhal.a(cache_hal.c.obj)

/DISCARD/
 *(.rela.*)

.noload         0x00000000        0x0
                0x00000000                        _noload_keep_in_elf_start = ABSOLUTE (.)
 *(.noload_keep_in_elf .noload_keep_in_elf.*)
                0x00000000                        _noload_keep_in_elf_end = ABSOLUTE (.)
OUTPUT(bootloader.elf elf32-littleriscv)

Cross Reference Table

Symbol                                            File
Cache_Disable_L1_CORE0_ICache                     esp-idf/hal/libhal.a(cache_hal.c.obj)
Cache_Disable_L1_CORE1_ICache                     esp-idf/hal/libhal.a(cache_hal.c.obj)
Cache_Disable_L1_DCache                           esp-idf/hal/libhal.a(cache_hal.c.obj)
Cache_Disable_L2_Cache                            esp-idf/hal/libhal.a(cache_hal.c.obj)
Cache_Enable_L1_CORE0_ICache                      esp-idf/hal/libhal.a(cache_hal.c.obj)
Cache_Enable_L1_CORE1_ICache                      esp-idf/hal/libhal.a(cache_hal.c.obj)
Cache_Enable_L1_DCache                            esp-idf/hal/libhal.a(cache_hal.c.obj)
Cache_Enable_L2_Cache                             esp-idf/hal/libhal.a(cache_hal.c.obj)
Cache_Get_L2_Cache_Line_Size                      esp-idf/hal/libhal.a(cache_hal.c.obj)
Cache_Invalidate_Addr                             esp-idf/hal/libhal.a(cache_hal.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
Cache_Resume_L1_CORE0_ICache                      esp-idf/hal/libhal.a(cache_hal.c.obj)
Cache_Resume_L1_CORE1_ICache                      esp-idf/hal/libhal.a(cache_hal.c.obj)
Cache_Resume_L1_DCache                            esp-idf/hal/libhal.a(cache_hal.c.obj)
Cache_Resume_L2_Cache                             esp-idf/hal/libhal.a(cache_hal.c.obj)
Cache_Suspend_L1_CORE0_ICache                     esp-idf/hal/libhal.a(cache_hal.c.obj)
Cache_Suspend_L1_CORE1_ICache                     esp-idf/hal/libhal.a(cache_hal.c.obj)
Cache_Suspend_L1_DCache                           esp-idf/hal/libhal.a(cache_hal.c.obj)
Cache_Suspend_L2_Cache                            esp-idf/hal/libhal.a(cache_hal.c.obj)
Cache_WriteBack_Addr                              esp-idf/hal/libhal.a(cache_hal.c.obj)
Cache_WriteBack_All                               esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
EFUSE                                             esp-idf/hal/libhal.a(mmu_hal.c.obj)
                                                  esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/hal/libhal.a(efuse_hal.c.obj)
ESP_EFUSE_ACTIVE_HP_DBIAS                         esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ACTIVE_LP_DBIAS                         esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ADC1_AVE_INITCODE_ATTEN0                esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ADC1_AVE_INITCODE_ATTEN1                esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ADC1_AVE_INITCODE_ATTEN2                esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ADC1_AVE_INITCODE_ATTEN3                esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ADC1_CH0_ATTEN0_INITCODE_DIFF           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ADC1_CH1_ATTEN0_INITCODE_DIFF           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ADC1_CH2_ATTEN0_INITCODE_DIFF           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ADC1_CH3_ATTEN0_INITCODE_DIFF           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ADC1_CH4_ATTEN0_INITCODE_DIFF           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ADC1_CH5_ATTEN0_INITCODE_DIFF           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ADC1_CH6_ATTEN0_INITCODE_DIFF           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ADC1_CH7_ATTEN0_INITCODE_DIFF           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ADC1_HI_DOUT_ATTEN0                     esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ADC1_HI_DOUT_ATTEN1                     esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ADC1_HI_DOUT_ATTEN2                     esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ADC1_HI_DOUT_ATTEN3                     esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ADC2_AVE_INITCODE_ATTEN0                esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ADC2_AVE_INITCODE_ATTEN1                esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ADC2_AVE_INITCODE_ATTEN2                esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ADC2_AVE_INITCODE_ATTEN3                esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ADC2_CH0_ATTEN0_INITCODE_DIFF           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ADC2_CH1_ATTEN0_INITCODE_DIFF           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ADC2_CH2_ATTEN0_INITCODE_DIFF           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ADC2_CH3_ATTEN0_INITCODE_DIFF           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ADC2_CH4_ATTEN0_INITCODE_DIFF           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ADC2_CH5_ATTEN0_INITCODE_DIFF           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ADC2_HI_DOUT_ATTEN0                     esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ADC2_HI_DOUT_ATTEN1                     esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ADC2_HI_DOUT_ATTEN2                     esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ADC2_HI_DOUT_ATTEN3                     esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_BLK_VERSION_MAJOR                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_BLK_VERSION_MINOR                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_CRYPT_DPA_ENABLE                        esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_DCDC_VSET                               esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_DCDC_VSET_EN                            esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_DISABLE_BLK_VERSION_MAJOR               esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_DISABLE_WAFER_VERSION_MAJOR             esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_DIS_DIRECT_BOOT                         esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
ESP_EFUSE_DIS_DOWNLOAD_MANUAL_ENCRYPT             esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
ESP_EFUSE_DIS_DOWNLOAD_MODE                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
ESP_EFUSE_DIS_FORCE_DOWNLOAD                      esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_DIS_PAD_JTAG                            esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
ESP_EFUSE_DIS_SWD                                 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_DIS_TWAI                                esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_DIS_USB_JTAG                            esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
ESP_EFUSE_DIS_USB_OTG_DOWNLOAD_MODE               esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_DIS_USB_SERIAL_JTAG_DOWNLOAD_MODE       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_DIS_USB_SERIAL_JTAG_ROM_PRINT           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_DIS_WDT                                 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_DSLP_DBG                                esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_DSLP_LP_DBIAS                           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ECDSA_ENABLE_SOFT_K                     esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ENABLE_SECURITY_DOWNLOAD                esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
ESP_EFUSE_FLASH_ECC_EN                            esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_FLASH_PAGE_SIZE                         esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_FLASH_TPUW                              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_FLASH_TYPE                              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_FORCE_DISABLE_SW_INIT_KEY               esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_FORCE_SEND_RESUME                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_FORCE_USE_KEY_MANAGER_KEY               esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_HP_PWR_SRC_SEL                          esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_HYS_EN_PAD                              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_JTAG_SEL_ENABLE                         esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_KEY0                                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_KEY1                                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_KEY2                                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_KEY3                                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_KEY4                                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_KEY5                                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_KEY_PURPOSE_0                           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_KEY_PURPOSE_1                           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_KEY_PURPOSE_2                           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_KEY_PURPOSE_3                           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_KEY_PURPOSE_4                           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_KEY_PURPOSE_5                           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_KM_DEPLOY_ONLY_ONCE                     esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_KM_DISABLE_DEPLOY_MODE                  esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_KM_HUK_GEN_STATE                        esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_KM_RND_SWITCH_CYCLE                     esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_LDO_VO1_DREF                            esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_LDO_VO1_MUL                             esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_LDO_VO2_DREF                            esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_LDO_VO2_MUL                             esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_LDO_VO3_C                               esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_LDO_VO3_K                               esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_LDO_VO3_VOS                             esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_LDO_VO4_C                               esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_LDO_VO4_K                               esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_LDO_VO4_VOS                             esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_LOCK_KM_KEY                             esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_LP_DCDC_DBIAS_VOL_GAP                   esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_LSLP_HP_DBIAS                           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_MAC                                     esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_OPTIONAL_UNIQUE_ID                      esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_PKG_VERSION                             esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
ESP_EFUSE_POWERGLITCH_EN                          esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_PSRAM_CAP                               esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_PSRAM_VENDOR                            esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_PXA0_TIEH_SEL_0                         esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_PXA0_TIEH_SEL_1                         esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_PXA0_TIEH_SEL_2                         esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_PXA0_TIEH_SEL_3                         esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_RD_DIS                                  esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_RD_DIS_ADC1_CH0_ATTEN0_INITCODE_DIFF    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_RD_DIS_ADC1_CH1_ATTEN0_INITCODE_DIFF    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_RD_DIS_ADC1_CH2_ATTEN0_INITCODE_DIFF    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_RD_DIS_ADC1_CH3_ATTEN0_INITCODE_DIFF    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_RD_DIS_ADC1_CH4_ATTEN0_INITCODE_DIFF    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_RD_DIS_ADC1_CH5_ATTEN0_INITCODE_DIFF    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_RD_DIS_ADC1_CH6_ATTEN0_INITCODE_DIFF    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_RD_DIS_ADC1_CH7_ATTEN0_INITCODE_DIFF    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_RD_DIS_ADC2_CH0_ATTEN0_INITCODE_DIFF    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_RD_DIS_ADC2_CH1_ATTEN0_INITCODE_DIFF    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_RD_DIS_ADC2_CH2_ATTEN0_INITCODE_DIFF    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_RD_DIS_ADC2_CH3_ATTEN0_INITCODE_DIFF    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_RD_DIS_ADC2_CH4_ATTEN0_INITCODE_DIFF    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_RD_DIS_ADC2_CH5_ATTEN0_INITCODE_DIFF    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_RD_DIS_ADC2_HI_DOUT_ATTEN0              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_RD_DIS_ADC2_HI_DOUT_ATTEN1              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_RD_DIS_ADC2_HI_DOUT_ATTEN2              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_RD_DIS_ADC2_HI_DOUT_ATTEN3              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_RD_DIS_BLOCK_KEY0                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_RD_DIS_BLOCK_KEY1                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_RD_DIS_BLOCK_KEY2                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_RD_DIS_BLOCK_KEY3                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_RD_DIS_BLOCK_KEY4                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_RD_DIS_BLOCK_KEY5                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_RD_DIS_BLOCK_SYS_DATA2                  esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_RD_DIS_TEMPERATURE_SENSOR               esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_SECURE_BOOT_AGGRESSIVE_REVOKE           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_SECURE_BOOT_DISABLE_FAST_WAKE           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_SECURE_BOOT_EN                          esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_SECURE_BOOT_KEY_REVOKE0                 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_SECURE_BOOT_KEY_REVOKE1                 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_SECURE_BOOT_KEY_REVOKE2                 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_SECURE_VERSION                          esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_SEC_DPA_LEVEL                           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_SOFT_DIS_JTAG                           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
ESP_EFUSE_SPI_BOOT_CRYPT_CNT                      esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
ESP_EFUSE_SPI_DOWNLOAD_MSPI_DIS                   esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
ESP_EFUSE_TEMP                                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_TEMPERATURE_SENSOR                      esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_UART_PRINT_CONTROL                      esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
ESP_EFUSE_USB_DEVICE_EXCHG_PINS                   esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_USB_OTG11_EXCHG_PINS                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_USB_PHY_SEL                             esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_USER_DATA                               esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_USER_DATA_MAC_CUSTOM                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WAFER_VERSION_MAJOR                     esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WAFER_VERSION_MINOR                     esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WDT_DELAY_SEL                           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS                                  esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ACTIVE_HP_DBIAS                  esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ACTIVE_LP_DBIAS                  esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ADC1_AVE_INITCODE_ATTEN0         esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ADC1_AVE_INITCODE_ATTEN1         esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ADC1_AVE_INITCODE_ATTEN2         esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ADC1_AVE_INITCODE_ATTEN3         esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ADC1_CH0_ATTEN0_INITCODE_DIFF    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ADC1_CH1_ATTEN0_INITCODE_DIFF    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ADC1_CH2_ATTEN0_INITCODE_DIFF    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ADC1_CH3_ATTEN0_INITCODE_DIFF    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ADC1_CH4_ATTEN0_INITCODE_DIFF    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ADC1_CH5_ATTEN0_INITCODE_DIFF    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ADC1_CH6_ATTEN0_INITCODE_DIFF    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ADC1_CH7_ATTEN0_INITCODE_DIFF    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ADC1_HI_DOUT_ATTEN0              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ADC1_HI_DOUT_ATTEN1              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ADC1_HI_DOUT_ATTEN2              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ADC1_HI_DOUT_ATTEN3              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ADC2_AVE_INITCODE_ATTEN0         esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ADC2_AVE_INITCODE_ATTEN1         esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ADC2_AVE_INITCODE_ATTEN2         esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ADC2_AVE_INITCODE_ATTEN3         esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ADC2_CH0_ATTEN0_INITCODE_DIFF    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ADC2_CH1_ATTEN0_INITCODE_DIFF    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ADC2_CH2_ATTEN0_INITCODE_DIFF    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ADC2_CH3_ATTEN0_INITCODE_DIFF    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ADC2_CH4_ATTEN0_INITCODE_DIFF    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ADC2_CH5_ATTEN0_INITCODE_DIFF    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ADC2_HI_DOUT_ATTEN0              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ADC2_HI_DOUT_ATTEN1              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ADC2_HI_DOUT_ATTEN2              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ADC2_HI_DOUT_ATTEN3              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_BLK1                             esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_WR_DIS_BLK_VERSION_MAJOR                esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_BLK_VERSION_MINOR                esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_BLOCK_KEY0                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_WR_DIS_BLOCK_KEY1                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_WR_DIS_BLOCK_KEY2                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_WR_DIS_BLOCK_KEY3                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_WR_DIS_BLOCK_KEY4                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_WR_DIS_BLOCK_KEY5                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_WR_DIS_BLOCK_SYS_DATA2                  esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_WR_DIS_BLOCK_USR_DATA                   esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_WR_DIS_CRYPT_DPA_ENABLE                 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_CUSTOM_MAC                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_DISABLE_BLK_VERSION_MAJOR        esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_DISABLE_WAFER_VERSION_MAJOR      esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_DIS_DIRECT_BOOT                  esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_DIS_DOWNLOAD_MANUAL_ENCRYPT      esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_DIS_DOWNLOAD_MODE                esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_DIS_FORCE_DOWNLOAD               esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_DIS_PAD_JTAG                     esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_DIS_SWD                          esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_DIS_TWAI                         esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_DIS_USB_JTAG                     esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_DIS_USB_OTG_DOWNLOAD_MODE        esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_DIS_USB_SERIAL_JTAG_DOWNLOAD_MODE esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_DIS_USB_SERIAL_JTAG_ROM_PRINT    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_DIS_WDT                          esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_DSLP_DBG                         esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_DSLP_LP_DBIAS                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ECDSA_ENABLE_SOFT_K              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ENABLE_SECURITY_DOWNLOAD         esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_FLASH_ECC_EN                     esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_FLASH_PAGE_SIZE                  esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_FLASH_TPUW                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_FLASH_TYPE                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_FORCE_DISABLE_SW_INIT_KEY        esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_FORCE_SEND_RESUME                esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_FORCE_USE_KEY_MANAGER_KEY        esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_HP_PWR_SRC_SEL                   esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_HYS_EN_PAD                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_JTAG_SEL_ENABLE                  esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_KEY_PURPOSE_0                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_WR_DIS_KEY_PURPOSE_1                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_WR_DIS_KEY_PURPOSE_2                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_WR_DIS_KEY_PURPOSE_3                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_WR_DIS_KEY_PURPOSE_4                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_WR_DIS_KEY_PURPOSE_5                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_WR_DIS_KM_DEPLOY_ONLY_ONCE              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_KM_DISABLE_DEPLOY_MODE           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_KM_HUK_GEN_STATE                 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_KM_RND_SWITCH_CYCLE              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_LDO_VO1_DREF                     esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_LDO_VO1_MUL                      esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_LDO_VO2_DREF                     esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_LDO_VO2_MUL                      esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_LDO_VO3_C                        esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_LDO_VO3_K                        esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_LDO_VO3_VOS                      esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_LDO_VO4_C                        esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_LDO_VO4_K                        esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_LDO_VO4_VOS                      esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_LOCK_KM_KEY                      esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_LP_DCDC_DBIAS_VOL_GAP            esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_LSLP_HP_DBIAS                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_MAC                              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_OPTIONAL_UNIQUE_ID               esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_PKG_VERSION                      esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_PSRAM_CAP                        esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_PSRAM_VENDOR                     esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_PXA0_TIEH_SEL_0                  esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_PXA0_TIEH_SEL_1                  esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_PXA0_TIEH_SEL_2                  esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_PXA0_TIEH_SEL_3                  esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_RD_DIS                           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
ESP_EFUSE_WR_DIS_SECURE_BOOT_AGGRESSIVE_REVOKE    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_SECURE_BOOT_DISABLE_FAST_WAKE    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_SECURE_BOOT_EN                   esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_SECURE_BOOT_KEY_REVOKE0          esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_WR_DIS_SECURE_BOOT_KEY_REVOKE1          esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_WR_DIS_SECURE_BOOT_KEY_REVOKE2          esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_WR_DIS_SECURE_VERSION                   esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_SEC_DPA_LEVEL                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_SOFT_DIS_JTAG                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_SPI_BOOT_CRYPT_CNT               esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
ESP_EFUSE_WR_DIS_SPI_DOWNLOAD_MSPI_DIS            esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_SYS_DATA_PART1                   esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_WR_DIS_TEMP                             esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_TEMPERATURE_SENSOR               esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_UART_PRINT_CONTROL               esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_USB_DEVICE_EXCHG_PINS            esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_USB_OTG11_EXCHG_PINS             esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_USB_PHY_SEL                      esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_WAFER_VERSION_MAJOR              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_WAFER_VERSION_MINOR              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_WDT_DELAY_SEL                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_XTS_KEY_LENGTH_256               esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_XTS_KEY_LENGTH_256                      esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
HP_SYS_CLKRST                                     esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32p4.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32p4.c.obj)
I2C_ANA_MST                                       esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32p4.c.obj)
LPPERI                                            esp-idf/esp_rom/libesp_rom.a(esp_rom_regi2c_esp32p4.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32p4.c.obj)
LP_ANA_PERI                                       esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32p4.c.obj)
LP_AON_CLKRST                                     esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
LP_TIMER                                          esp-idf/hal/libhal.a(lp_timer_hal.c.obj)
LP_UART                                           esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
LP_WDT                                            esp-idf/esp_rom/libesp_rom.a(esp_rom_wdt.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
PMU                                               esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
SPIMEM0                                           esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
SPIMEM1                                           esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
TIMERG0                                           esp-idf/esp_rom/libesp_rom.a(esp_rom_wdt.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
TIMERG1                                           esp-idf/esp_rom/libesp_rom.a(esp_rom_wdt.c.obj)
UART0                                             esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
__adddf3                                          /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(adddf3.o)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
__ashldi3                                         /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_ashldi3.o)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
__assert_func                                     esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
                                                  esp-idf/esp_rom/libesp_rom.a(esp_rom_regi2c_esp32p4.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32p4.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
__clz_tab                                         /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_clz.o)
                                                  /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_clzsi2.o)
                                                  /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_udivdi3.o)
__clzsi2                                          /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_clzsi2.o)
                                                  /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(extendsfdf2.o)
                                                  /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(floatunsidf.o)
                                                  /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(floatsidf.o)
                                                  /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(muldf3.o)
                                                  /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(divdf3.o)
                                                  /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(adddf3.o)
__divdf3                                          /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(divdf3.o)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
__extendsfdf2                                     /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(extendsfdf2.o)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
__fixdfsi                                         /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(fixdfsi.o)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
__fixunsdfsi                                      /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(fixunsdfsi.o)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
__floatsidf                                       /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(floatsidf.o)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
__floatunsidf                                     /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(floatunsidf.o)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
__getreent                                        esp-idf/main/libmain.a(bootloader_start.c.obj)
__lshrdi3                                         /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_lshrdi3.o)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
__muldf3                                          /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(muldf3.o)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
__popcountsi2                                     /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_popcountsi2.o)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
__sf                                              /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-impure.o)
__udivdi3                                         /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libgcc.a(_udivdi3.o)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
_bss_end                                          esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32p4.c.obj)
_bss_start                                        esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32p4.c.obj)
_data_end                                         esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32p4.c.obj)
_data_start                                       esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32p4.c.obj)
_dram_end                                         esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
_dram_start                                       esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
_impure_data                                      /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-impure.o)
                                                  esp-idf/main/libmain.a(bootloader_start.c.obj)
_impure_ptr                                       /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-impure.o)
abort                                             esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
                                                  esp-idf/hal/libhal.a(cache_hal.c.obj)
                                                  esp-idf/hal/libhal.a(mmu_hal.c.obj)
                                                  esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
bootloader_after_init                             esp-idf/main/libmain.a(bootloader_start.c.obj)
bootloader_ana_clock_glitch_reset_config          esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_ana_super_wdt_reset_config             esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32p4.c.obj)
bootloader_atexit                                 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_before_init                            esp-idf/main/libmain.a(bootloader_start.c.obj)
bootloader_check_bootloader_validity              esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32p4.c.obj)
bootloader_clear_bss_section                      esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32p4.c.obj)
bootloader_clock_configure                        esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32p4.c.obj)
bootloader_common_check_chip_revision_validity    esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
bootloader_common_check_chip_validity             esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
bootloader_common_check_efuse_blk_validity        esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
bootloader_common_get_active_otadata              esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_common_get_partition_description       esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_common_ota_select_crc                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_common_ota_select_invalid              esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_common_ota_select_valid                esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
bootloader_common_read_otadata                    esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_common_select_otadata                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
bootloader_config_wdt                             esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32p4.c.obj)
bootloader_configure_spi_pins                     esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32p4.c.obj)
bootloader_console_deinit                         esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_console_init                           esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32p4.c.obj)
bootloader_debug_buffer                           esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
bootloader_enable_random                          esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32p4.c.obj)
bootloader_enable_wp                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32p4.c.obj)
bootloader_execute_flash_command                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32p4.c.obj)
bootloader_fill_random                            esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
bootloader_flash_clock_config                     esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32p4.c.obj)
bootloader_flash_cs_timing_config                 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32p4.c.obj)
bootloader_flash_erase_range                      esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
bootloader_flash_erase_sector                     esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_flash_execute_command_common           esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
bootloader_flash_get_spi_mode                     esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32p4.c.obj)
bootloader_flash_is_octal_mode_enabled            esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
bootloader_flash_read                             esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
bootloader_flash_read_sfdp                        esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
bootloader_flash_reset_chip                       esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
bootloader_flash_unlock                           esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32p4.c.obj)
bootloader_flash_unlock_default                   esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
bootloader_flash_update_id                        esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32p4.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32p4.c.obj)
bootloader_flash_update_size                      esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32p4.c.obj)
bootloader_flash_write                            esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_flash_xmc_startup                      esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32p4.c.obj)
bootloader_image_hdr                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32p4.c.obj)
bootloader_init                                   esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32p4.c.obj)
                                                  esp-idf/main/libmain.a(bootloader_start.c.obj)
bootloader_init_mem                               esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32p4.c.obj)
bootloader_init_mspi_clock                        esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32p4.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32p4.c.obj)
bootloader_init_spi_flash                         esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32p4.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32p4.c.obj)
bootloader_load_image                             esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_load_image_no_verify                   esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
bootloader_mmap                                   esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_mmap_get_free_pages                    esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_munmap                                 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_print_banner                           esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32p4.c.obj)
bootloader_random_disable                         esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32p4.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_random_enable                          esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32p4.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
bootloader_read_bootloader_header                 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32p4.c.obj)
bootloader_read_flash_id                          esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32p4.c.obj)
bootloader_reset                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
                                                  esp-idf/main/libmain.a(bootloader_start.c.obj)
bootloader_sha256_data                            esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_sha256_finish                          esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_sha256_flash_contents                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_sha256_hex_to_str                      esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_sha256_start                           esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_spi_flash_reset                        esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
bootloader_utility_get_selected_boot_partition    esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
                                                  esp-idf/main/libmain.a(bootloader_start.c.obj)
bootloader_utility_load_boot_image                esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
                                                  esp-idf/main/libmain.a(bootloader_start.c.obj)
bootloader_utility_load_partition_table           esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
                                                  esp-idf/main/libmain.a(bootloader_start.c.obj)
cache_hal_disable                                 esp-idf/hal/libhal.a(cache_hal.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32p4.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
cache_hal_enable                                  esp-idf/hal/libhal.a(cache_hal.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32p4.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
cache_hal_freeze                                  esp-idf/hal/libhal.a(cache_hal.c.obj)
cache_hal_get_cache_line_size                     esp-idf/hal/libhal.a(cache_hal.c.obj)
cache_hal_init                                    esp-idf/hal/libhal.a(cache_hal.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32p4.c.obj)
cache_hal_invalidate_addr                         esp-idf/hal/libhal.a(cache_hal.c.obj)
cache_hal_is_cache_enabled                        esp-idf/hal/libhal.a(cache_hal.c.obj)
cache_hal_resume                                  esp-idf/hal/libhal.a(cache_hal.c.obj)
cache_hal_suspend                                 esp-idf/hal/libhal.a(cache_hal.c.obj)
cache_hal_unfreeze                                esp-idf/hal/libhal.a(cache_hal.c.obj)
cache_hal_vaddr_to_cache_level_id                 esp-idf/hal/libhal.a(cache_hal.c.obj)
cache_hal_writeback_addr                          esp-idf/hal/libhal.a(cache_hal.c.obj)
call_start_cpu0                                   esp-idf/main/libmain.a(bootloader_start.c.obj)
efuse_hal_blk_version                             esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
efuse_hal_chip_revision                           esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32p4.c.obj)
efuse_hal_clear_program_registers                 esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
efuse_hal_flash_encryption_enabled                esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
efuse_hal_get_chip_ver_pkg                        esp-idf/hal/libhal.a(efuse_hal.c.obj)
efuse_hal_get_disable_blk_version_major           esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
efuse_hal_get_disable_wafer_version_major         esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
efuse_hal_get_mac                                 esp-idf/hal/libhal.a(efuse_hal.c.obj)
efuse_hal_get_major_chip_version                  esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/hal/libhal.a(efuse_hal.c.obj)
efuse_hal_get_minor_chip_version                  esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/hal/libhal.a(efuse_hal.c.obj)
efuse_hal_is_coding_error_in_block                esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
efuse_hal_program                                 esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
efuse_hal_read                                    esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
efuse_hal_rs_calculate                            esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
efuse_hal_set_ecdsa_key                           esp-idf/hal/libhal.a(efuse_hal.c.obj)
efuse_hal_set_timing                              esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
esp_bootloader_desc                               esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
esp_bootloader_get_description                    esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
esp_cpu_configure_region_protection               esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
esp_efuse_batch_write_begin                       esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_efuse_batch_write_cancel                      esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_batch_write_commit                      esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_efuse_block_is_empty                          esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_check_errors                            esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_count_unused_key_blocks                 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_destroy_block                           esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_disable_rom_download_mode               esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
esp_efuse_enable_rom_secure_download_mode         esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_efuse_find_purpose                            esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_efuse_find_unused_key_block                   esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_get_coding_scheme                       esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
esp_efuse_get_digest_revoke                       esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_get_field_size                          esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
esp_efuse_get_key                                 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_get_key_dis_read                        esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_efuse_get_key_dis_write                       esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_efuse_get_key_purpose                         esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_get_keypurpose_dis_write                esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_efuse_get_pkg_ver                             esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
esp_efuse_get_purpose_field                       esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_get_write_protect_of_digest_revoke      esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_key_block_unused                        esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_read_block                              esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_read_field_bit                          esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_efuse_read_field_blob                         esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_efuse_read_field_cnt                          esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_efuse_read_reg                                esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_set_digest_revoke                       esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_set_key_dis_read                        esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_set_key_dis_write                       esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_set_key_purpose                         esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_set_keypurpose_dis_write                esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_set_read_protect                        esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_set_rom_log_scheme                      esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
esp_efuse_set_write_protect                       esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_set_write_protect_of_digest_revoke      esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_utility_apply_new_coding_scheme         esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_utility_burn_chip                       esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
esp_efuse_utility_burn_chip_opt                   esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_utility_burn_efuses                     esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_utility_check_errors                    esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_utility_clear_program_registers         esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
esp_efuse_utility_count_once                      esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_utility_debug_dump_blocks               esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
esp_efuse_utility_debug_dump_pending              esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
esp_efuse_utility_debug_dump_single_block         esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
esp_efuse_utility_erase_virt_blocks               esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
esp_efuse_utility_fill_buff                       esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_utility_get_number_of_items             esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_utility_get_read_register_address       esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_utility_is_correct_written_data         esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
esp_efuse_utility_process                         esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_utility_read_reg                        esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
esp_efuse_utility_reset                           esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_utility_update_virt_blocks              esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
esp_efuse_utility_write_blob                      esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_utility_write_cnt                       esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_utility_write_reg                       esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_write_block                             esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_write_field_bit                         esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_efuse_write_field_blob                        esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
esp_efuse_write_field_cnt                         esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_efuse_write_key                               esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_write_keys                              esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_write_reg                               esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_flash_encryption_cfg_verify_release_mode      esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_flash_encryption_enabled                      esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
esp_flash_encryption_set_release_mode             esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_flash_write_protect_crypt_cnt                 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_get_flash_encryption_mode                     esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_image_bootloader_offset_get                   esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
esp_image_bootloader_offset_set                   esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
esp_image_get_flash_size                          esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
esp_image_get_metadata                            esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
esp_image_verify                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
esp_image_verify_bootloader                       esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
esp_image_verify_bootloader_data                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
esp_log_early_timestamp                           esp-idf/log/liblog.a(log_timestamp.c.obj)
esp_log_timestamp                                 esp-idf/log/liblog.a(log_timestamp.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32p4.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32p4.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
                                                  esp-idf/main/libmain.a(bootloader_start.c.obj)
esp_partition_table_verify                        esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
esp_rom_crc32_le                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
esp_rom_delay_us                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32p4.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
esp_rom_get_cpu_ticks_per_us                      esp-idf/log/liblog.a(log_timestamp.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
esp_rom_get_reset_reason                          esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32p4.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                                                  esp-idf/main/libmain.a(bootloader_start.c.obj)
esp_rom_gpio_pad_set_drv                          esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32p4.c.obj)
esp_rom_install_uart_printf                       esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
esp_rom_md5_final                                 esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
esp_rom_md5_init                                  esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
esp_rom_md5_update                                esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
esp_rom_output_flush_tx                           esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
esp_rom_output_tx_wait_idle                       esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
esp_rom_printf                                    esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32p4.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32p4.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
                                                  esp-idf/main/libmain.a(bootloader_start.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
esp_rom_regi2c_read                               esp-idf/esp_rom/libesp_rom.a(esp_rom_regi2c_esp32p4.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
esp_rom_regi2c_read_mask                          esp-idf/esp_rom/libesp_rom.a(esp_rom_regi2c_esp32p4.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
esp_rom_regi2c_write                              esp-idf/esp_rom/libesp_rom.a(esp_rom_regi2c_esp32p4.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
esp_rom_regi2c_write_mask                         esp-idf/esp_rom/libesp_rom.a(esp_rom_regi2c_esp32p4.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32p4.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32p4.c.obj)
esp_rom_set_cpu_ticks_per_us                      esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
esp_rom_software_reset_system                     esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
esp_rom_spiflash_config_clk                       esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32p4.c.obj)
esp_rom_spiflash_config_param                     esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32p4.c.obj)
esp_rom_spiflash_erase_block                      esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
esp_rom_spiflash_erase_sector                     esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
esp_rom_spiflash_read                             esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
esp_rom_spiflash_wait_idle                        esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32p4.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
esp_rom_spiflash_write                            esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
esp_rom_spiflash_write_encrypted                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
esp_secure_boot_read_key_digests                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ets_efuse_clear_program_registers                 esp-idf/hal/libhal.a(efuse_hal.c.obj)
ets_efuse_rs_calculate                            esp-idf/hal/libhal.a(efuse_hal.c.obj)
ets_sha_enable                                    esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
ets_sha_finish                                    esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
ets_sha_init                                      esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
ets_sha_update                                    esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
lp_timer_hal_clear_alarm_intr_status              esp-idf/hal/libhal.a(lp_timer_hal.c.obj)
lp_timer_hal_clear_overflow_intr_status           esp-idf/hal/libhal.a(lp_timer_hal.c.obj)
lp_timer_hal_get_cycle_count                      esp-idf/hal/libhal.a(lp_timer_hal.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
lp_timer_hal_set_alarm_target                     esp-idf/hal/libhal.a(lp_timer_hal.c.obj)
memcmp                                            /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-memcmp.o)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
memcpy                                            /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-memcpy.o)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
memset                                            /Users/<USER>/.espressif/tools/riscv32-esp-elf/esp-14.2.0_20241119/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/14.2.0/../../../../riscv32-esp-elf/lib/rv32imafc_zicsr_zifencei/ilp32f/no-rtti/libc.a(libc_a-memset.o)
                                                  esp-idf/esp_rom/libesp_rom.a(esp_rom_wdt.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
                                                  esp-idf/main/libmain.a(bootloader_start.c.obj)
mmu_hal_bytes_to_pages                            esp-idf/hal/libhal.a(mmu_hal.c.obj)
mmu_hal_check_valid_ext_vaddr_region              esp-idf/hal/libhal.a(mmu_hal.c.obj)
mmu_hal_get_id_from_target                        esp-idf/hal/libhal.a(mmu_hal.c.obj)
mmu_hal_get_id_from_vaddr                         esp-idf/hal/libhal.a(mmu_hal.c.obj)
mmu_hal_init                                      esp-idf/hal/libhal.a(mmu_hal.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32p4.c.obj)
mmu_hal_map_region                                esp-idf/hal/libhal.a(mmu_hal.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
mmu_hal_paddr_to_vaddr                            esp-idf/hal/libhal.a(mmu_hal.c.obj)
mmu_hal_pages_to_bytes                            esp-idf/hal/libhal.a(mmu_hal.c.obj)
mmu_hal_unmap_all                                 esp-idf/hal/libhal.a(mmu_hal.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
mmu_hal_unmap_region                              esp-idf/hal/libhal.a(mmu_hal.c.obj)
mmu_hal_vaddr_to_paddr                            esp-idf/hal/libhal.a(mmu_hal.c.obj)
range_read_addr_blocks                            esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
range_write_addr_blocks                           esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
regi2c_read_impl                                  esp-idf/esp_rom/libesp_rom.a(esp_rom_regi2c_esp32p4.c.obj)
regi2c_read_mask_impl                             esp-idf/esp_rom/libesp_rom.a(esp_rom_regi2c_esp32p4.c.obj)
regi2c_write_impl                                 esp-idf/esp_rom/libesp_rom.a(esp_rom_regi2c_esp32p4.c.obj)
regi2c_write_mask_impl                            esp-idf/esp_rom/libesp_rom.a(esp_rom_regi2c_esp32p4.c.obj)
rom_cache_internal_table_ptr                      esp-idf/hal/libhal.a(cache_hal.c.obj)
rom_spiflash_legacy_data                          esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32p4.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
rtc_clk_32k_bootstrap                             esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
rtc_clk_32k_enable                                esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
rtc_clk_32k_enabled                               esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
rtc_clk_8m_enable                                 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
rtc_clk_8m_enabled                                esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
rtc_clk_apb_freq_get                              esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
rtc_clk_apll_coeff_calc                           esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
rtc_clk_apll_coeff_set                            esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
rtc_clk_apll_enable                               esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
rtc_clk_cpu_freq_get_config                       esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
rtc_clk_cpu_freq_mhz_to_config                    esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
rtc_clk_cpu_freq_set_config                       esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
rtc_clk_cpu_freq_set_config_fast                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
rtc_clk_cpu_freq_set_xtal                         esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
rtc_clk_cpu_freq_set_xtal_for_sleep               esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
rtc_clk_cpu_set_to_default_config                 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
rtc_clk_fast_src_get                              esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
rtc_clk_fast_src_set                              esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
rtc_clk_init                                      esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
rtc_clk_lp_pll_enable                             esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
rtc_clk_lp_pll_src_set                            esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
rtc_clk_mpll_configure                            esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
rtc_clk_mpll_disable                              esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
rtc_clk_mpll_enable                               esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
rtc_clk_mpll_get_freq                             esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
rtc_clk_rc32k_enable                              esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
rtc_clk_set_cpu_switch_to_pll                     esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
rtc_clk_slow_freq_get_hz                          esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
rtc_clk_slow_src_get                              esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
rtc_clk_slow_src_set                              esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
rtc_clk_xtal_freq_get                             esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
rtc_clk_xtal_freq_update                          esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
rtc_dig_8m_enabled                                esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
rtc_dig_clk8m_disable                             esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
rtc_dig_clk8m_enable                              esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
rv_utils_dbgr_is_attached                         esp-idf/riscv/libriscv.a(rv_utils.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
s_cache_hal_init_ctx                              esp-idf/hal/libhal.a(cache_hal.c.obj)
s_get_cache_state                                 esp-idf/hal/libhal.a(cache_hal.c.obj)
s_revoke_table                                    esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
s_table                                           esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
s_update_cache_state                              esp-idf/hal/libhal.a(cache_hal.c.obj)
wdt_hal_config_stage                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
wdt_hal_deinit                                    esp-idf/esp_rom/libesp_rom.a(esp_rom_wdt.c.obj)
wdt_hal_enable                                    esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
wdt_hal_init                                      esp-idf/esp_rom/libesp_rom.a(esp_rom_wdt.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
wdt_hal_set_flashboot_en                          esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
wdt_hal_write_protect_disable                     esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
wdt_hal_write_protect_enable                      esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
