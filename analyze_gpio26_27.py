#!/usr/bin/env python3
import fitz  # PyMuPDF
import re

def analyze_gpio26_27():
    """专门分析GPIO26/27相关的配置信息"""
    
    # 要搜索的关键词
    keywords = [
        'GPIO26', 'GPIO27', 'USB', 'PHY', 'OTG',
        '交换矩阵', '管脚分配', '复用', '默认',
        'UART', 'uart', '引脚', '管脚'
    ]
    
    pdf_files = ['Esp32-p4_datasheet_cn.pdf', 'Esp32-p4_technical_reference_manual_cn.pdf']
    
    for pdf_file in pdf_files:
        print(f"\n{'='*60}")
        print(f"分析文件: {pdf_file}")
        print(f"{'='*60}")
        
        try:
            doc = fitz.open(pdf_file)
            
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                text = page.get_text()
                
                # 查找包含GPIO26或GPIO27的段落
                lines = text.split('\n')
                for i, line in enumerate(lines):
                    if 'GPIO26' in line or 'GPIO27' in line:
                        # 显示上下文
                        start = max(0, i-5)
                        end = min(len(lines), i+6)
                        
                        print(f"\n--- 第{page_num + 1}页 ---")
                        for j in range(start, end):
                            marker = ">>> " if j == i else "    "
                            print(f"{marker}{lines[j]}")
                        print()
                
                # 查找USB相关的重要信息
                if any(keyword in text for keyword in ['USB PHY', 'USB 2.0', 'OTG', '交换矩阵']):
                    # 查找包含这些关键词的段落
                    for keyword in ['USB PHY', 'USB 2.0', 'OTG', '交换矩阵', '管脚分配']:
                        if keyword in text:
                            # 找到关键词所在的段落
                            paragraphs = text.split('\n\n')
                            for para in paragraphs:
                                if keyword in para and ('GPIO26' in para or 'GPIO27' in para or '26' in para or '27' in para):
                                    print(f"\n--- 第{page_num + 1}页 - {keyword}相关信息 ---")
                                    print(para)
                                    print()
            
            doc.close()
            
        except Exception as e:
            print(f"读取{pdf_file}失败: {e}")

if __name__ == "__main__":
    analyze_gpio26_27()
