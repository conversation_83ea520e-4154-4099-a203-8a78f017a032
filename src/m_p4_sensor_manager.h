/**
 * ESP32-P4 水质监测系统 - 传感器数据管理器
 * 
 * 基于Arduino版本的m_sensorDataManager.h移植到ESP-IDF平台
 * 提供传感器数据采集、缓存、验证和管理功能
 */

#ifndef M_P4_SENSOR_MANAGER_H
#define M_P4_SENSOR_MANAGER_H

#include "m_p4_system_config.h"
#include "m_p4_rs485.h"
#include <math.h>

// ==================== 传感器配置 ====================
#define SENSOR_BUFFER_SIZE          120     // 数据缓冲区大小（120个数据点）
#define SENSOR_POLLING_INTERVAL     3000    // 传感器轮询间隔(毫秒)
#define SENSOR_ROUND_INTERVAL       1000    // 轮次间隔(毫秒)
#define SENSOR_TIMEOUT              200     // 传感器响应超时(毫秒) - 极短超时时间
#define SENSOR_MAX_RETRIES          3       // 最大重试次数
#define SENSOR_OUTLIER_THRESHOLD    3.0     // 异常值阈值（标准差倍数）
#define SENSOR_AVERAGE_COUNT        10      // 平均值计算的数据点数量

// ==================== 传感器类型 ====================
typedef enum {
    SENSOR_TYPE_PH = 0,         // pH传感器
    SENSOR_TYPE_ORP,            // ORP传感器
    SENSOR_TYPE_DO,             // 溶解氧传感器
    SENSOR_TYPE_TEMP,           // 温度传感器
    SENSOR_TYPE_COUNT           // 传感器类型数量
} sensor_type_t;

// ==================== 传感器状态 ====================
typedef enum {
    SENSOR_STATE_OFFLINE = 0,   // 离线
    SENSOR_STATE_ONLINE,        // 在线
    SENSOR_STATE_ERROR,         // 错误
    SENSOR_STATE_CALIBRATING,   // 校准中
    SENSOR_STATE_MAINTENANCE    // 维护中
} sensor_state_t;

// ==================== 采集状态 ====================
typedef enum {
    ACQUISITION_STATE_STOPPED = 0,     // 停止
    ACQUISITION_STATE_INITIALIZING,    // 初始化中
    ACQUISITION_STATE_RUNNING,         // 运行中
    ACQUISITION_STATE_PAUSED,          // 暂停
    ACQUISITION_STATE_ERROR,           // 错误
    ACQUISITION_STATE_RECOVERING       // 恢复中
} acquisition_state_t;

// ==================== 传感器数据点 ====================
typedef struct {
    float value;                // 传感器数值
    float temperature;          // 温度值
    uint32_t timestamp;         // 时间戳(毫秒)
    uint32_t response_time;     // 响应时间(毫秒)
    bool is_valid;              // 数据是否有效
    bool is_outlier;            // 是否为异常值
    modbus_error_t error;       // 错误码
} sensor_data_point_t;

// ==================== 传感器配置 ====================
typedef struct {
    uint8_t slave_addr;         // Modbus从机地址
    uint16_t register_addr;     // 寄存器地址
    uint16_t register_count;    // 寄存器数量
    float scale_factor;         // 比例因子
    float offset;               // 偏移量
    float min_value;            // 最小有效值
    float max_value;            // 最大有效值
    uint32_t timeout_ms;        // 超时时间
    bool enabled;               // 是否启用
    char name[32];              // 传感器名称
} sensor_config_t;

// ==================== 传感器统计信息 ====================
typedef struct {
    uint32_t total_readings;    // 总读取次数
    uint32_t successful_readings; // 成功读取次数
    uint32_t error_readings;    // 错误读取次数
    uint32_t outlier_readings;  // 异常值读取次数
    uint32_t last_read_time;    // 最后读取时间
    float average_response_time; // 平均响应时间
    float success_rate;         // 成功率
    sensor_state_t state;       // 传感器状态
} sensor_stats_t;

// ==================== 传感器管理器结构 ====================
typedef struct {
    // 配置信息
    sensor_config_t configs[SENSOR_TYPE_COUNT];
    
    // 数据缓冲区（循环缓冲区）
    sensor_data_point_t buffers[SENSOR_TYPE_COUNT][SENSOR_BUFFER_SIZE];
    uint16_t buffer_head[SENSOR_TYPE_COUNT];    // 缓冲区头指针
    uint16_t buffer_count[SENSOR_TYPE_COUNT];   // 缓冲区数据数量
    
    // 统计信息
    sensor_stats_t stats[SENSOR_TYPE_COUNT];
    
    // 系统状态
    acquisition_state_t acquisition_state;
    uint32_t acquisition_start_time;
    uint32_t last_acquisition_time;
    uint32_t acquisition_cycle_count;
    
    // 同步控制
    SemaphoreHandle_t mutex;
    TaskHandle_t acquisition_task_handle;
    bool initialized;
    bool running;
    
} sensor_manager_t;

// ==================== 全局变量声明 ====================
extern sensor_manager_t g_sensor_manager;

// ==================== 函数声明 ====================

/**
 * 初始化传感器管理器
 * @return ESP_OK 成功，其他值表示失败
 */
esp_err_t sensor_manager_init(void);

/**
 * 反初始化传感器管理器
 * @return ESP_OK 成功，其他值表示失败
 */
esp_err_t sensor_manager_deinit(void);

/**
 * 启动传感器数据采集
 * @return ESP_OK 成功，其他值表示失败
 */
esp_err_t sensor_manager_start_acquisition(void);

/**
 * 停止传感器数据采集
 * @return ESP_OK 成功，其他值表示失败
 */
esp_err_t sensor_manager_stop_acquisition(void);

/**
 * 暂停传感器数据采集
 * @return ESP_OK 成功，其他值表示失败
 */
esp_err_t sensor_manager_pause_acquisition(void);

/**
 * 恢复传感器数据采集
 * @return ESP_OK 成功，其他值表示失败
 */
esp_err_t sensor_manager_resume_acquisition(void);

/**
 * 配置传感器参数
 * @param type 传感器类型
 * @param config 传感器配置
 * @return ESP_OK 成功，其他值表示失败
 */
esp_err_t sensor_manager_configure_sensor(sensor_type_t type, const sensor_config_t* config);

/**
 * 读取单个传感器数据
 * @param type 传感器类型
 * @param data_point 输出数据点
 * @return ESP_OK 成功，其他值表示失败
 */
esp_err_t sensor_manager_read_sensor(sensor_type_t type, sensor_data_point_t* data_point);

/**
 * 获取最新的传感器数据
 * @param type 传感器类型
 * @param data_point 输出数据点
 * @return ESP_OK 成功，其他值表示失败
 */
esp_err_t sensor_manager_get_latest_data(sensor_type_t type, sensor_data_point_t* data_point);

/**
 * 获取传感器历史数据
 * @param type 传感器类型
 * @param data_points 输出数据数组
 * @param count 数据点数量（输入：期望数量，输出：实际数量）
 * @return ESP_OK 成功，其他值表示失败
 */
esp_err_t sensor_manager_get_history_data(sensor_type_t type, sensor_data_point_t* data_points, uint16_t* count);

/**
 * 计算传感器数据的平均值
 * @param type 传感器类型
 * @param count 计算的数据点数量
 * @param average 输出平均值
 * @return ESP_OK 成功，其他值表示失败
 */
esp_err_t sensor_manager_calculate_average(sensor_type_t type, uint16_t count, float* average);

/**
 * 获取传感器统计信息
 * @param type 传感器类型
 * @param stats 输出统计信息
 * @return ESP_OK 成功，其他值表示失败
 */
esp_err_t sensor_manager_get_stats(sensor_type_t type, sensor_stats_t* stats);

/**
 * 打印传感器配置信息（与S3版本格式一致）
 */
void sensor_manager_print_config(void);

/**
 * 重置传感器统计信息
 * @param type 传感器类型
 * @return ESP_OK 成功，其他值表示失败
 */
esp_err_t sensor_manager_reset_stats(sensor_type_t type);

/**
 * 清空传感器数据缓冲区
 * @param type 传感器类型
 * @return ESP_OK 成功，其他值表示失败
 */
esp_err_t sensor_manager_clear_buffer(sensor_type_t type);

/**
 * 获取采集状态
 * @return acquisition_state_t 采集状态
 */
acquisition_state_t sensor_manager_get_acquisition_state(void);

/**
 * 检查传感器是否在线
 * @param type 传感器类型
 * @return true 在线，false 离线
 */
bool sensor_manager_is_sensor_online(sensor_type_t type);

/**
 * 获取传感器类型名称
 * @param type 传感器类型
 * @return const char* 传感器名称
 */
const char* sensor_manager_get_type_name(sensor_type_t type);

/**
 * 打印传感器管理器状态
 */
void sensor_manager_print_status(void);

/**
 * 打印所有传感器统计信息
 */
void sensor_manager_print_all_stats(void);

// sensor_acquisition_task函数现在是static的，不需要在头文件中声明

/**
 * 生成模拟传感器数据
 * @param type 传感器类型
 * @param data_point 输出数据点
 */
void generate_simulated_sensor_data(sensor_type_t type, sensor_data_point_t* data_point);

// ==================== 便利宏定义 ====================

/**
 * 检查传感器管理器是否已初始化
 */
#define SENSOR_MANAGER_IS_INITIALIZED() (g_sensor_manager.initialized)

/**
 * 检查传感器数据采集是否正在运行
 */
#define SENSOR_MANAGER_IS_RUNNING() (g_sensor_manager.running)

/**
 * 获取传感器缓冲区中的数据数量
 */
#define SENSOR_MANAGER_GET_BUFFER_COUNT(type) (g_sensor_manager.buffer_count[type])

#endif // M_P4_SENSOR_MANAGER_H
