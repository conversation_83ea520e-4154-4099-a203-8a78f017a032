/**
 * ESP32-P4 水质监测系统 - 看门狗模块实现
 * 专为ESP32-P4平台设计，高性能任务管理
 */

#include "m_p4_watchdog.h"
#include "esp_system.h"
#include <string.h>

// ==================== 创建全局看门狗管理器实例 ====================
WatchdogManager g_watchdog;

// ==================== 全局看门狗中断服务函数实现 ====================
void IRAM_ATTR watchdogResetModule(void* arg) {
    // 看门狗超时，重启系统
    printf("看门狗超时！系统即将重启...\n");

    // 强制重启ESP32
    esp_restart();
}

// ==================== WatchdogManager类实现 ====================

// 构造函数
WatchdogManager::WatchdogManager() {
    _task_mutex = xSemaphoreCreateMutex();
    _registeredTaskCount = 0;

    // 初始化任务状态数组
    for (int i = 0; i < MAX_TASKS; i++) {
        _taskStatus[i].status = TASK_STATUS_UNKNOWN;
        _taskStatus[i].lastReportTime = 0;
        _taskStatus[i].isRegistered = false;
        memset(_taskStatus[i].taskName, 0, sizeof(_taskStatus[i].taskName));
    }
}

// 析构函数
WatchdogManager::~WatchdogManager() {
    if (_watchdogTimer != NULL) {
        esp_timer_stop(_watchdogTimer);
        esp_timer_delete(_watchdogTimer);
    }

    if (_task_mutex != NULL) {
        vSemaphoreDelete(_task_mutex);
    }
}

// 初始化看门狗
bool WatchdogManager::init() {
    printf("初始化看门狗系统...\n");

    // 创建看门狗定时器
    esp_timer_create_args_t watchdog_conf = {
        .callback = watchdogResetModule,
        .name = "watchdog_timer"
    };

    esp_err_t watchdog_create_result = esp_timer_create(&watchdog_conf, &_watchdogTimer);
    if (watchdog_create_result != ESP_OK) {
        printf("看门狗定时器创建失败!\n");
        return false;
    }

    // 启动看门狗定时器
    esp_timer_start_once(_watchdogTimer, WATCHDOG_TIMEOUT_S * 1000000); // 转换为微秒
    printf("✅ 看门狗系统初始化完成，超时时间: %d秒\n", WATCHDOG_TIMEOUT_S);

    return true;
}

// 启动看门狗任务
bool WatchdogManager::startTask(int core, int priority, int stackSize) {
    // 创建看门狗任务
    BaseType_t result = xTaskCreatePinnedToCore(
        watchdogTaskFunction,     // 任务函数
        "Watchdog_Task",          // 任务名称
        stackSize,                // 堆栈大小
        this,                     // 任务参数
        priority,                 // 任务优先级
        &_watchdog_task_handle,   // 任务句柄
        core                      // 运行在指定核心
    );

    if (result != pdPASS) {
        printf("看门狗任务创建失败!\n");
        return false;
    }

    printf("✅ 看门狗任务创建成功，运行在Core %d\n", core);
    return true;
}

// 喂狗
void WatchdogManager::feedWatchdog() {
    if (_watchdogTimer != NULL) {
        // 检查所有任务状态
        bool allTasksHealthy = checkAllTasksHealth(esp_timer_get_time() / 1000);

        // 只有当所有任务健康或在初始化状态时才喂狗
        if (allTasksHealthy || _watchdogState == WATCHDOG_STATE_INIT) {
            esp_timer_stop(_watchdogTimer);
            esp_timer_start_once(_watchdogTimer, WATCHDOG_TIMEOUT_S * 1000000);
            // 可选：输出喂狗日志
            // printf("看门狗已喂食 - 所有任务正常\n");
        } else {
            printf("警告: 存在异常任务，延迟喂狗\n");
        }
    }
}

// 注册任务
bool WatchdogManager::registerTask(uint8_t taskId, const char* taskName) {
    if (taskId >= MAX_TASKS || _task_mutex == NULL) return false;

    if (xSemaphoreTake(_task_mutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        if (!_taskStatus[taskId].isRegistered) {
            _taskStatus[taskId].isRegistered = true;
            _taskStatus[taskId].status = TASK_STATUS_UNKNOWN;
            _taskStatus[taskId].lastReportTime = esp_timer_get_time() / 1000;
            strncpy(_taskStatus[taskId].taskName, taskName, sizeof(_taskStatus[taskId].taskName) - 1);
            _taskStatus[taskId].taskName[sizeof(_taskStatus[taskId].taskName) - 1] = '\0';
            _registeredTaskCount++;

            printf("任务注册: ID=%d, 名称=%s, 总数=%d\n", taskId, taskName, _registeredTaskCount);
        }
        xSemaphoreGive(_task_mutex);
        return true;
    }
    return false;
}

// 任务报告状态
bool WatchdogManager::reportTaskStatus(uint8_t taskId, uint8_t status) {
    if (taskId >= MAX_TASKS || _task_mutex == NULL) return false;

    if (xSemaphoreTake(_task_mutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        if (_taskStatus[taskId].isRegistered) {
            _taskStatus[taskId].status = status;
            _taskStatus[taskId].lastReportTime = esp_timer_get_time() / 1000;
        }
        xSemaphoreGive(_task_mutex);
        return true;
    }
    return false;
}

// 检查所有任务健康状态
bool WatchdogManager::checkAllTasksHealth(unsigned long currentTime) {
    if (_task_mutex == NULL || _registeredTaskCount == 0) return false;

    bool allHealthy = true;

    if (xSemaphoreTake(_task_mutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        for (int i = 0; i < MAX_TASKS; i++) {
            if (_taskStatus[i].isRegistered) {
                // 检查任务是否超时
                if (currentTime - _taskStatus[i].lastReportTime > TASK_TIMEOUT_MS) {
                    _taskStatus[i].status = TASK_STATUS_UNKNOWN;  // 超时设为未知状态
                    allHealthy = false;
                } else if (_taskStatus[i].status != TASK_STATUS_RUNNING) {
                    allHealthy = false;
                }
            }
        }
        xSemaphoreGive(_task_mutex);
    }

    return allHealthy;
}

// 打印任务状态
void WatchdogManager::printTaskStatus() {
    if (_task_mutex == NULL) return;

    printf("=== 任务状态报告 ===\n");
    if (xSemaphoreTake(_task_mutex, pdMS_TO_TICKS(100)) == pdTRUE) {
        for (int i = 0; i < MAX_TASKS; i++) {
            if (_taskStatus[i].isRegistered) {
                const char* statusStr = "";
                switch (_taskStatus[i].status) {
                    case TASK_STATUS_RUNNING: statusStr = "运行中"; break;
                    case TASK_STATUS_ERROR: statusStr = "错误"; break;
                    default: statusStr = "未知"; break;
                }

                unsigned long timeSinceReport = (esp_timer_get_time() / 1000) - _taskStatus[i].lastReportTime;
                printf("任务[%d] %s: %s (上次报告: %lu ms前)\n",
                             i, _taskStatus[i].taskName, statusStr, timeSinceReport);
            }
        }
        xSemaphoreGive(_task_mutex);
    }
    printf("==================\n");
}

// 获取当前看门狗状态
uint8_t WatchdogManager::getWatchdogState() {
    return _watchdogState;
}

// 获取已注册任务数量
uint8_t WatchdogManager::getRegisteredTaskCount() {
    return _registeredTaskCount;
}

// 便捷方法：传感器任务报告状态
void WatchdogManager::updateSensorTimestamp() {
    reportTaskStatus(TASK_ID_SENSOR, TASK_STATUS_RUNNING);
}

// 看门狗任务实现
void WatchdogManager::watchdogTaskImpl() {
    printf("看门狗任务启动在Core %d\n", xPortGetCoreID());

    // 等待系统初始化
    vTaskDelay(pdMS_TO_TICKS(3000));

    // 注册看门狗任务自身
    registerTask(TASK_ID_WATCHDOG, "Watchdog");

    // 设置初始状态
    _watchdogState = WATCHDOG_STATE_RUNNING;
    _lastWatchdogCheck = esp_timer_get_time() / 1000;

    printf("看门狗系统启动，等待 %d 个任务注册...\n", _registeredTaskCount);

    while(1) {
        unsigned long currentTime = esp_timer_get_time() / 1000;

        // 看门狗任务自身报告状态
        reportTaskStatus(TASK_ID_WATCHDOG, TASK_STATUS_RUNNING);

        // 每秒检查一次所有任务状态
        if (currentTime - _lastWatchdogCheck >= WATCHDOG_CHECK_INTERVAL_MS) {
            _lastWatchdogCheck = currentTime;

            // 检查所有已注册任务的状态
            bool allTasksHealthy = checkAllTasksHealth(currentTime);

            // 状态机逻辑
            switch (_watchdogState) {
                case WATCHDOG_STATE_INIT:
                    // 初始化状态，等待所有任务注册并报告健康状态
                    if (_registeredTaskCount > 0 && allTasksHealthy) {
                        _watchdogState = WATCHDOG_STATE_RUNNING;
                        printf("看门狗: 所有任务健康，进入正常运行状态\n");
                        printTaskStatus();
                    }
                    // 初始化阶段喂狗，防止过早重启
                    feedWatchdog();
                    break;

                case WATCHDOG_STATE_RUNNING:
                    // 正常运行状态，检查是否所有任务健康
                    if (!allTasksHealthy) {
                        _watchdogState = WATCHDOG_STATE_ERROR;
                        printf("看门狗: 检测到任务异常，进入错误状态\n");
                        printTaskStatus();
                    } else {
                        // 所有任务健康，喂狗
                        feedWatchdog();
                    }
                    break;

                case WATCHDOG_STATE_ERROR:
                    // 错误状态，不再喂狗，等待系统重启
                    printf("看门狗: 系统处于错误状态，即将重启...\n");
                    printTaskStatus();
                    // 不喂狗，让系统自动重启
                    vTaskDelay(pdMS_TO_TICKS(1000));
                    break;

                default:
                    // 未知状态，重置为初始化状态
                    _watchdogState = WATCHDOG_STATE_INIT;
                    break;
            }
        }

        vTaskDelay(pdMS_TO_TICKS(100)); // 短暂延时，减少CPU占用
    }
}
