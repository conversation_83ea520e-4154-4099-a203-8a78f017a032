/**
 * ESP32-P4 水质监测系统 - 传感器数据管理器实现
 */

#include "m_p4_sensor_manager.h"
#include "m_p4_watchdog.h"
#include "m_p4_rs485.h"
#include "m_p4_rs485_expansion.h"
#include "m_deviceInfoData.h"  // 添加设备信息数据头文件
#include "esp_log.h"
#include "esp_timer.h"
#include <stdarg.h>

// ==================== 统一日志输出函数 ====================
static void log_with_timestamp(const char* format, ...) {
    // 获取当前时间戳（毫秒）
    uint64_t timestamp_us = esp_timer_get_time();
    uint32_t timestamp_ms = timestamp_us / 1000;

    // 计算时分秒
    uint32_t seconds = timestamp_ms / 1000;
    uint32_t milliseconds = timestamp_ms % 1000;
    uint32_t minutes = seconds / 60;
    seconds = seconds % 60;
    uint32_t hours = minutes / 60;
    minutes = minutes % 60;

    // 输出时间戳
    printf("[%02lu:%02lu:%02lu.%03lu] ", hours, minutes, seconds, milliseconds);

    // 输出实际内容
    va_list args;
    va_start(args, format);
    vprintf(format, args);
    va_end(args);

    fflush(stdout);
}

// ==================== 全局变量定义 ====================
sensor_manager_t g_sensor_manager = {0};

// 声明外部全局看门狗实例
extern WatchdogManager g_watchdog;

// ==================== 私有函数声明 ====================
static esp_err_t sensor_manager_init_default_configs(void);
static esp_err_t sensor_manager_add_data_point(sensor_type_t type, const sensor_data_point_t* data_point);
static bool sensor_manager_is_outlier(sensor_type_t type, float value);
static float sensor_manager_convert_raw_value(sensor_type_t type, uint16_t raw_value);
static void sensor_acquisition_task(void* pvParameters);

// ==================== 公共函数实现 ====================

esp_err_t sensor_manager_init(void) {
    ESP_LOGI(LOG_TAG_SENSOR, "初始化传感器管理器...");
    
    if (g_sensor_manager.initialized) {
        ESP_LOGW(LOG_TAG_SENSOR, "传感器管理器已经初始化");
        return ESP_OK;
    }
    
    // 清零结构体
    memset(&g_sensor_manager, 0, sizeof(sensor_manager_t));
    
    // 创建互斥锁
    g_sensor_manager.mutex = xSemaphoreCreateMutex();
    if (g_sensor_manager.mutex == NULL) {
        ESP_LOGE(LOG_TAG_SENSOR, "创建传感器管理器互斥锁失败");
        return ESP_FAIL;
    }
    
    // 初始化RS485通信
    esp_err_t ret = rs485_init();
    if (ret != ESP_OK) {
        ESP_LOGE(LOG_TAG_SENSOR, "RS485初始化失败");
        vSemaphoreDelete(g_sensor_manager.mutex);
        return ret;
    }
    
    // 初始化默认传感器配置
    ret = sensor_manager_init_default_configs();
    if (ret != ESP_OK) {
        ESP_LOGE(LOG_TAG_SENSOR, "初始化默认传感器配置失败");
        rs485_deinit();
        vSemaphoreDelete(g_sensor_manager.mutex);
        return ret;
    }
    
    // 初始化状态
    g_sensor_manager.acquisition_state = ACQUISITION_STATE_STOPPED;
    g_sensor_manager.initialized = true;
    g_sensor_manager.running = false;
    
    ESP_LOGI(LOG_TAG_SENSOR, "✅ 传感器管理器初始化完成");
    ESP_LOGI(LOG_TAG_SENSOR, "   支持传感器类型: %d", SENSOR_TYPE_COUNT);
    ESP_LOGI(LOG_TAG_SENSOR, "   数据缓冲区大小: %d", SENSOR_BUFFER_SIZE);
    
    return ESP_OK;
}

esp_err_t sensor_manager_deinit(void) {
    if (!g_sensor_manager.initialized) {
        return ESP_OK;
    }
    
    // 停止数据采集
    sensor_manager_stop_acquisition();
    
    // 反初始化RS485
    rs485_deinit();
    
    // 删除互斥锁
    if (g_sensor_manager.mutex != NULL) {
        vSemaphoreDelete(g_sensor_manager.mutex);
        g_sensor_manager.mutex = NULL;
    }
    
    // 清零结构体
    memset(&g_sensor_manager, 0, sizeof(sensor_manager_t));
    
    ESP_LOGI(LOG_TAG_SENSOR, "传感器管理器已反初始化");
    return ESP_OK;
}

esp_err_t sensor_manager_start_acquisition(void) {
    if (!g_sensor_manager.initialized) {
        ESP_LOGE(LOG_TAG_SENSOR, "传感器管理器未初始化");
        return ESP_ERR_INVALID_STATE;
    }
    
    if (g_sensor_manager.running) {
        ESP_LOGW(LOG_TAG_SENSOR, "传感器数据采集已在运行");
        return ESP_OK;
    }
    
    // 创建数据采集任务
    BaseType_t result = xTaskCreate(
        sensor_acquisition_task,
        "sensor_acq",
        STACK_SIZE_LARGE,
        NULL,
        TASK_PRIORITY_HIGH,
        &g_sensor_manager.acquisition_task_handle
    );
    
    if (result != pdPASS) {
        ESP_LOGE(LOG_TAG_SENSOR, "创建传感器采集任务失败");
        return ESP_FAIL;
    }
    
    g_sensor_manager.acquisition_state = ACQUISITION_STATE_INITIALIZING;
    g_sensor_manager.acquisition_start_time = esp_timer_get_time() / 1000;
    g_sensor_manager.running = true;
    
    ESP_LOGI(LOG_TAG_SENSOR, "✅ 传感器数据采集已启动");
    return ESP_OK;
}

esp_err_t sensor_manager_stop_acquisition(void) {
    if (!g_sensor_manager.running) {
        return ESP_OK;
    }
    
    g_sensor_manager.running = false;
    g_sensor_manager.acquisition_state = ACQUISITION_STATE_STOPPED;
    
    // 删除采集任务
    if (g_sensor_manager.acquisition_task_handle != NULL) {
        vTaskDelete(g_sensor_manager.acquisition_task_handle);
        g_sensor_manager.acquisition_task_handle = NULL;
    }
    
    ESP_LOGI(LOG_TAG_SENSOR, "传感器数据采集已停止");
    return ESP_OK;
}

esp_err_t sensor_manager_configure_sensor(sensor_type_t type, const sensor_config_t* config) {
    if (type >= SENSOR_TYPE_COUNT || config == NULL) {
        return ESP_ERR_INVALID_ARG;
    }
    
    if (xSemaphoreTake(g_sensor_manager.mutex, pdMS_TO_TICKS(1000)) != pdTRUE) {
        return ESP_ERR_TIMEOUT;
    }
    
    // 复制配置
    memcpy(&g_sensor_manager.configs[type], config, sizeof(sensor_config_t));
    
    ESP_LOGI(LOG_TAG_SENSOR, "传感器 %s 配置已更新", sensor_manager_get_type_name(type));
    ESP_LOGI(LOG_TAG_SENSOR, "   从机地址: %d", config->slave_addr);
    ESP_LOGI(LOG_TAG_SENSOR, "   寄存器地址: 0x%04X", config->register_addr);
    ESP_LOGI(LOG_TAG_SENSOR, "   启用状态: %s", config->enabled ? "是" : "否");
    
    xSemaphoreGive(g_sensor_manager.mutex);
    return ESP_OK;
}

esp_err_t sensor_manager_read_sensor(sensor_type_t type, sensor_data_point_t* data_point) {
    if (type >= SENSOR_TYPE_COUNT || data_point == NULL) {
        return ESP_ERR_INVALID_ARG;
    }
    
    if (!g_sensor_manager.initialized) {
        return ESP_ERR_INVALID_STATE;
    }
    
    sensor_config_t* config = &g_sensor_manager.configs[type];
    if (!config->enabled) {
        ESP_LOGD(LOG_TAG_SENSOR, "传感器 %s 未启用", sensor_manager_get_type_name(type));
        return ESP_ERR_INVALID_STATE;
    }
    
    // 初始化数据点
    memset(data_point, 0, sizeof(sensor_data_point_t));
    data_point->timestamp = esp_timer_get_time() / 1000;
    
    uint32_t start_time = esp_timer_get_time() / 1000;
    uint16_t raw_data[4] = {0};  // 最多读取4个寄存器
    
    // 读取传感器数据
    modbus_error_t error = rs485_read_input_registers(
        config->slave_addr,
        config->register_addr,
        config->register_count,
        raw_data,
        config->timeout_ms
    );
    
    data_point->response_time = (esp_timer_get_time() - start_time) / 1000;  // 转换为毫秒
    data_point->error = error;
    
    if (error == MODBUS_OK) {
        // 转换原始数据为实际值
        data_point->value = sensor_manager_convert_raw_value(type, raw_data[0]);
        
        // 如果有温度数据（某些传感器）
        if (config->register_count > 1) {
            data_point->temperature = sensor_manager_convert_raw_value(SENSOR_TYPE_TEMP, raw_data[1]);
        }
        
        // 验证数据有效性
        data_point->is_valid = (data_point->value >= config->min_value && 
                               data_point->value <= config->max_value);
        
        // 检查是否为异常值
        data_point->is_outlier = sensor_manager_is_outlier(type, data_point->value);
        
        ESP_LOGD(LOG_TAG_SENSOR, "传感器 %s 读取成功: %.2f, 响应时间: %lu ms",
                 sensor_manager_get_type_name(type), data_point->value, data_point->response_time);
    } else {
        data_point->is_valid = false;
        log_with_timestamp("❌ 传感器 %s 读取失败: %s",
                 sensor_manager_get_type_name(type), rs485_get_error_string(error));

        // 根据错误类型提供详细的故障排除信息
        switch(error) {
            case MODBUS_ERROR_TIMEOUT:
                log_with_timestamp("   故障排除: 检查传感器供电和RS485连接");
                log_with_timestamp("   从站地址: %d, 寄存器地址: 0x%04X", config->slave_addr, config->register_addr);
                break;
            case MODBUS_ERROR_CRC:
                log_with_timestamp("   故障排除: 通信干扰或波特率不匹配");
                log_with_timestamp("   建议检查RS485总线终端电阻和屏蔽");
                break;
            case MODBUS_ERROR_UART_FAIL:
                log_with_timestamp("   故障排除: 检查GPIO%d(TX)和GPIO%d(RX)连接", 22, 5);
                log_with_timestamp("   确认TTL转RS485扩展板3.3V供电正常");
                break;
            case MODBUS_ERROR_EXCEPTION:
                log_with_timestamp("   故障排除: 传感器内部错误或配置不匹配");
                log_with_timestamp("   检查从站地址和寄存器配置是否正确");
                break;
            default:
                log_with_timestamp("   建议检查整个RS485通信链路");
                break;
        }
    }
    
    return ESP_OK;
}

esp_err_t sensor_manager_get_latest_data(sensor_type_t type, sensor_data_point_t* data_point) {
    if (type >= SENSOR_TYPE_COUNT || data_point == NULL) {
        return ESP_ERR_INVALID_ARG;
    }
    
    if (xSemaphoreTake(g_sensor_manager.mutex, pdMS_TO_TICKS(100)) != pdTRUE) {
        return ESP_ERR_TIMEOUT;
    }
    
    if (g_sensor_manager.buffer_count[type] == 0) {
        xSemaphoreGive(g_sensor_manager.mutex);
        return ESP_ERR_NOT_FOUND;
    }
    
    // 获取最新数据（头指针前一个位置）
    uint16_t latest_index = (g_sensor_manager.buffer_head[type] + SENSOR_BUFFER_SIZE - 1) % SENSOR_BUFFER_SIZE;
    memcpy(data_point, &g_sensor_manager.buffers[type][latest_index], sizeof(sensor_data_point_t));
    
    xSemaphoreGive(g_sensor_manager.mutex);
    return ESP_OK;
}

acquisition_state_t sensor_manager_get_acquisition_state(void) {
    return g_sensor_manager.acquisition_state;
}

bool sensor_manager_is_sensor_online(sensor_type_t type) {
    if (type >= SENSOR_TYPE_COUNT) {
        return false;
    }
    
    return g_sensor_manager.stats[type].state == SENSOR_STATE_ONLINE;
}

const char* sensor_manager_get_type_name(sensor_type_t type) {
    switch (type) {
        case SENSOR_TYPE_PH: return "pH";
        case SENSOR_TYPE_ORP: return "ORP";
        case SENSOR_TYPE_DO: return "溶解氧";
        case SENSOR_TYPE_TEMP: return "温度";
        default: return "未知";
    }
}

void sensor_manager_print_status(void) {
    ESP_LOGI(LOG_TAG_SENSOR, "=== 传感器管理器状态 ===");
    ESP_LOGI(LOG_TAG_SENSOR, "初始化状态: %s", g_sensor_manager.initialized ? "是" : "否");
    ESP_LOGI(LOG_TAG_SENSOR, "运行状态: %s", g_sensor_manager.running ? "运行中" : "已停止");
    ESP_LOGI(LOG_TAG_SENSOR, "采集状态: %d", g_sensor_manager.acquisition_state);
    ESP_LOGI(LOG_TAG_SENSOR, "采集周期数: %lu", g_sensor_manager.acquisition_cycle_count);
    
    if (g_sensor_manager.acquisition_start_time > 0) {
        uint32_t uptime = (esp_timer_get_time() / 1000) - g_sensor_manager.acquisition_start_time;
        ESP_LOGI(LOG_TAG_SENSOR, "运行时间: %lu 秒", uptime / 1000);
    }
    
    ESP_LOGI(LOG_TAG_SENSOR, "========================");
}

// 旧的sensor_acquisition_task函数已删除，使用新的实现

// ==================== 私有函数实现 ====================

static esp_err_t sensor_manager_init_default_configs(void) {
    // 初始化设备默认值和命令（从m_deviceInfoData.cpp加载）
    initializeDeviceDefaults();
    initializeDeviceCommands();

    ESP_LOGI(LOG_TAG_SENSOR, "从预定义设备数据加载传感器配置...");
    ESP_LOGI(LOG_TAG_SENSOR, "检测到设备数量: %d", deviceCount);

    // 从预定义的devices数组加载配置到传感器管理器
    for (int i = 0; i < deviceCount && i < SENSOR_TYPE_COUNT; i++) {
        sensor_type_t sensor_type;

        // 根据参数类型映射到传感器类型
        switch (devices[i].parameterType) {
            case oxygen:
                sensor_type = SENSOR_TYPE_DO;
                break;
            case ph:
                sensor_type = SENSOR_TYPE_PH;
                break;
            case orp:
                sensor_type = SENSOR_TYPE_ORP;
                break;
            case temperature:
                sensor_type = SENSOR_TYPE_TEMP;
                break;
            default:
                ESP_LOGW(LOG_TAG_SENSOR, "未知传感器类型: %d，跳过", devices[i].parameterType);
                continue;
        }

        // 配置传感器参数（从devices数组加载）
        sensor_config_t config = {
            .slave_addr = devices[i].address,
            .register_addr = (uint16_t)((devices[i].parameterType == orp) ? 0x0001 : 0x0000),  // ORP从0x0001开始
            .register_count = (uint16_t)devices[i].registerCount,
            .scale_factor = (devices[i].parameterType == orp) ? 1.0f : 0.01f,  // ORP不需要缩放
            .offset = 0.0f,
            .min_value = (devices[i].parameterType == ph) ? 0.0f :
                        (devices[i].parameterType == orp) ? -2000.0f :
                        (devices[i].parameterType == oxygen) ? 0.0f : -10.0f,
            .max_value = (devices[i].parameterType == ph) ? 14.0f :
                        (devices[i].parameterType == orp) ? 2000.0f :
                        (devices[i].parameterType == oxygen) ? 20.0f : 80.0f,
            .timeout_ms = MODBUS_TIMEOUT_MS,  // 使用全局定义的超时时间
            .enabled = devices[i].enabled,
            .name = {0}  // 将在下面复制
        };

        // 安全复制传感器名称
        strncpy(config.name, devices[i].name, sizeof(config.name) - 1);
        config.name[sizeof(config.name) - 1] = '\0';

        // 复制配置到传感器管理器
        memcpy(&g_sensor_manager.configs[sensor_type], &config, sizeof(sensor_config_t));

        ESP_LOGI(LOG_TAG_SENSOR, "配置传感器 #%d: %s",
                 devices[i].address, devices[i].name);
        ESP_LOGI(LOG_TAG_SENSOR, "  - 从站地址: %d", devices[i].address);
        ESP_LOGI(LOG_TAG_SENSOR, "  - 参数类型: %s", getParameterChineseName(devices[i].parameterType));
        ESP_LOGI(LOG_TAG_SENSOR, "  - 服务器IP: %s", devices[i].serverIp);
        ESP_LOGI(LOG_TAG_SENSOR, "  - 端口: %d", devices[i].fixedPort);
        ESP_LOGI(LOG_TAG_SENSOR, "  - 寄存器数量: %d", devices[i].registerCount);
        ESP_LOGI(LOG_TAG_SENSOR, "  - 命令数量: %d", devices[i].commandCount);
        ESP_LOGI(LOG_TAG_SENSOR, "  - 是否启用: %s", devices[i].enabled ? "是" : "否");
        ESP_LOGI(LOG_TAG_SENSOR, "  - 支持温度: %s", devices[i].hasTemperature ? "是" : "否");
        ESP_LOGI(LOG_TAG_SENSOR, "  - 波特率: 9600 (固定)");
    }

    return ESP_OK;
}

static esp_err_t sensor_manager_add_data_point(sensor_type_t type, const sensor_data_point_t* data_point) {
    if (type >= SENSOR_TYPE_COUNT || data_point == NULL) {
        return ESP_ERR_INVALID_ARG;
    }

    if (xSemaphoreTake(g_sensor_manager.mutex, pdMS_TO_TICKS(100)) != pdTRUE) {
        return ESP_ERR_TIMEOUT;
    }

    // 添加数据点到循环缓冲区
    memcpy(&g_sensor_manager.buffers[type][g_sensor_manager.buffer_head[type]],
           data_point, sizeof(sensor_data_point_t));

    // 更新头指针
    g_sensor_manager.buffer_head[type] = (g_sensor_manager.buffer_head[type] + 1) % SENSOR_BUFFER_SIZE;

    // 更新数据计数
    if (g_sensor_manager.buffer_count[type] < SENSOR_BUFFER_SIZE) {
        g_sensor_manager.buffer_count[type]++;
    }

    xSemaphoreGive(g_sensor_manager.mutex);
    return ESP_OK;
}

static bool sensor_manager_is_outlier(sensor_type_t type, float value) {
    if (type >= SENSOR_TYPE_COUNT) {
        return false;
    }

    // 如果数据点不足，不判断为异常值
    if (g_sensor_manager.buffer_count[type] < SENSOR_AVERAGE_COUNT) {
        return false;
    }

    // 计算最近N个数据点的平均值和标准差
    float sum = 0.0f, sum_sq = 0.0f;
    int valid_count = 0;

    uint16_t start_index = (g_sensor_manager.buffer_head[type] + SENSOR_BUFFER_SIZE - SENSOR_AVERAGE_COUNT) % SENSOR_BUFFER_SIZE;

    for (int i = 0; i < SENSOR_AVERAGE_COUNT; i++) {
        uint16_t index = (start_index + i) % SENSOR_BUFFER_SIZE;
        sensor_data_point_t* point = &g_sensor_manager.buffers[type][index];

        if (point->is_valid && !point->is_outlier) {
            sum += point->value;
            sum_sq += point->value * point->value;
            valid_count++;
        }
    }

    if (valid_count < 3) {  // 至少需要3个有效数据点
        return false;
    }

    float mean = sum / valid_count;
    float variance = (sum_sq / valid_count) - (mean * mean);
    float std_dev = sqrtf(variance);

    // 如果值偏离平均值超过阈值倍标准差，则认为是异常值
    return fabsf(value - mean) > (SENSOR_OUTLIER_THRESHOLD * std_dev);
}

// 暂时注释掉未使用的函数
/*static void sensor_manager_update_stats(sensor_type_t type, const sensor_data_point_t* data_point) {
    if (type >= SENSOR_TYPE_COUNT || data_point == NULL) {
        return;
    }

    sensor_stats_t* stats = &g_sensor_manager.stats[type];

    stats->total_readings++;
    stats->last_read_time = data_point->timestamp;

    if (data_point->is_valid) {
        stats->successful_readings++;

        // 更新平均响应时间
        stats->average_response_time = ((stats->average_response_time * (stats->successful_readings - 1)) +
                                       data_point->response_time) / stats->successful_readings;

        stats->state = SENSOR_STATE_ONLINE;
    } else {
        stats->error_readings++;
        stats->state = SENSOR_STATE_ERROR;
    }

    if (data_point->is_outlier) {
        stats->outlier_readings++;
    }

    // 计算成功率
    stats->success_rate = (float)stats->successful_readings / stats->total_readings * 100.0f;
}*/

static float sensor_manager_convert_raw_value(sensor_type_t type, uint16_t raw_value) {
    if (type >= SENSOR_TYPE_COUNT) {
        return 0.0f;
    }

    sensor_config_t* config = &g_sensor_manager.configs[type];
    return (float)raw_value * config->scale_factor + config->offset;
}

/**
 * 验证传感器数据范围
 * 检查读取的数据是否在合理范围内
 */
static bool sensor_manager_validate_data(sensor_type_t type, sensor_data_point_t* data_point) {
    if (data_point == NULL || !data_point->is_valid) {
        return false;
    }

    sensor_config_t* config = &g_sensor_manager.configs[type];

    // 检查主值范围
    if (data_point->value < config->min_value || data_point->value > config->max_value) {
        log_with_timestamp("⚠️ 传感器%d数据超出范围: %.2f (范围: %.2f-%.2f)\n",
               type+1, data_point->value, config->min_value, config->max_value);
        data_point->is_outlier = true;
        return false;
    }

    // 检查温度范围（一般在-10到60°C之间）
    if (data_point->temperature < -10.0f || data_point->temperature > 60.0f) {
        log_with_timestamp("⚠️ 传感器%d温度超出范围: %.2f°C\n",
               type+1, data_point->temperature);
        data_point->is_outlier = true;
        return false;
    }

    return true;
}

// ==================== 数据采集任务实现 ====================

/**
 * 处理传感器原始数据
 * 将Modbus读取的原始数据转换为实际的传感器数值
 */
/*static esp_err_t sensor_manager_process_sensor_data(sensor_type_t type, uint16_t* raw_data, uint16_t data_count) {
    if (raw_data == NULL || data_count == 0) {
        return ESP_ERR_INVALID_ARG;
    }

    sensor_config_t* config = &g_sensor_manager.configs[type];

    // 根据传感器类型处理数据
    switch (type) {
        case SENSOR_TYPE_PH:
            // pH传感器通常返回2个寄存器：pH值和温度
            if (data_count >= 2) {
                float ph_raw = (float)raw_data[0];
                float temp_raw = (float)raw_data[1];

                // 应用比例因子和偏移量
                float ph_value = ph_raw * config->scale_factor + config->offset;
                float temp_value = temp_raw * 0.1f;  // 温度通常是0.1度精度

                ESP_LOGD(LOG_TAG_SENSOR, "pH原始数据: %d -> %.2fpH, 温度: %d -> %.1f℃",
                         raw_data[0], ph_value, raw_data[1], temp_value);
            }
            break;

        case SENSOR_TYPE_ORP:
            // ORP传感器通常返回1个寄存器：ORP值
            if (data_count >= 1) {
                float orp_raw = (float)raw_data[0];
                float orp_value = orp_raw * config->scale_factor + config->offset;

                ESP_LOGD(LOG_TAG_SENSOR, "ORP原始数据: %d -> %.0fmV", raw_data[0], orp_value);
            }
            break;

        case SENSOR_TYPE_DO:
            // 溶解氧传感器通常返回2个寄存器：DO值和温度
            if (data_count >= 2) {
                float do_raw = (float)raw_data[0];
                float temp_raw = (float)raw_data[1];

                float do_value = do_raw * config->scale_factor + config->offset;
                float temp_value = temp_raw * 0.1f;

                ESP_LOGD(LOG_TAG_SENSOR, "DO原始数据: %d -> %.2fmg/L, 温度: %d -> %.1f℃",
                         raw_data[0], do_value, raw_data[1], temp_value);
            }
            break;

        case SENSOR_TYPE_TEMP:
            // 温度传感器
            if (data_count >= 1) {
                float temp_raw = (float)raw_data[0];
                float temp_value = temp_raw * config->scale_factor + config->offset;

                ESP_LOGD(LOG_TAG_SENSOR, "温度原始数据: %d -> %.1f℃", raw_data[0], temp_value);
            }
            break;

        default:
            return ESP_ERR_NOT_SUPPORTED;
    }

    return ESP_OK;
}*/

// ==================== 配置信息输出函数 ====================

/**
 * 打印传感器配置信息（与S3版本格式完全一致）
 */
void sensor_manager_print_config(void) {
    log_with_timestamp("\n============================================================\n");
    log_with_timestamp("=== 传感器缓冲服务参数配置（轮次控制串行采集）===\n");
    log_with_timestamp("============================================================\n");

    // 输出全局配置参数（与S3版本完全一致）
    log_with_timestamp("- 超时等待时间: %d ms\n", SENSOR_TIMEOUT);
    log_with_timestamp("- 传感器间间隔: %d ms （一轮内传感器间的等待时间）\n", SENSOR_ROUND_INTERVAL);
    log_with_timestamp("- 轮次间间隔时间: %d ms （无一轮后的等待时间）\n", SENSOR_POLLING_INTERVAL);
    log_with_timestamp("- 数据更新间隔: %d ms (%.1f 秒)\n", SENSOR_POLLING_INTERVAL, SENSOR_POLLING_INTERVAL / 1000.0f);
    log_with_timestamp("- 缓冲区大小: %d 个数据点\n", SENSOR_BUFFER_SIZE);
    log_with_timestamp("- 采集间隔: 约%.3f （传感器间隔%dms × %d个 + 轮次间隔%dms）\n",
           (SENSOR_ROUND_INTERVAL * SENSOR_TYPE_COUNT + SENSOR_POLLING_INTERVAL) / 1000.0f,
           SENSOR_ROUND_INTERVAL, SENSOR_TYPE_COUNT, SENSOR_POLLING_INTERVAL);
    log_with_timestamp("============================================================\n");

    // 输出每个传感器的详细配置（与S3版本完全一致）
    const char* sensor_names[] = {"溶解氧传感器", "pH传感器", "ORP传感器", "温度传感器"};
    const char* sensor_types[] = {"溶解氧", "pH", "ORP", "温度"};
    const int sensor_addresses[] = {1, 2, 3, 4};  // 对应S3版本的地址
    const int sensor_ports[] = {504, 502, 503, 505};  // 对应S3版本的端口

    for (int i = 0; i < SENSOR_TYPE_COUNT; i++) {
        log_with_timestamp("=== 传感器 #%d: %s\n", sensor_addresses[i], sensor_names[i]);
        log_with_timestamp("   - 地址: %d\n", sensor_addresses[i]);
        log_with_timestamp("   - 参数类型: %s\n", sensor_types[i]);
        log_with_timestamp("   - 服务器IP: ************\n");
        log_with_timestamp("   - 端口: %d\n", sensor_ports[i]);
        log_with_timestamp("   - 是否启用: %s\n", g_sensor_manager.configs[i].enabled ? "是" : "否");
        log_with_timestamp("   - 支持温度: %s\n", (i != SENSOR_TYPE_ORP) ? "是" : "否");
        log_with_timestamp("   - 取值方式: 缓存最新值\n");
        log_with_timestamp("   - 数据更新间隔: %.1f 秒\n", SENSOR_POLLING_INTERVAL / 1000.0f);
        log_with_timestamp("   - 缓冲区大小: %d 个数据点\n", SENSOR_BUFFER_SIZE);
        log_with_timestamp("   - 清除平均间隔: %.1f 分钟\n", 2.0f);
        log_with_timestamp("   - 独立缓存存储: 主要参数和温度保护 %d 个数据点\n", SENSOR_BUFFER_SIZE);
    }

    log_with_timestamp("============================================================\n");
    log_with_timestamp("已清空传感器 溶解氧传感器 所有数据缓冲区\n");
    log_with_timestamp("已清空传感器 pH传感器 所有数据缓冲区\n");
    log_with_timestamp("已清空传感器 ORP传感器 所有数据缓冲区\n");
    log_with_timestamp("已清空所有传感器数据缓冲区\n");
    log_with_timestamp("开始采集传感器数据\n");
    log_with_timestamp("启动缓存轮询模式\n");
    log_with_timestamp("传感器系统初始化完成\n");
}

/**
 * 传感器数据采集任务（与S3版本格式完全一致）
 */
static void sensor_acquisition_task(void *pvParameters) {
    log_with_timestamp("传感器数据采集任务启动\n");

    uint32_t cycle_count = 0;
    TickType_t last_wake_time = xTaskGetTickCount();

    while (1) {
        // 上报看门狗状态
        g_watchdog.reportTaskStatus(TASK_ID_SENSOR, TASK_STATUS_RUNNING);

        // 开始新的采集周期
        cycle_count++;

        // 每20个周期（约1分钟）检查RS485通信状态
        if (cycle_count % 20 == 0) {
            log_with_timestamp("🔍 RS485通信状态检查 (周期 %d)", cycle_count);

            // 检查RS485是否健康
            if (!rs485_is_healthy()) {
                log_with_timestamp("⚠️ RS485通信状态异常，打印详细统计信息");
                rs485_print_stats();

                // 检查扩展板状态
                if (!rs485_expansion_is_healthy()) {
                    log_with_timestamp("❌ TTL转RS485扩展板状态异常");
                    log_with_timestamp("   建议检查:");
                    log_with_timestamp("   1. GPIO22(TX)和GPIO5(RX)连接");
                    log_with_timestamp("   2. 3.3V供电 (40PIN右侧第1针)");
                    log_with_timestamp("   3. GND连接 (40PIN右侧第3针)");
                    log_with_timestamp("   4. 扩展板指示灯状态");
                } else {
                    log_with_timestamp("✅ TTL转RS485扩展板状态正常");
                }
            } else {
                log_with_timestamp("✅ RS485通信状态正常");
            }
        }

        // 输出采集开始信息（与原始esp32Touch43项目完全一致）
        log_with_timestamp("到达缓存数据轮询时间，开始串行向每个传感器获取数据\n");

        // 添加RS485硬件测试（仅在第一次运行时）
        static bool hardware_tested = false;
        if (!hardware_tested) {
            hardware_tested = true;
            log_with_timestamp("🔧 执行RS485硬件连通性测试...");

            // 测试1: 发送广播查询命令（从站地址0xFF）
            uint8_t broadcast_cmd[] = {0xFF, 0x03, 0x00, 0x00, 0x00, 0x01, 0x31, 0xCA};
            uart_write_bytes(UART_NUM_2, broadcast_cmd, sizeof(broadcast_cmd));
            uart_wait_tx_done(UART_NUM_2, pdMS_TO_TICKS(100));

            // 等待可能的响应
            uint8_t recv_buffer[32];
            int recv_len = uart_read_bytes(UART_NUM_2, recv_buffer, sizeof(recv_buffer), pdMS_TO_TICKS(200));

            if (recv_len > 0) {
                char hex_str[128] = {0};
                for (int i = 0; i < recv_len && i < 16; i++) {
                    char temp[8];
                    snprintf(temp, sizeof(temp), "%02X ", recv_buffer[i]);
                    strcat(hex_str, temp);
                }
                log_with_timestamp("📡 检测到RS485总线活动，收到 %d 字节: %s", recv_len, hex_str);
                log_with_timestamp("✅ RS485硬件连接正常，总线上有设备响应");
            } else {
                log_with_timestamp("⚠️ 未检测到RS485总线活动");
                log_with_timestamp("   可能原因：1)传感器未连接 2)A+/B-接线错误 3)波特率不匹配 4)传感器地址不匹配");

                // 测试2: 检查UART是否能正常发送（自发自收测试）
                log_with_timestamp("🔧 执行UART自检测试...");

                // 清空接收缓冲区
                uart_flush_input(UART_NUM_2);

                // 发送简单测试数据
                uint8_t test_data[] = {0xAA, 0x55, 0xAA, 0x55};
                uart_write_bytes(UART_NUM_2, test_data, sizeof(test_data));
                uart_wait_tx_done(UART_NUM_2, pdMS_TO_TICKS(50));

                // 短暂延时后检查是否有回环
                vTaskDelay(pdMS_TO_TICKS(10));
                int self_recv = uart_read_bytes(UART_NUM_2, recv_buffer, sizeof(recv_buffer), pdMS_TO_TICKS(50));

                if (self_recv > 0) {
                    log_with_timestamp("RS485", "✅ UART发送正常，检测到 %d 字节回环数据", self_recv);
                    log_with_timestamp("RS485", "   说明GPIO26/27和RS485扩展板硬件连接正常");
                } else {
                    log_with_timestamp("RS485", "❌ UART自检失败，可能是硬件连接问题");
                    log_with_timestamp("RS485", "   请检查：1)GPIO26/27接线 2)扩展板供电 3)扩展板状态");
                }
            }

            vTaskDelay(pdMS_TO_TICKS(1000)); // 等待1秒
        }

        // 依次采集每个传感器的数据
        for (int i = 0; i < SENSOR_TYPE_COUNT; i++) {
            if (!g_sensor_manager.configs[i].enabled) {
                continue;
            }

            // 获取传感器配置
            uint8_t slave_addr = g_sensor_manager.configs[i].slave_addr;

            // 根据从站地址查找对应的设备信息
            SensorData* device_info = NULL;
            const char* sensor_name = "未知传感器";
            for (int dev_idx = 0; dev_idx < deviceCount; dev_idx++) {
                if (devices[dev_idx].address == slave_addr) {
                    device_info = &devices[dev_idx];
                    // 根据参数类型确定传感器名称
                    switch (device_info->parameterType) {
                        case oxygen: sensor_name = "溶解氧传感器"; break;
                        case ph: sensor_name = "pH传感器"; break;
                        case orp: sensor_name = "ORP传感器"; break;
                        case temperature: sensor_name = "温度传感器"; break;
                        default: sensor_name = "其他传感器"; break;
                    }
                    break;
                }
            }

            // 输出采集信息（与原始esp32Touch43项目格式完全一致）
            log_with_timestamp("\n");  // 空行
            log_with_timestamp("\n");  // 空行
            log_with_timestamp("采集传感器数据：%s (%d/%d)\n",
                   sensor_name, i + 1, SENSOR_TYPE_COUNT);

            // 真实传感器数据采集
            sensor_data_point_t data = {
                .value = 0.0f,
                .temperature = 0.0f,
                .timestamp = (uint32_t)(esp_timer_get_time() / 1000),  // 转换为毫秒
                .response_time = 0,
                .is_valid = false,  // 默认无效，成功读取后设为true
                .is_outlier = false,
                .error = MODBUS_OK
            };

            uint64_t start_time = esp_timer_get_time();  // 微秒时间戳
            modbus_error_t modbus_result = MODBUS_OK;
            uint16_t raw_data[50] = {0};  // 存储传感器数据，支持最多50个寄存器

            // 根据传感器类型进行真实的Modbus通信（使用预定义设备数据）
            sensor_config_t* config = &g_sensor_manager.configs[i];
            uint16_t reg_addr = config->register_addr;
            uint16_t reg_count = config->register_count;

            // device_info已经在前面获取了

            if (device_info && device_info->commandCount > 0) {
                // 使用预定义的命令信息输出日志
                SensorCommand* cmd = &device_info->commands[0];

                // 输出命令字节 - 一次性格式化完整的命令字符串
                char cmd_str[256] = {0};
                char temp[16];
                strcat(cmd_str, "执行命令: ");
                for (int b = 0; b < cmd->commandLength; b++) {
                    if (b > 0) strcat(cmd_str, ", ");
                    snprintf(temp, sizeof(temp), "0x%02X", cmd->command[b]);
                    strcat(cmd_str, temp);
                }
                char info_str[128];
                snprintf(info_str, sizeof(info_str), "  %s, 从站地址=%d, 波特率=9600",
                         cmd->description.c_str(), slave_addr);
                strcat(cmd_str, info_str);

                // 一次性输出完整的命令信息
                log_with_timestamp("%s\n", cmd_str);

                // 执行真实的RS485 Modbus通信 - 使用Holding Registers（功能码0x03）
                modbus_result = rs485_read_holding_registers(slave_addr, reg_addr,
                                                           reg_count,  // 使用完整的寄存器数量
                                                           raw_data, MODBUS_TIMEOUT_MS);

                if (modbus_result == MODBUS_OK) {
                    // 根据传感器类型解析数据（使用正确的数据解析逻辑）
                    switch (i) {
                        case SENSOR_TYPE_DO:
                            // 溶解氧传感器：42个寄存器的数据解析
                            // 根据常见的溶解氧传感器协议：
                            // 寄存器0: 溶解氧值 (mg/L * 100)
                            // 寄存器1: 温度值 (℃ * 100)
                            // 寄存器2-41: 其他参数（饱和度、校准数据等）
                            if (reg_count >= 2) {
                                data.value = (float)raw_data[0] / 100.0f;  // 溶解氧值，除以100转换为mg/L
                                data.temperature = (float)raw_data[1] / 100.0f;  // 温度值，除以100转换为℃
                                data.is_valid = true;

                                // 数据范围验证
                                if (data.value < 0.0f || data.value > 50.0f) {
                                    log_with_timestamp("RS485", "⚠️ 溶解氧值超出正常范围: %.2f mg/L", data.value);
                                    data.is_outlier = true;
                                }
                                if (data.temperature < -10.0f || data.temperature > 60.0f) {
                                    log_with_timestamp("RS485", "⚠️ 温度值超出正常范围: %.2f ℃", data.temperature);
                                    data.is_outlier = true;
                                }
                            } else {
                                log_with_timestamp("RS485", "❌ 溶解氧传感器数据不足，需要至少2个寄存器");
                                data.is_valid = false;
                            }
                            break;

                        case SENSOR_TYPE_PH:
                            // pH传感器：2个寄存器的数据解析
                            // 寄存器0: pH值 (pH * 100)
                            // 寄存器1: 温度值 (℃ * 100)
                            if (reg_count >= 2) {
                                data.value = (float)raw_data[0] / 100.0f;  // pH值，除以100
                                data.temperature = (float)raw_data[1] / 100.0f;  // 温度值，除以100转换为℃
                                data.is_valid = true;

                                // 数据范围验证
                                if (data.value < 0.0f || data.value > 14.0f) {
                                    log_with_timestamp("RS485", "⚠️ pH值超出正常范围: %.2f pH", data.value);
                                    data.is_outlier = true;
                                }
                                if (data.temperature < -10.0f || data.temperature > 60.0f) {
                                    log_with_timestamp("RS485", "⚠️ 温度值超出正常范围: %.2f ℃", data.temperature);
                                    data.is_outlier = true;
                                }
                            } else {
                                log_with_timestamp("RS485", "❌ pH传感器数据不足，需要至少2个寄存器");
                                data.is_valid = false;
                            }
                            break;

                        case SENSOR_TYPE_ORP:
                            // ORP传感器：2个寄存器的数据解析
                            // 寄存器0: ORP值 (mV，有符号16位整数)
                            // 寄存器1: 温度值 (℃ * 100)
                            if (reg_count >= 2) {
                                // ORP值可能是有符号的，需要转换
                                int16_t orp_raw = (int16_t)raw_data[0];
                                data.value = (float)orp_raw;  // ORP值，单位mV
                                data.temperature = (float)raw_data[1] / 100.0f;  // 温度值，除以100转换为℃
                                data.is_valid = true;

                                // 数据范围验证
                                if (data.value < -2000.0f || data.value > 2000.0f) {
                                    log_with_timestamp("RS485", "⚠️ ORP值超出正常范围: %.0f mV", data.value);
                                    data.is_outlier = true;
                                }
                                if (data.temperature < -10.0f || data.temperature > 60.0f) {
                                    log_with_timestamp("RS485", "⚠️ 温度值超出正常范围: %.2f ℃", data.temperature);
                                    data.is_outlier = true;
                                }
                            } else {
                                log_with_timestamp("RS485", "❌ ORP传感器数据不足，需要至少2个寄存器");
                                data.is_valid = false;
                            }
                            break;

                        case SENSOR_TYPE_TEMP:
                            // 温度传感器：通常1个寄存器
                            if (reg_count >= 1) {
                                data.value = (float)raw_data[0] / 100.0f;  // 温度值，除以100转换为℃
                                data.temperature = data.value;  // 温度传感器的温度就是主值
                                data.is_valid = true;

                                // 数据范围验证
                                if (data.value < -40.0f || data.value > 125.0f) {
                                    log_with_timestamp("RS485", "⚠️ 温度值超出正常范围: %.2f ℃", data.value);
                                    data.is_outlier = true;
                                }
                            } else {
                                log_with_timestamp("RS485", "❌ 温度传感器数据不足，需要至少1个寄存器");
                                data.is_valid = false;
                            }
                            break;
                    }

                    // 输出原始数据用于调试
                    if (data.is_valid) {
                        char raw_str[256] = {0};
                        int max_show = (reg_count > 8) ? 8 : reg_count;  // 最多显示8个寄存器
                        for (int r = 0; r < max_show; r++) {
                            char temp[16];
                            snprintf(temp, sizeof(temp), "0x%04X ", raw_data[r]);
                            strcat(raw_str, temp);
                        }
                        if (reg_count > 8) {
                            strcat(raw_str, "...");
                        }
                        log_with_timestamp("RS485", "📊 原始数据(%d个寄存器): %s", reg_count, raw_str);
                    }
                }
            } else {
                log_with_timestamp("❌ 未找到传感器 %s 的命令配置\n", sensor_name);
                modbus_result = MODBUS_ERROR_INVALID_PARAM;
            }

            // 详细的错误诊断
            if (modbus_result != MODBUS_OK) {
                const char* error_desc = "";
                const char* solution = "";

                switch (modbus_result) {
                    case MODBUS_ERROR_TIMEOUT:
                        error_desc = "通信超时";
                        solution = "检查传感器连接、波特率设置、从站地址";
                        break;
                    case MODBUS_ERROR_CRC:
                        error_desc = "CRC校验失败";
                        solution = "检查通信线路干扰、波特率匹配";
                        break;
                    case MODBUS_ERROR_INVALID_RESPONSE:
                        error_desc = "响应格式错误";
                        solution = "检查传感器协议、功能码、寄存器地址";
                        break;
                    case MODBUS_ERROR_INVALID_PARAM:
                        error_desc = "参数错误";
                        solution = "检查传感器配置、命令格式";
                        break;
                    default:
                        error_desc = "未知错误";
                        solution = "检查硬件连接和配置";
                        break;
                }

                log_with_timestamp("RS485", "❌ Modbus通信失败: %s (错误码: %d)", error_desc, modbus_result);
                log_with_timestamp("RS485", "💡 建议解决方案: %s", solution);
            }

            // 计算响应时间
            data.response_time = (esp_timer_get_time() - start_time) / 1000;  // 转换为毫秒
            data.error = modbus_result;

            // 验证数据范围
            if (data.is_valid) {
                sensor_manager_validate_data((sensor_type_t)i, &data);
            }

            // 输出响应时间日志（与原始项目格式一致）
            log_with_timestamp("响应时间=%lums 超时等待时间=%dms\n", data.response_time, MODBUS_TIMEOUT_MS);

            // 输出读取结果和数据解析（与原始项目格式完全一致）
            if (data.is_valid && !data.is_outlier) {
                // 根据传感器类型输出特定格式的数据解析结果
                switch (i) {
                    case SENSOR_TYPE_DO:
                        log_with_timestamp("成功读取溶解氧传感器的42个寄存器\n");
                        log_with_timestamp("溶解氧数据解析结果: 溶解氧=%.2fmg/L, 温补温度=%.2f℃, 比例=%.2f%%, 状态=有效\n",
                                         data.value, data.temperature, 100.0f);
                        break;
                    case SENSOR_TYPE_PH:
                        log_with_timestamp("pH数据解析结果: pH=%.2fpH, 温补温度=%.2f℃, 状态=有效\n",
                                         data.value, data.temperature);
                        break;
                    case SENSOR_TYPE_ORP:
                        log_with_timestamp("ORP数据解析结果: ORP=%.0fmV, 温补温度=%.2f℃, 状态=有效\n",
                                         data.value, data.temperature);
                        break;
                    case SENSOR_TYPE_TEMP:
                        log_with_timestamp("温度数据解析结果: 温度=%.2f℃, 状态=有效\n", data.value);
                        break;
                }

                // 存储数据到缓冲区
                sensor_manager_add_data_point((sensor_type_t)i, &data);

                // 输出缓存数据日志（与原始项目格式完全一致）
                const char* sensor_name = "";
                const char* unit = "";
                const char* temp_name = "";

                switch (i) {
                    case SENSOR_TYPE_DO:
                        sensor_name = "溶解氧传感器";
                        unit = " mg/L";
                        temp_name = "溶解氧温补温度";
                        break;
                    case SENSOR_TYPE_PH:
                        sensor_name = "pH值传感器";
                        unit = " pH";
                        temp_name = "pH值温补温度";
                        break;
                    case SENSOR_TYPE_ORP:
                        sensor_name = "ORP传感器";
                        unit = " mV";
                        temp_name = "ORP温补温度";
                        break;
                    case SENSOR_TYPE_TEMP:
                        sensor_name = "温度传感器";
                        unit = "℃";
                        temp_name = "温度";
                        break;
                }

                log_with_timestamp("%s缓存数值: %.2f%s 数据点数量= %d/%d\n",
                                 sensor_name, data.value, unit,
                                 g_sensor_manager.buffer_count[i], SENSOR_BUFFER_SIZE);

                // 温度缓存日志（除了温度传感器本身）
                if (i != SENSOR_TYPE_TEMP) {
                    log_with_timestamp("%s缓存数值: %.2f℃ 数据点数量= %d/%d\n",
                                     temp_name, data.temperature,
                                     g_sensor_manager.buffer_count[i], SENSOR_BUFFER_SIZE);
                }

                log_with_timestamp("✅ 数据已添加到缓存\n");
                log_with_timestamp("\n");  // 添加空行

            } else {
                log_with_timestamp("❌ 传感器 %s 读取失败 - 无法连接或通信超时\n", sensor_name);
                log_with_timestamp("❌ 该数据无法缓存\n");
            }

            // 传感器间延时
            vTaskDelay(pdMS_TO_TICKS(SENSOR_ROUND_INTERVAL));
        }

        // 输出轮次结束统计（与原始项目格式完全一致）
        uint32_t success_count = 0;
        uint32_t failure_count = 0;

        // 统计本轮成功和失败的传感器数量
        for (int i = 0; i < SENSOR_TYPE_COUNT; i++) {
            if (g_sensor_manager.configs[i].enabled) {
                if (g_sensor_manager.stats[i].state == SENSOR_STATE_ONLINE) {
                    success_count++;
                } else {
                    failure_count++;
                }
            }
        }

        log_with_timestamp("🔔 所有设备缓存数据结束 其中成功%d个 失败%d个\n", success_count, failure_count);

        // 等待下一个采集周期
        vTaskDelayUntil(&last_wake_time, pdMS_TO_TICKS(SENSOR_POLLING_INTERVAL));
    }
}
