#!/usr/bin/env python3
import fitz  # PyMuPDF
import sys
import os

def extract_text_from_pdf(pdf_path, search_terms=None):
    """从PDF中提取文本，可选择搜索特定术语"""
    try:
        doc = fitz.open(pdf_path)
        all_text = ""
        found_sections = []
        
        for page_num in range(len(doc)):
            page = doc.load_page(page_num)
            text = page.get_text()
            all_text += f"\n=== 第{page_num + 1}页 ===\n{text}\n"
            
            # 如果有搜索词，查找相关内容
            if search_terms:
                for term in search_terms:
                    if term.lower() in text.lower():
                        found_sections.append({
                            'page': page_num + 1,
                            'term': term,
                            'context': text
                        })
        
        doc.close()
        return all_text, found_sections
    except Exception as e:
        return f"读取PDF失败: {e}", []

def main():
    # 查找项目中的PDF文件
    pdf_files = []
    for file in os.listdir('.'):
        if file.endswith('.pdf'):
            pdf_files.append(file)
    
    if not pdf_files:
        print("未找到PDF文件")
        return
    
    print(f"找到PDF文件: {pdf_files}")
    
    # 搜索与GPIO20/21和UART相关的内容
    search_terms = [
        'GPIO20', 'GPIO21', 'UART', 'uart',
        'UART0', 'UART1', 'UART2', 'UART3', 'UART4',
        'TXD', 'RXD', 'U0TXD', 'U0RXD',
        'ADC1_CH4', 'ADC1_CH5', 'ADC',
        '引脚复用', '引脚功能', 'pin function', 'pin mux',
        '功能选择', 'function select'
    ]
    
    for pdf_file in pdf_files:
        print(f"\n{'='*50}")
        print(f"正在读取: {pdf_file}")
        print(f"{'='*50}")
        
        text, found_sections = extract_text_from_pdf(pdf_file, search_terms)
        
        if found_sections:
            print(f"\n找到相关内容 ({len(found_sections)} 处):")
            for section in found_sections[:10]:  # 限制显示前10个结果
                print(f"\n--- 第{section['page']}页 - 关键词: {section['term']} ---")
                # 显示包含关键词的段落
                lines = section['context'].split('\n')
                for i, line in enumerate(lines):
                    if section['term'].lower() in line.lower():
                        # 显示前后几行作为上下文
                        start = max(0, i-2)
                        end = min(len(lines), i+3)
                        for j in range(start, end):
                            marker = ">>> " if j == i else "    "
                            print(f"{marker}{lines[j]}")
                        print()
        else:
            print("未找到相关的UART/GPIO信息")

if __name__ == "__main__":
    main()
