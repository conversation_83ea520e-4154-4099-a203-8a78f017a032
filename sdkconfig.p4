# ESP32-P4 专用配置文件
# 使用方法: cp sdkconfig.p4 sdkconfig

#
# ESP32-P4 特定配置
#
CONFIG_IDF_TARGET_ESP32P4=y
CONFIG_IDF_TARGET="esp32p4"
CONFIG_IDF_FIRMWARE_CHIP_ID=0x0012

#
# Serial flasher config
#
CONFIG_ESPTOOLPY_FLASHMODE_QIO=y
CONFIG_ESPTOOLPY_FLASHFREQ_80M=y
CONFIG_ESPTOOLPY_FLASHSIZE_16MB=y
CONFIG_ESPTOOLPY_FLASHSIZE="16MB"

#
# 处理器配置
#
CONFIG_ESP32P4_DEFAULT_CPU_FREQ_360=y
CONFIG_ESP32P4_DEFAULT_CPU_FREQ_MHZ=360

#
# 内存配置
#
CONFIG_ESP32P4_SPIRAM_SUPPORT=y
CONFIG_SPIRAM_MODE_OCT=y
CONFIG_SPIRAM_TYPE_AUTO=y
CONFIG_SPIRAM_SIZE=33554432
CONFIG_SPIRAM_SPEED_200M=y
CONFIG_SPIRAM_BOOT_INIT=y
CONFIG_SPIRAM_USE_MALLOC=y
CONFIG_SPIRAM_MALLOC_ALWAYSINTERNAL=16384

#
# 以太网配置
#
CONFIG_ETH_USE_ESP32_EMAC=y
CONFIG_ETH_PHY_INTERFACE_RMII=y
CONFIG_ETH_RMII_CLK_INPUT=y
CONFIG_ETH_RMII_CLK_IN_GPIO=50
CONFIG_ETH_PHY_IP101=y
CONFIG_ETH_PHY_RST_GPIO=51
CONFIG_ETH_PHY_ADDR=1

#
# WiFi配置 (通过ESP32-C6)
#
CONFIG_ESP_WIFI_REMOTE_ENABLED=y
CONFIG_ESP_WIFI_REMOTE_EPPP_CLIENT=y

#
# UART配置
#
CONFIG_ESP_CONSOLE_UART_DEFAULT=y
CONFIG_ESP_CONSOLE_UART_NUM=0
CONFIG_ESP_CONSOLE_UART_BAUDRATE=115200

#
# I2C配置
#
CONFIG_I2C_ENABLE_DEBUG_LOG=y

#
# SPI配置
#
CONFIG_SPI_MASTER_IN_IRAM=y
CONFIG_SPI_MASTER_ISR_IN_IRAM=y

#
# SDMMC配置
#
CONFIG_SDMMC_HOST_SLOT_1=y
CONFIG_SDMMC_HOST_SLOT_1_CLK_GPIO=43
CONFIG_SDMMC_HOST_SLOT_1_CMD_GPIO=44
CONFIG_SDMMC_HOST_SLOT_1_D0_GPIO=39
CONFIG_SDMMC_HOST_SLOT_1_D1_GPIO=40
CONFIG_SDMMC_HOST_SLOT_1_D2_GPIO=41
CONFIG_SDMMC_HOST_SLOT_1_D3_GPIO=42

#
# I2S配置
#
CONFIG_I2S_ENABLE_DEBUG_LOG=y
CONFIG_I2S_ISR_IRAM_SAFE=y

#
# FreeRTOS配置
#
CONFIG_FREERTOS_HZ=1000
CONFIG_FREERTOS_ASSERT_ON_UNTESTED_FUNCTION=y
CONFIG_FREERTOS_CHECK_STACKOVERFLOW_PTRVAL=y
CONFIG_FREERTOS_THREAD_LOCAL_STORAGE_POINTERS=1
CONFIG_FREERTOS_IDLE_TASK_STACKSIZE=1536
CONFIG_FREERTOS_ISR_STACKSIZE=1536
CONFIG_FREERTOS_LEGACY_HOOKS=y
CONFIG_FREERTOS_MAX_TASK_NAME_LEN=16
CONFIG_FREERTOS_SUPPORT_STATIC_ALLOCATION=y
CONFIG_FREERTOS_TIMER_TASK_PRIORITY=1
CONFIG_FREERTOS_TIMER_TASK_STACK_DEPTH=2048
CONFIG_FREERTOS_TIMER_QUEUE_LENGTH=10
CONFIG_FREERTOS_QUEUE_REGISTRY_SIZE=0
CONFIG_FREERTOS_USE_TRACE_FACILITY=y
CONFIG_FREERTOS_USE_STATS_FORMATTING_FUNCTIONS=y
CONFIG_FREERTOS_VTASKLIST_INCLUDE_COREID=y
CONFIG_FREERTOS_GENERATE_RUN_TIME_STATS=y
CONFIG_FREERTOS_RUN_TIME_STATS_USING_ESP_TIMER=y

#
# 堆内存配置
#
CONFIG_HEAP_CORRUPTION_DETECTION=y
CONFIG_HEAP_TRACING_DEST_TRACING=y
CONFIG_HEAP_TASK_TRACKING=y

#
# 日志配置
#
CONFIG_LOG_DEFAULT_LEVEL_INFO=y
CONFIG_LOG_DEFAULT_LEVEL=3
CONFIG_LOG_MAXIMUM_EQUALS_DEFAULT=y
CONFIG_LOG_MAXIMUM_LEVEL=3
CONFIG_LOG_COLORS=y
CONFIG_LOG_TIMESTAMP_SOURCE_RTOS=y

#
# NVS配置 (暂时禁用加密)
#
# CONFIG_NVS_ENCRYPTION is not set
# CONFIG_NVS_SEC_KEY_PROTECTION_SCHEME_HMAC_BASED is not set

#
# 分区表配置
#
CONFIG_PARTITION_TABLE_CUSTOM=y
CONFIG_PARTITION_TABLE_CUSTOM_FILENAME="partitions.csv"
CONFIG_PARTITION_TABLE_FILENAME="partitions.csv"
CONFIG_PARTITION_TABLE_OFFSET=0x8000
CONFIG_PARTITION_TABLE_MD5=y

#
# 应用程序配置
#
CONFIG_APP_COMPILE_TIME_DATE=y
CONFIG_APP_EXCLUDE_PROJECT_VER_VAR=y
CONFIG_APP_EXCLUDE_PROJECT_NAME_VAR=y

#
# 编译器配置
#
CONFIG_COMPILER_OPTIMIZATION_SIZE=y
CONFIG_COMPILER_CXX_EXCEPTIONS=y
CONFIG_COMPILER_CXX_RTTI=y
CONFIG_COMPILER_STACK_CHECK_MODE_NORM=y
CONFIG_COMPILER_WARN_WRITE_STRINGS=y

#
# 组件配置
#
CONFIG_ARDUINO_RUNNING_CORE=1
CONFIG_ARDUINO_EVENT_RUNNING_CORE=1
CONFIG_ARDUINO_UDP_RUNNING_CORE=1
CONFIG_ARDUINO_ISR_IRAM=y
CONFIG_ARDUINO_LOOP_STACK_SIZE=8192

#
# HTTP客户端配置
#
CONFIG_ESP_HTTP_CLIENT_ENABLE_HTTPS=y
CONFIG_ESP_HTTP_CLIENT_ENABLE_BASIC_AUTH=y

#
# TCP/IP配置
#
CONFIG_LWIP_MAX_SOCKETS=16
CONFIG_LWIP_SO_REUSE=y
CONFIG_LWIP_SO_REUSE_RXTOALL=y
CONFIG_LWIP_IP_FORWARD=y
CONFIG_LWIP_IPV4=y
CONFIG_LWIP_IPV6=y
CONFIG_LWIP_NETIF_LOOPBACK=y
CONFIG_LWIP_LOOPBACK_MAX_PBUFS=8

#
# mDNS配置
#
CONFIG_MDNS_MAX_SERVICES=10
CONFIG_MDNS_TASK_PRIORITY=1
CONFIG_MDNS_TASK_STACK_SIZE=4096
CONFIG_MDNS_SERVICE_ADD_TIMEOUT_MS=2000
CONFIG_MDNS_TIMER_PERIOD_MS=100

#
# 安全配置 (暂时禁用)
#
# CONFIG_SECURE_BOOT is not set
# CONFIG_SECURE_BOOT_V2_ENABLED is not set
# CONFIG_SECURE_SIGNED_APPS_NO_SECURE_BOOT is not set
# CONFIG_SECURE_SIGNED_ON_BOOT_NO_SECURE_BOOT is not set
# CONFIG_SECURE_SIGNED_ON_UPDATE_NO_SECURE_BOOT is not set

#
# 调试配置
#
CONFIG_ESP_SYSTEM_PANIC_PRINT_HALT=y
CONFIG_ESP_SYSTEM_PANIC_PRINT_REBOOT=y
CONFIG_ESP_SYSTEM_GDBSTUB_RUNTIME=y
CONFIG_ESP_DEBUG_OCDAWARE=y
CONFIG_ESP_SYSTEM_CHECK_INT_LEVEL_4=y

#
# 性能监控
#
CONFIG_ESP_SYSTEM_EVENT_QUEUE_SIZE=32
CONFIG_ESP_SYSTEM_EVENT_TASK_STACK_SIZE=2304
CONFIG_ESP_MAIN_TASK_STACK_SIZE=3584
CONFIG_ESP_MAIN_TASK_AFFINITY_CPU0=y
CONFIG_ESP_MINIMAL_SHARED_STACK_SIZE=2048

#
# 看门狗配置
#
CONFIG_ESP_TASK_WDT=y
CONFIG_ESP_TASK_WDT_PANIC=y
CONFIG_ESP_TASK_WDT_TIMEOUT_S=5
CONFIG_ESP_TASK_WDT_CHECK_IDLE_TASK_CPU0=y
CONFIG_ESP_TASK_WDT_CHECK_IDLE_TASK_CPU1=y
CONFIG_ESP_INT_WDT=y
CONFIG_ESP_INT_WDT_TIMEOUT_MS=300
CONFIG_ESP_INT_WDT_CHECK_CPU1=y
