#
                # Automatically generated file. DO NOT EDIT.
                # Espressif IoT Development Framework (ESP-IDF) Configuration cmake include file
                #
set(CONFIG_SOC_ADC_SUPPORTED "y")
set(CONFIG_SOC_ANA_CMPR_SUPPORTED "y")
set(CONFIG_SOC_DEDICATED_GPIO_SUPPORTED "y")
set(CONFIG_SOC_UART_SUPPORTED "y")
set(CONFIG_SOC_GDMA_SUPPORTED "y")
set(CONFIG_SOC_UHCI_SUPPORTED "y")
set(CONFIG_SOC_AHB_GDMA_SUPPORTED "y")
set(CONFIG_SOC_AXI_GDMA_SUPPORTED "y")
set(CONFIG_SOC_DW_GDMA_SUPPORTED "y")
set(CONFIG_SOC_DMA2D_SUPPORTED "y")
set(CONFIG_SOC_GPTIMER_SUPPORTED "y")
set(CONFIG_SOC_PCNT_SUPPORTED "y")
set(CONFIG_SOC_LCDCAM_SUPPORTED "y")
set(CONFIG_SOC_LCDCAM_CAM_SUPPORTED "y")
set(CONFIG_SOC_LCDCAM_I80_LCD_SUPPORTED "y")
set(CONFIG_SOC_LCDCAM_RGB_LCD_SUPPORTED "y")
set(CONFIG_SOC_MIPI_CSI_SUPPORTED "y")
set(CONFIG_SOC_MIPI_DSI_SUPPORTED "y")
set(CONFIG_SOC_MCPWM_SUPPORTED "y")
set(CONFIG_SOC_TWAI_SUPPORTED "y")
set(CONFIG_SOC_ETM_SUPPORTED "y")
set(CONFIG_SOC_PARLIO_SUPPORTED "y")
set(CONFIG_SOC_ASYNC_MEMCPY_SUPPORTED "y")
set(CONFIG_SOC_EMAC_SUPPORTED "y")
set(CONFIG_SOC_USB_OTG_SUPPORTED "y")
set(CONFIG_SOC_WIRELESS_HOST_SUPPORTED "y")
set(CONFIG_SOC_USB_SERIAL_JTAG_SUPPORTED "y")
set(CONFIG_SOC_TEMP_SENSOR_SUPPORTED "y")
set(CONFIG_SOC_SUPPORTS_SECURE_DL_MODE "y")
set(CONFIG_SOC_ULP_SUPPORTED "y")
set(CONFIG_SOC_LP_CORE_SUPPORTED "y")
set(CONFIG_SOC_EFUSE_KEY_PURPOSE_FIELD "y")
set(CONFIG_SOC_EFUSE_SUPPORTED "y")
set(CONFIG_SOC_RTC_FAST_MEM_SUPPORTED "y")
set(CONFIG_SOC_RTC_MEM_SUPPORTED "y")
set(CONFIG_SOC_RMT_SUPPORTED "y")
set(CONFIG_SOC_I2S_SUPPORTED "y")
set(CONFIG_SOC_SDM_SUPPORTED "y")
set(CONFIG_SOC_GPSPI_SUPPORTED "y")
set(CONFIG_SOC_LEDC_SUPPORTED "y")
set(CONFIG_SOC_ISP_SUPPORTED "y")
set(CONFIG_SOC_I2C_SUPPORTED "y")
set(CONFIG_SOC_SYSTIMER_SUPPORTED "y")
set(CONFIG_SOC_AES_SUPPORTED "y")
set(CONFIG_SOC_MPI_SUPPORTED "y")
set(CONFIG_SOC_SHA_SUPPORTED "y")
set(CONFIG_SOC_HMAC_SUPPORTED "y")
set(CONFIG_SOC_DIG_SIGN_SUPPORTED "y")
set(CONFIG_SOC_ECC_SUPPORTED "y")
set(CONFIG_SOC_ECC_EXTENDED_MODES_SUPPORTED "y")
set(CONFIG_SOC_FLASH_ENC_SUPPORTED "y")
set(CONFIG_SOC_SECURE_BOOT_SUPPORTED "y")
set(CONFIG_SOC_BOD_SUPPORTED "y")
set(CONFIG_SOC_VBAT_SUPPORTED "y")
set(CONFIG_SOC_APM_SUPPORTED "y")
set(CONFIG_SOC_PMU_SUPPORTED "y")
set(CONFIG_SOC_DCDC_SUPPORTED "y")
set(CONFIG_SOC_PAU_SUPPORTED "y")
set(CONFIG_SOC_LP_TIMER_SUPPORTED "y")
set(CONFIG_SOC_ULP_LP_UART_SUPPORTED "y")
set(CONFIG_SOC_LP_GPIO_MATRIX_SUPPORTED "y")
set(CONFIG_SOC_LP_PERIPHERALS_SUPPORTED "y")
set(CONFIG_SOC_LP_I2C_SUPPORTED "y")
set(CONFIG_SOC_LP_I2S_SUPPORTED "y")
set(CONFIG_SOC_LP_SPI_SUPPORTED "y")
set(CONFIG_SOC_LP_ADC_SUPPORTED "y")
set(CONFIG_SOC_LP_VAD_SUPPORTED "y")
set(CONFIG_SOC_SPIRAM_SUPPORTED "y")
set(CONFIG_SOC_PSRAM_DMA_CAPABLE "y")
set(CONFIG_SOC_SDMMC_HOST_SUPPORTED "y")
set(CONFIG_SOC_CLK_TREE_SUPPORTED "y")
set(CONFIG_SOC_ASSIST_DEBUG_SUPPORTED "y")
set(CONFIG_SOC_DEBUG_PROBE_SUPPORTED "y")
set(CONFIG_SOC_WDT_SUPPORTED "y")
set(CONFIG_SOC_SPI_FLASH_SUPPORTED "y")
set(CONFIG_SOC_TOUCH_SENSOR_SUPPORTED "y")
set(CONFIG_SOC_RNG_SUPPORTED "y")
set(CONFIG_SOC_GP_LDO_SUPPORTED "y")
set(CONFIG_SOC_PPA_SUPPORTED "y")
set(CONFIG_SOC_LIGHT_SLEEP_SUPPORTED "y")
set(CONFIG_SOC_DEEP_SLEEP_SUPPORTED "y")
set(CONFIG_SOC_PM_SUPPORTED "y")
set(CONFIG_SOC_BITSCRAMBLER_SUPPORTED "y")
set(CONFIG_SOC_SIMD_INSTRUCTION_SUPPORTED "y")
set(CONFIG_SOC_I3C_MASTER_SUPPORTED "y")
set(CONFIG_SOC_XTAL_SUPPORT_40M "y")
set(CONFIG_SOC_AES_SUPPORT_DMA "y")
set(CONFIG_SOC_AES_SUPPORT_GCM "y")
set(CONFIG_SOC_AES_GDMA "y")
set(CONFIG_SOC_AES_SUPPORT_AES_128 "y")
set(CONFIG_SOC_AES_SUPPORT_AES_256 "y")
set(CONFIG_SOC_ADC_RTC_CTRL_SUPPORTED "y")
set(CONFIG_SOC_ADC_DIG_CTRL_SUPPORTED "y")
set(CONFIG_SOC_ADC_DMA_SUPPORTED "y")
set(CONFIG_SOC_ADC_PERIPH_NUM "2")
set(CONFIG_SOC_ADC_MAX_CHANNEL_NUM "8")
set(CONFIG_SOC_ADC_ATTEN_NUM "4")
set(CONFIG_SOC_ADC_DIGI_CONTROLLER_NUM "2")
set(CONFIG_SOC_ADC_PATT_LEN_MAX "16")
set(CONFIG_SOC_ADC_DIGI_MAX_BITWIDTH "12")
set(CONFIG_SOC_ADC_DIGI_MIN_BITWIDTH "12")
set(CONFIG_SOC_ADC_DIGI_IIR_FILTER_NUM "2")
set(CONFIG_SOC_ADC_DIGI_MONITOR_NUM "2")
set(CONFIG_SOC_ADC_DIGI_RESULT_BYTES "4")
set(CONFIG_SOC_ADC_DIGI_DATA_BYTES_PER_CONV "4")
set(CONFIG_SOC_ADC_SAMPLE_FREQ_THRES_HIGH "83333")
set(CONFIG_SOC_ADC_SAMPLE_FREQ_THRES_LOW "611")
set(CONFIG_SOC_ADC_RTC_MIN_BITWIDTH "12")
set(CONFIG_SOC_ADC_RTC_MAX_BITWIDTH "12")
set(CONFIG_SOC_ADC_CALIBRATION_V1_SUPPORTED "y")
set(CONFIG_SOC_ADC_SELF_HW_CALI_SUPPORTED "y")
set(CONFIG_SOC_ADC_CALIB_CHAN_COMPENS_SUPPORTED "y")
set(CONFIG_SOC_ADC_SHARED_POWER "y")
set(CONFIG_SOC_BROWNOUT_RESET_SUPPORTED "y")
set(CONFIG_SOC_SHARED_IDCACHE_SUPPORTED "y")
set(CONFIG_SOC_CACHE_WRITEBACK_SUPPORTED "y")
set(CONFIG_SOC_CACHE_FREEZE_SUPPORTED "y")
set(CONFIG_SOC_CACHE_INTERNAL_MEM_VIA_L1CACHE "y")
set(CONFIG_SOC_CPU_CORES_NUM "2")
set(CONFIG_SOC_CPU_INTR_NUM "32")
set(CONFIG_SOC_CPU_HAS_FLEXIBLE_INTC "y")
set(CONFIG_SOC_INT_CLIC_SUPPORTED "y")
set(CONFIG_SOC_INT_HW_NESTED_SUPPORTED "y")
set(CONFIG_SOC_BRANCH_PREDICTOR_SUPPORTED "y")
set(CONFIG_SOC_CPU_COPROC_NUM "3")
set(CONFIG_SOC_CPU_HAS_FPU "y")
set(CONFIG_SOC_CPU_HAS_FPU_EXT_ILL_BUG "y")
set(CONFIG_SOC_CPU_HAS_HWLOOP "y")
set(CONFIG_SOC_CPU_HAS_HWLOOP_STATE_BUG "y")
set(CONFIG_SOC_CPU_HAS_PIE "y")
set(CONFIG_SOC_HP_CPU_HAS_MULTIPLE_CORES "y")
set(CONFIG_SOC_CPU_BREAKPOINTS_NUM "3")
set(CONFIG_SOC_CPU_WATCHPOINTS_NUM "3")
set(CONFIG_SOC_CPU_WATCHPOINT_MAX_REGION_SIZE "0x100")
set(CONFIG_SOC_CPU_HAS_PMA "y")
set(CONFIG_SOC_CPU_IDRAM_SPLIT_USING_PMP "y")
set(CONFIG_SOC_CPU_PMP_REGION_GRANULARITY "128")
set(CONFIG_SOC_CPU_HAS_LOCKUP_RESET "y")
set(CONFIG_SOC_SIMD_PREFERRED_DATA_ALIGNMENT "16")
set(CONFIG_SOC_DS_SIGNATURE_MAX_BIT_LEN "4096")
set(CONFIG_SOC_DS_KEY_PARAM_MD_IV_LENGTH "16")
set(CONFIG_SOC_DS_KEY_CHECK_MAX_WAIT_US "1100")
set(CONFIG_SOC_DMA_CAN_ACCESS_FLASH "y")
set(CONFIG_SOC_AHB_GDMA_VERSION "2")
set(CONFIG_SOC_GDMA_SUPPORT_CRC "y")
set(CONFIG_SOC_GDMA_NUM_GROUPS_MAX "2")
set(CONFIG_SOC_GDMA_PAIRS_PER_GROUP_MAX "3")
set(CONFIG_SOC_AXI_GDMA_SUPPORT_PSRAM "y")
set(CONFIG_SOC_GDMA_SUPPORT_ETM "y")
set(CONFIG_SOC_GDMA_SUPPORT_SLEEP_RETENTION "y")
set(CONFIG_SOC_AXI_DMA_EXT_MEM_ENC_ALIGNMENT "16")
set(CONFIG_SOC_DMA2D_GROUPS "1")
set(CONFIG_SOC_DMA2D_TX_CHANNELS_PER_GROUP "3")
set(CONFIG_SOC_DMA2D_RX_CHANNELS_PER_GROUP "2")
set(CONFIG_SOC_ETM_GROUPS "1")
set(CONFIG_SOC_ETM_CHANNELS_PER_GROUP "50")
set(CONFIG_SOC_ETM_SUPPORT_SLEEP_RETENTION "y")
set(CONFIG_SOC_GPIO_PORT "1")
set(CONFIG_SOC_GPIO_PIN_COUNT "55")
set(CONFIG_SOC_GPIO_SUPPORT_PIN_GLITCH_FILTER "y")
set(CONFIG_SOC_GPIO_FLEX_GLITCH_FILTER_NUM "8")
set(CONFIG_SOC_GPIO_SUPPORT_PIN_HYS_FILTER "y")
set(CONFIG_SOC_GPIO_SUPPORT_ETM "y")
set(CONFIG_SOC_GPIO_SUPPORT_RTC_INDEPENDENT "y")
set(CONFIG_SOC_GPIO_SUPPORT_DEEPSLEEP_WAKEUP "y")
set(CONFIG_SOC_LP_IO_HAS_INDEPENDENT_WAKEUP_SOURCE "y")
set(CONFIG_SOC_LP_IO_CLOCK_IS_INDEPENDENT "y")
set(CONFIG_SOC_GPIO_VALID_GPIO_MASK "0x7fffffffffffff")
set(CONFIG_SOC_GPIO_IN_RANGE_MAX "54")
set(CONFIG_SOC_GPIO_OUT_RANGE_MAX "54")
set(CONFIG_SOC_GPIO_DEEP_SLEEP_WAKE_VALID_GPIO_MASK "0")
set(CONFIG_SOC_GPIO_DEEP_SLEEP_WAKE_SUPPORTED_PIN_CNT "16")
set(CONFIG_SOC_GPIO_VALID_DIGITAL_IO_PAD_MASK "0x7fffffffff0000")
set(CONFIG_SOC_GPIO_CLOCKOUT_BY_GPIO_MATRIX "y")
set(CONFIG_SOC_GPIO_CLOCKOUT_CHANNEL_NUM "2")
set(CONFIG_SOC_CLOCKOUT_SUPPORT_CHANNEL_DIVIDER "y")
set(CONFIG_SOC_DEBUG_PROBE_NUM_UNIT "1")
set(CONFIG_SOC_DEBUG_PROBE_MAX_OUTPUT_WIDTH "16")
set(CONFIG_SOC_GPIO_SUPPORT_FORCE_HOLD "y")
set(CONFIG_SOC_RTCIO_PIN_COUNT "16")
set(CONFIG_SOC_RTCIO_INPUT_OUTPUT_SUPPORTED "y")
set(CONFIG_SOC_RTCIO_HOLD_SUPPORTED "y")
set(CONFIG_SOC_RTCIO_WAKE_SUPPORTED "y")
set(CONFIG_SOC_RTCIO_EDGE_WAKE_SUPPORTED "y")
set(CONFIG_SOC_DEDIC_GPIO_OUT_CHANNELS_NUM "8")
set(CONFIG_SOC_DEDIC_GPIO_IN_CHANNELS_NUM "8")
set(CONFIG_SOC_DEDIC_PERIPH_ALWAYS_ENABLE "y")
set(CONFIG_SOC_ANA_CMPR_NUM "2")
set(CONFIG_SOC_ANA_CMPR_CAN_DISTINGUISH_EDGE "y")
set(CONFIG_SOC_ANA_CMPR_SUPPORT_ETM "y")
set(CONFIG_SOC_I2C_NUM "3")
set(CONFIG_SOC_HP_I2C_NUM "2")
set(CONFIG_SOC_I2C_FIFO_LEN "32")
set(CONFIG_SOC_I2C_CMD_REG_NUM "8")
set(CONFIG_SOC_I2C_SUPPORT_SLAVE "y")
set(CONFIG_SOC_I2C_SUPPORT_HW_FSM_RST "y")
set(CONFIG_SOC_I2C_SUPPORT_HW_CLR_BUS "y")
set(CONFIG_SOC_I2C_SUPPORT_XTAL "y")
set(CONFIG_SOC_I2C_SUPPORT_RTC "y")
set(CONFIG_SOC_I2C_SUPPORT_10BIT_ADDR "y")
set(CONFIG_SOC_I2C_SLAVE_SUPPORT_BROADCAST "y")
set(CONFIG_SOC_I2C_SLAVE_CAN_GET_STRETCH_CAUSE "y")
set(CONFIG_SOC_I2C_SLAVE_SUPPORT_I2CRAM_ACCESS "y")
set(CONFIG_SOC_I2C_SLAVE_SUPPORT_SLAVE_UNMATCH "y")
set(CONFIG_SOC_I2C_SUPPORT_SLEEP_RETENTION "y")
set(CONFIG_SOC_LP_I2C_NUM "1")
set(CONFIG_SOC_LP_I2C_FIFO_LEN "16")
set(CONFIG_SOC_I2S_NUM "3")
set(CONFIG_SOC_I2S_HW_VERSION_2 "y")
set(CONFIG_SOC_I2S_SUPPORTS_ETM "y")
set(CONFIG_SOC_I2S_SUPPORTS_XTAL "y")
set(CONFIG_SOC_I2S_SUPPORTS_APLL "y")
set(CONFIG_SOC_I2S_SUPPORTS_PCM "y")
set(CONFIG_SOC_I2S_SUPPORTS_PDM "y")
set(CONFIG_SOC_I2S_SUPPORTS_PDM_TX "y")
set(CONFIG_SOC_I2S_SUPPORTS_PCM2PDM "y")
set(CONFIG_SOC_I2S_SUPPORTS_PDM_RX "y")
set(CONFIG_SOC_I2S_SUPPORTS_PDM2PCM "y")
set(CONFIG_SOC_I2S_SUPPORTS_PDM_RX_HP_FILTER "y")
set(CONFIG_SOC_I2S_SUPPORTS_TX_SYNC_CNT "y")
set(CONFIG_SOC_I2S_SUPPORTS_TDM "y")
set(CONFIG_SOC_I2S_PDM_MAX_TX_LINES "2")
set(CONFIG_SOC_I2S_PDM_MAX_RX_LINES "4")
set(CONFIG_SOC_I2S_TDM_FULL_DATA_WIDTH "y")
set(CONFIG_SOC_I2S_SUPPORT_SLEEP_RETENTION "y")
set(CONFIG_SOC_LP_I2S_NUM "1")
set(CONFIG_SOC_ISP_BF_SUPPORTED "y")
set(CONFIG_SOC_ISP_CCM_SUPPORTED "y")
set(CONFIG_SOC_ISP_DEMOSAIC_SUPPORTED "y")
set(CONFIG_SOC_ISP_DVP_SUPPORTED "y")
set(CONFIG_SOC_ISP_SHARPEN_SUPPORTED "y")
set(CONFIG_SOC_ISP_COLOR_SUPPORTED "y")
set(CONFIG_SOC_ISP_LSC_SUPPORTED "y")
set(CONFIG_SOC_ISP_SHARE_CSI_BRG "y")
set(CONFIG_SOC_ISP_NUMS "1")
set(CONFIG_SOC_ISP_DVP_CTLR_NUMS "1")
set(CONFIG_SOC_ISP_AE_CTLR_NUMS "1")
set(CONFIG_SOC_ISP_AE_BLOCK_X_NUMS "5")
set(CONFIG_SOC_ISP_AE_BLOCK_Y_NUMS "5")
set(CONFIG_SOC_ISP_AF_CTLR_NUMS "1")
set(CONFIG_SOC_ISP_AF_WINDOW_NUMS "3")
set(CONFIG_SOC_ISP_BF_TEMPLATE_X_NUMS "3")
set(CONFIG_SOC_ISP_BF_TEMPLATE_Y_NUMS "3")
set(CONFIG_SOC_ISP_CCM_DIMENSION "3")
set(CONFIG_SOC_ISP_DEMOSAIC_GRAD_RATIO_INT_BITS "2")
set(CONFIG_SOC_ISP_DEMOSAIC_GRAD_RATIO_DEC_BITS "4")
set(CONFIG_SOC_ISP_DEMOSAIC_GRAD_RATIO_RES_BITS "26")
set(CONFIG_SOC_ISP_DVP_DATA_WIDTH_MAX "16")
set(CONFIG_SOC_ISP_SHARPEN_TEMPLATE_X_NUMS "3")
set(CONFIG_SOC_ISP_SHARPEN_TEMPLATE_Y_NUMS "3")
set(CONFIG_SOC_ISP_SHARPEN_H_FREQ_COEF_INT_BITS "3")
set(CONFIG_SOC_ISP_SHARPEN_H_FREQ_COEF_DEC_BITS "5")
set(CONFIG_SOC_ISP_SHARPEN_H_FREQ_COEF_RES_BITS "24")
set(CONFIG_SOC_ISP_SHARPEN_M_FREQ_COEF_INT_BITS "3")
set(CONFIG_SOC_ISP_SHARPEN_M_FREQ_COEF_DEC_BITS "5")
set(CONFIG_SOC_ISP_SHARPEN_M_FREQ_COEF_RES_BITS "24")
set(CONFIG_SOC_ISP_HIST_CTLR_NUMS "1")
set(CONFIG_SOC_ISP_HIST_BLOCK_X_NUMS "5")
set(CONFIG_SOC_ISP_HIST_BLOCK_Y_NUMS "5")
set(CONFIG_SOC_ISP_HIST_SEGMENT_NUMS "16")
set(CONFIG_SOC_ISP_HIST_INTERVAL_NUMS "15")
set(CONFIG_SOC_ISP_LSC_GRAD_RATIO_INT_BITS "2")
set(CONFIG_SOC_ISP_LSC_GRAD_RATIO_DEC_BITS "8")
set(CONFIG_SOC_ISP_LSC_GRAD_RATIO_RES_BITS "22")
set(CONFIG_SOC_LEDC_SUPPORT_PLL_DIV_CLOCK "y")
set(CONFIG_SOC_LEDC_SUPPORT_XTAL_CLOCK "y")
set(CONFIG_SOC_LEDC_TIMER_NUM "4")
set(CONFIG_SOC_LEDC_CHANNEL_NUM "8")
set(CONFIG_SOC_LEDC_TIMER_BIT_WIDTH "20")
set(CONFIG_SOC_LEDC_GAMMA_CURVE_FADE_SUPPORTED "y")
set(CONFIG_SOC_LEDC_GAMMA_CURVE_FADE_RANGE_MAX "16")
set(CONFIG_SOC_LEDC_SUPPORT_FADE_STOP "y")
set(CONFIG_SOC_LEDC_FADE_PARAMS_BIT_WIDTH "10")
set(CONFIG_SOC_LEDC_SUPPORT_SLEEP_RETENTION "y")
set(CONFIG_SOC_MMU_PERIPH_NUM "2")
set(CONFIG_SOC_MMU_LINEAR_ADDRESS_REGION_NUM "2")
set(CONFIG_SOC_MMU_DI_VADDR_SHARED "y")
set(CONFIG_SOC_MMU_PER_EXT_MEM_TARGET "y")
set(CONFIG_SOC_MPU_MIN_REGION_SIZE "0x20000000")
set(CONFIG_SOC_MPU_REGIONS_MAX_NUM "8")
set(CONFIG_SOC_PCNT_GROUPS "1")
set(CONFIG_SOC_PCNT_UNITS_PER_GROUP "4")
set(CONFIG_SOC_PCNT_CHANNELS_PER_UNIT "2")
set(CONFIG_SOC_PCNT_THRES_POINT_PER_UNIT "2")
set(CONFIG_SOC_PCNT_SUPPORT_RUNTIME_THRES_UPDATE "y")
set(CONFIG_SOC_PCNT_SUPPORT_CLEAR_SIGNAL "y")
set(CONFIG_SOC_PCNT_SUPPORT_SLEEP_RETENTION "y")
set(CONFIG_SOC_RMT_GROUPS "1")
set(CONFIG_SOC_RMT_TX_CANDIDATES_PER_GROUP "4")
set(CONFIG_SOC_RMT_RX_CANDIDATES_PER_GROUP "4")
set(CONFIG_SOC_RMT_CHANNELS_PER_GROUP "8")
set(CONFIG_SOC_RMT_MEM_WORDS_PER_CHANNEL "48")
set(CONFIG_SOC_RMT_SUPPORT_RX_PINGPONG "y")
set(CONFIG_SOC_RMT_SUPPORT_RX_DEMODULATION "y")
set(CONFIG_SOC_RMT_SUPPORT_TX_ASYNC_STOP "y")
set(CONFIG_SOC_RMT_SUPPORT_TX_LOOP_COUNT "y")
set(CONFIG_SOC_RMT_SUPPORT_TX_LOOP_AUTO_STOP "y")
set(CONFIG_SOC_RMT_SUPPORT_TX_SYNCHRO "y")
set(CONFIG_SOC_RMT_SUPPORT_TX_CARRIER_DATA_ONLY "y")
set(CONFIG_SOC_RMT_SUPPORT_XTAL "y")
set(CONFIG_SOC_RMT_SUPPORT_RC_FAST "y")
set(CONFIG_SOC_RMT_SUPPORT_DMA "y")
set(CONFIG_SOC_RMT_SUPPORT_SLEEP_RETENTION "y")
set(CONFIG_SOC_LCD_I80_SUPPORTED "y")
set(CONFIG_SOC_LCD_RGB_SUPPORTED "y")
set(CONFIG_SOC_LCDCAM_I80_NUM_BUSES "1")
set(CONFIG_SOC_LCDCAM_I80_BUS_WIDTH "24")
set(CONFIG_SOC_LCDCAM_RGB_NUM_PANELS "1")
set(CONFIG_SOC_LCDCAM_RGB_DATA_WIDTH "24")
set(CONFIG_SOC_LCD_SUPPORT_RGB_YUV_CONV "y")
set(CONFIG_SOC_MCPWM_GROUPS "2")
set(CONFIG_SOC_MCPWM_TIMERS_PER_GROUP "3")
set(CONFIG_SOC_MCPWM_OPERATORS_PER_GROUP "3")
set(CONFIG_SOC_MCPWM_COMPARATORS_PER_OPERATOR "2")
set(CONFIG_SOC_MCPWM_EVENT_COMPARATORS_PER_OPERATOR "2")
set(CONFIG_SOC_MCPWM_GENERATORS_PER_OPERATOR "2")
set(CONFIG_SOC_MCPWM_TRIGGERS_PER_OPERATOR "2")
set(CONFIG_SOC_MCPWM_GPIO_FAULTS_PER_GROUP "3")
set(CONFIG_SOC_MCPWM_CAPTURE_TIMERS_PER_GROUP "y")
set(CONFIG_SOC_MCPWM_CAPTURE_CHANNELS_PER_TIMER "3")
set(CONFIG_SOC_MCPWM_GPIO_SYNCHROS_PER_GROUP "3")
set(CONFIG_SOC_MCPWM_SWSYNC_CAN_PROPAGATE "y")
set(CONFIG_SOC_MCPWM_SUPPORT_ETM "y")
set(CONFIG_SOC_MCPWM_SUPPORT_EVENT_COMPARATOR "y")
set(CONFIG_SOC_MCPWM_CAPTURE_CLK_FROM_GROUP "y")
set(CONFIG_SOC_MCPWM_SUPPORT_SLEEP_RETENTION "y")
set(CONFIG_SOC_USB_OTG_PERIPH_NUM "2")
set(CONFIG_SOC_USB_UTMI_PHY_NUM "1")
set(CONFIG_SOC_USB_UTMI_PHY_NO_POWER_OFF_ISO "y")
set(CONFIG_SOC_PARLIO_GROUPS "1")
set(CONFIG_SOC_PARLIO_TX_UNITS_PER_GROUP "1")
set(CONFIG_SOC_PARLIO_RX_UNITS_PER_GROUP "1")
set(CONFIG_SOC_PARLIO_TX_UNIT_MAX_DATA_WIDTH "16")
set(CONFIG_SOC_PARLIO_RX_UNIT_MAX_DATA_WIDTH "16")
set(CONFIG_SOC_PARLIO_TX_CLK_SUPPORT_GATING "y")
set(CONFIG_SOC_PARLIO_RX_CLK_SUPPORT_GATING "y")
set(CONFIG_SOC_PARLIO_RX_CLK_SUPPORT_OUTPUT "y")
set(CONFIG_SOC_PARLIO_TRANS_BIT_ALIGN "y")
set(CONFIG_SOC_PARLIO_TX_SUPPORT_LOOP_TRANSMISSION "y")
set(CONFIG_SOC_PARLIO_SUPPORT_SLEEP_RETENTION "y")
set(CONFIG_SOC_PARLIO_SUPPORT_SPI_LCD "y")
set(CONFIG_SOC_PARLIO_SUPPORT_I80_LCD "y")
set(CONFIG_SOC_MPI_MEM_BLOCKS_NUM "4")
set(CONFIG_SOC_MPI_OPERATIONS_NUM "3")
set(CONFIG_SOC_RSA_MAX_BIT_LEN "4096")
set(CONFIG_SOC_SDMMC_USE_IOMUX "y")
set(CONFIG_SOC_SDMMC_USE_GPIO_MATRIX "y")
set(CONFIG_SOC_SDMMC_NUM_SLOTS "2")
set(CONFIG_SOC_SDMMC_DELAY_PHASE_NUM "4")
set(CONFIG_SOC_SDMMC_IO_POWER_EXTERNAL "y")
set(CONFIG_SOC_SDMMC_PSRAM_DMA_CAPABLE "y")
set(CONFIG_SOC_SDMMC_UHS_I_SUPPORTED "y")
set(CONFIG_SOC_SHA_DMA_MAX_BUFFER_SIZE "3968")
set(CONFIG_SOC_SHA_SUPPORT_DMA "y")
set(CONFIG_SOC_SHA_SUPPORT_RESUME "y")
set(CONFIG_SOC_SHA_GDMA "y")
set(CONFIG_SOC_SHA_SUPPORT_SHA1 "y")
set(CONFIG_SOC_SHA_SUPPORT_SHA224 "y")
set(CONFIG_SOC_SHA_SUPPORT_SHA256 "y")
set(CONFIG_SOC_SHA_SUPPORT_SHA384 "y")
set(CONFIG_SOC_SHA_SUPPORT_SHA512 "y")
set(CONFIG_SOC_SHA_SUPPORT_SHA512_224 "y")
set(CONFIG_SOC_SHA_SUPPORT_SHA512_256 "y")
set(CONFIG_SOC_SHA_SUPPORT_SHA512_T "y")
set(CONFIG_SOC_ECDSA_SUPPORT_EXPORT_PUBKEY "y")
set(CONFIG_SOC_ECDSA_SUPPORT_DETERMINISTIC_MODE "y")
set(CONFIG_SOC_ECDSA_USES_MPI "y")
set(CONFIG_SOC_SDM_GROUPS "1")
set(CONFIG_SOC_SDM_CHANNELS_PER_GROUP "8")
set(CONFIG_SOC_SDM_CLK_SUPPORT_PLL_F80M "y")
set(CONFIG_SOC_SDM_CLK_SUPPORT_XTAL "y")
set(CONFIG_SOC_SPI_PERIPH_NUM "3")
set(CONFIG_SOC_SPI_MAX_CS_NUM "6")
set(CONFIG_SOC_SPI_MAXIMUM_BUFFER_SIZE "64")
set(CONFIG_SOC_SPI_SUPPORT_SLEEP_RETENTION "y")
set(CONFIG_SOC_SPI_SUPPORT_SLAVE_HD_VER2 "y")
set(CONFIG_SOC_SPI_SLAVE_SUPPORT_SEG_TRANS "y")
set(CONFIG_SOC_SPI_SUPPORT_DDRCLK "y")
set(CONFIG_SOC_SPI_SUPPORT_CD_SIG "y")
set(CONFIG_SOC_SPI_SUPPORT_OCT "y")
set(CONFIG_SOC_SPI_SUPPORT_CLK_XTAL "y")
set(CONFIG_SOC_SPI_SUPPORT_CLK_RC_FAST "y")
set(CONFIG_SOC_SPI_SUPPORT_CLK_SPLL "y")
set(CONFIG_SOC_MSPI_HAS_INDEPENT_IOMUX "y")
set(CONFIG_SOC_MEMSPI_IS_INDEPENDENT "y")
set(CONFIG_SOC_SPI_MAX_PRE_DIVIDER "16")
set(CONFIG_SOC_LP_SPI_PERIPH_NUM "y")
set(CONFIG_SOC_LP_SPI_MAXIMUM_BUFFER_SIZE "64")
set(CONFIG_SOC_SPIRAM_XIP_SUPPORTED "y")
set(CONFIG_SOC_SPI_MEM_SUPPORT_AUTO_WAIT_IDLE "y")
set(CONFIG_SOC_SPI_MEM_SUPPORT_AUTO_SUSPEND "y")
set(CONFIG_SOC_SPI_MEM_SUPPORT_AUTO_RESUME "y")
set(CONFIG_SOC_SPI_MEM_SUPPORT_IDLE_INTR "y")
set(CONFIG_SOC_SPI_MEM_SUPPORT_SW_SUSPEND "y")
set(CONFIG_SOC_SPI_MEM_SUPPORT_CHECK_SUS "y")
set(CONFIG_SOC_SPI_MEM_SUPPORT_TIMING_TUNING "y")
set(CONFIG_SOC_MEMSPI_TIMING_TUNING_BY_DQS "y")
set(CONFIG_SOC_MEMSPI_TIMING_TUNING_BY_FLASH_DELAY "y")
set(CONFIG_SOC_SPI_MEM_SUPPORT_CACHE_32BIT_ADDR_MAP "y")
set(CONFIG_SOC_SPI_PERIPH_SUPPORT_CONTROL_DUMMY_OUT "y")
set(CONFIG_SOC_MEMSPI_SRC_FREQ_80M_SUPPORTED "y")
set(CONFIG_SOC_MEMSPI_SRC_FREQ_40M_SUPPORTED "y")
set(CONFIG_SOC_MEMSPI_SRC_FREQ_20M_SUPPORTED "y")
set(CONFIG_SOC_MEMSPI_SRC_FREQ_120M_SUPPORTED "y")
set(CONFIG_SOC_MEMSPI_FLASH_PSRAM_INDEPENDENT "y")
set(CONFIG_SOC_SYSTIMER_COUNTER_NUM "2")
set(CONFIG_SOC_SYSTIMER_ALARM_NUM "3")
set(CONFIG_SOC_SYSTIMER_BIT_WIDTH_LO "32")
set(CONFIG_SOC_SYSTIMER_BIT_WIDTH_HI "20")
set(CONFIG_SOC_SYSTIMER_FIXED_DIVIDER "y")
set(CONFIG_SOC_SYSTIMER_SUPPORT_RC_FAST "y")
set(CONFIG_SOC_SYSTIMER_INT_LEVEL "y")
set(CONFIG_SOC_SYSTIMER_ALARM_MISS_COMPENSATE "y")
set(CONFIG_SOC_SYSTIMER_SUPPORT_ETM "y")
set(CONFIG_SOC_LP_TIMER_BIT_WIDTH_LO "32")
set(CONFIG_SOC_LP_TIMER_BIT_WIDTH_HI "16")
set(CONFIG_SOC_TIMER_GROUPS "2")
set(CONFIG_SOC_TIMER_GROUP_TIMERS_PER_GROUP "2")
set(CONFIG_SOC_TIMER_GROUP_COUNTER_BIT_WIDTH "54")
set(CONFIG_SOC_TIMER_GROUP_SUPPORT_XTAL "y")
set(CONFIG_SOC_TIMER_GROUP_SUPPORT_RC_FAST "y")
set(CONFIG_SOC_TIMER_GROUP_TOTAL_TIMERS "4")
set(CONFIG_SOC_TIMER_SUPPORT_ETM "y")
set(CONFIG_SOC_TIMER_SUPPORT_SLEEP_RETENTION "y")
set(CONFIG_SOC_MWDT_SUPPORT_XTAL "y")
set(CONFIG_SOC_MWDT_SUPPORT_SLEEP_RETENTION "y")
set(CONFIG_SOC_TOUCH_SENSOR_VERSION "3")
set(CONFIG_SOC_TOUCH_SENSOR_NUM "14")
set(CONFIG_SOC_TOUCH_MIN_CHAN_ID "1")
set(CONFIG_SOC_TOUCH_MAX_CHAN_ID "14")
set(CONFIG_SOC_TOUCH_SUPPORT_SLEEP_WAKEUP "y")
set(CONFIG_SOC_TOUCH_SUPPORT_BENCHMARK "y")
set(CONFIG_SOC_TOUCH_SUPPORT_WATERPROOF "y")
set(CONFIG_SOC_TOUCH_SUPPORT_PROX_SENSING "y")
set(CONFIG_SOC_TOUCH_PROXIMITY_CHANNEL_NUM "3")
set(CONFIG_SOC_TOUCH_PROXIMITY_MEAS_DONE_SUPPORTED "y")
set(CONFIG_SOC_TOUCH_SUPPORT_FREQ_HOP "y")
set(CONFIG_SOC_TOUCH_SAMPLE_CFG_NUM "3")
set(CONFIG_SOC_TWAI_CONTROLLER_NUM "3")
set(CONFIG_SOC_TWAI_MASK_FILTER_NUM "1")
set(CONFIG_SOC_TWAI_CLK_SUPPORT_XTAL "y")
set(CONFIG_SOC_TWAI_BRP_MIN "2")
set(CONFIG_SOC_TWAI_BRP_MAX "32768")
set(CONFIG_SOC_TWAI_SUPPORTS_RX_STATUS "y")
set(CONFIG_SOC_TWAI_SUPPORT_SLEEP_RETENTION "y")
set(CONFIG_SOC_EFUSE_DIS_PAD_JTAG "y")
set(CONFIG_SOC_EFUSE_DIS_USB_JTAG "y")
set(CONFIG_SOC_EFUSE_DIS_DIRECT_BOOT "y")
set(CONFIG_SOC_EFUSE_SOFT_DIS_JTAG "y")
set(CONFIG_SOC_EFUSE_DIS_DOWNLOAD_MSPI "y")
set(CONFIG_SOC_EFUSE_ECDSA_KEY "y")
set(CONFIG_SOC_KEY_MANAGER_ECDSA_KEY_DEPLOY "y")
set(CONFIG_SOC_KEY_MANAGER_FE_KEY_DEPLOY "y")
set(CONFIG_SOC_SECURE_BOOT_V2_RSA "y")
set(CONFIG_SOC_SECURE_BOOT_V2_ECC "y")
set(CONFIG_SOC_EFUSE_SECURE_BOOT_KEY_DIGESTS "3")
set(CONFIG_SOC_EFUSE_REVOKE_BOOT_KEY_DIGESTS "y")
set(CONFIG_SOC_SUPPORT_SECURE_BOOT_REVOKE_KEY "y")
set(CONFIG_SOC_FLASH_ENCRYPTED_XTS_AES_BLOCK_MAX "64")
set(CONFIG_SOC_FLASH_ENCRYPTION_XTS_AES "y")
set(CONFIG_SOC_FLASH_ENCRYPTION_XTS_AES_OPTIONS "y")
set(CONFIG_SOC_FLASH_ENCRYPTION_XTS_AES_128 "y")
set(CONFIG_SOC_FLASH_ENCRYPTION_XTS_AES_256 "y")
set(CONFIG_SOC_UART_NUM "6")
set(CONFIG_SOC_UART_HP_NUM "5")
set(CONFIG_SOC_UART_LP_NUM "1")
set(CONFIG_SOC_UART_FIFO_LEN "128")
set(CONFIG_SOC_LP_UART_FIFO_LEN "16")
set(CONFIG_SOC_UART_BITRATE_MAX "5000000")
set(CONFIG_SOC_UART_SUPPORT_PLL_F80M_CLK "y")
set(CONFIG_SOC_UART_SUPPORT_RTC_CLK "y")
set(CONFIG_SOC_UART_SUPPORT_XTAL_CLK "y")
set(CONFIG_SOC_UART_SUPPORT_WAKEUP_INT "y")
set(CONFIG_SOC_UART_HAS_LP_UART "y")
set(CONFIG_SOC_UART_SUPPORT_SLEEP_RETENTION "y")
set(CONFIG_SOC_UART_SUPPORT_FSM_TX_WAIT_SEND "y")
set(CONFIG_SOC_UART_WAKEUP_CHARS_SEQ_MAX_LEN "5")
set(CONFIG_SOC_UART_WAKEUP_SUPPORT_ACTIVE_THRESH_MODE "y")
set(CONFIG_SOC_UART_WAKEUP_SUPPORT_FIFO_THRESH_MODE "y")
set(CONFIG_SOC_UART_WAKEUP_SUPPORT_START_BIT_MODE "y")
set(CONFIG_SOC_UART_WAKEUP_SUPPORT_CHAR_SEQ_MODE "y")
set(CONFIG_SOC_LP_I2S_SUPPORT_VAD "y")
set(CONFIG_SOC_UHCI_NUM "1")
set(CONFIG_SOC_COEX_HW_PTI "y")
set(CONFIG_SOC_PHY_DIG_REGS_MEM_SIZE "21")
set(CONFIG_SOC_WIFI_LIGHT_SLEEP_CLK_WIDTH "12")
set(CONFIG_SOC_PM_SUPPORT_EXT1_WAKEUP "y")
set(CONFIG_SOC_PM_SUPPORT_EXT1_WAKEUP_MODE_PER_PIN "y")
set(CONFIG_SOC_PM_EXT1_WAKEUP_BY_PMU "y")
set(CONFIG_SOC_PM_SUPPORT_WIFI_WAKEUP "y")
set(CONFIG_SOC_PM_SUPPORT_TOUCH_SENSOR_WAKEUP "y")
set(CONFIG_SOC_PM_SUPPORT_XTAL32K_PD "y")
set(CONFIG_SOC_PM_SUPPORT_RC32K_PD "y")
set(CONFIG_SOC_PM_SUPPORT_RC_FAST_PD "y")
set(CONFIG_SOC_PM_SUPPORT_VDDSDIO_PD "y")
set(CONFIG_SOC_PM_SUPPORT_TOP_PD "y")
set(CONFIG_SOC_PM_SUPPORT_CNNT_PD "y")
set(CONFIG_SOC_PM_SUPPORT_RTC_PERIPH_PD "y")
set(CONFIG_SOC_PM_SUPPORT_DEEPSLEEP_CHECK_STUB_ONLY "y")
set(CONFIG_SOC_PM_CPU_RETENTION_BY_SW "y")
set(CONFIG_SOC_PM_CACHE_RETENTION_BY_PAU "y")
set(CONFIG_SOC_PM_PAU_LINK_NUM "4")
set(CONFIG_SOC_PM_PAU_REGDMA_LINK_MULTI_ADDR "y")
set(CONFIG_SOC_PAU_IN_TOP_DOMAIN "y")
set(CONFIG_SOC_CPU_IN_TOP_DOMAIN "y")
set(CONFIG_SOC_PM_PAU_REGDMA_UPDATE_CACHE_BEFORE_WAIT_COMPARE "y")
set(CONFIG_SOC_SLEEP_SYSTIMER_STALL_WORKAROUND "y")
set(CONFIG_SOC_SLEEP_TGWDT_STOP_WORKAROUND "y")
set(CONFIG_SOC_PM_RETENTION_MODULE_NUM "64")
set(CONFIG_SOC_PSRAM_VDD_POWER_MPLL "y")
set(CONFIG_SOC_CLK_RC_FAST_SUPPORT_CALIBRATION "y")
set(CONFIG_SOC_CLK_APLL_SUPPORTED "y")
set(CONFIG_SOC_CLK_MPLL_SUPPORTED "y")
set(CONFIG_SOC_CLK_SDIO_PLL_SUPPORTED "y")
set(CONFIG_SOC_CLK_XTAL32K_SUPPORTED "y")
set(CONFIG_SOC_CLK_RC32K_SUPPORTED "y")
set(CONFIG_SOC_CLK_LP_FAST_SUPPORT_LP_PLL "y")
set(CONFIG_SOC_CLK_LP_FAST_SUPPORT_XTAL "y")
set(CONFIG_SOC_PERIPH_CLK_CTRL_SHARED "y")
set(CONFIG_SOC_CLK_ANA_I2C_MST_HAS_ROOT_GATE "y")
set(CONFIG_SOC_TEMPERATURE_SENSOR_LP_PLL_SUPPORT "y")
set(CONFIG_SOC_TEMPERATURE_SENSOR_INTR_SUPPORT "y")
set(CONFIG_SOC_TSENS_IS_INDEPENDENT_FROM_ADC "y")
set(CONFIG_SOC_TEMPERATURE_SENSOR_SUPPORT_ETM "y")
set(CONFIG_SOC_TEMPERATURE_SENSOR_SUPPORT_SLEEP_RETENTION "y")
set(CONFIG_SOC_MEM_TCM_SUPPORTED "y")
set(CONFIG_SOC_MEM_NON_CONTIGUOUS_SRAM "y")
set(CONFIG_SOC_ASYNCHRONOUS_BUS_ERROR_MODE "y")
set(CONFIG_SOC_EMAC_IEEE1588V2_SUPPORTED "y")
set(CONFIG_SOC_EMAC_USE_MULTI_IO_MUX "y")
set(CONFIG_SOC_EMAC_MII_USE_GPIO_MATRIX "y")
set(CONFIG_SOC_JPEG_CODEC_SUPPORTED "y")
set(CONFIG_SOC_JPEG_DECODE_SUPPORTED "y")
set(CONFIG_SOC_JPEG_ENCODE_SUPPORTED "y")
set(CONFIG_SOC_LCDCAM_CAM_SUPPORT_RGB_YUV_CONV "y")
set(CONFIG_SOC_LCDCAM_CAM_PERIPH_NUM "1")
set(CONFIG_SOC_LCDCAM_CAM_DATA_WIDTH_MAX "16")
set(CONFIG_SOC_I3C_MASTER_PERIPH_NUM "y")
set(CONFIG_SOC_I3C_MASTER_ADDRESS_TABLE_NUM "12")
set(CONFIG_SOC_I3C_MASTER_COMMAND_TABLE_NUM "12")
set(CONFIG_SOC_LP_CORE_SUPPORT_ETM "y")
set(CONFIG_SOC_LP_CORE_SUPPORT_LP_ADC "y")
set(CONFIG_SOC_LP_CORE_SUPPORT_LP_VAD "y")
set(CONFIG_SOC_LP_CORE_SUPPORT_STORE_LOAD_EXCEPTIONS "y")
set(CONFIG_IDF_CMAKE "y")
set(CONFIG_IDF_TOOLCHAIN "gcc")
set(CONFIG_IDF_TOOLCHAIN_GCC "y")
set(CONFIG_IDF_TARGET_ARCH_RISCV "y")
set(CONFIG_IDF_TARGET_ARCH "riscv")
set(CONFIG_IDF_TARGET "esp32p4")
set(CONFIG_IDF_INIT_VERSION "5.5.0")
set(CONFIG_IDF_TARGET_ESP32P4 "y")
set(CONFIG_IDF_FIRMWARE_CHIP_ID "0x12")
set(CONFIG_APP_BUILD_TYPE_APP_2NDBOOT "y")
set(CONFIG_APP_BUILD_TYPE_RAM "")
set(CONFIG_APP_BUILD_GENERATE_BINARIES "y")
set(CONFIG_APP_BUILD_BOOTLOADER "y")
set(CONFIG_APP_BUILD_USE_FLASH_SECTIONS "y")
set(CONFIG_APP_REPRODUCIBLE_BUILD "")
set(CONFIG_APP_NO_BLOBS "")
set(CONFIG_BOOTLOADER_COMPILE_TIME_DATE "y")
set(CONFIG_BOOTLOADER_PROJECT_VER "1")
set(CONFIG_BOOTLOADER_APP_ROLLBACK_ENABLE "")
set(CONFIG_BOOTLOADER_OFFSET_IN_FLASH "0x2000")
set(CONFIG_BOOTLOADER_COMPILER_OPTIMIZATION_SIZE "y")
set(CONFIG_BOOTLOADER_COMPILER_OPTIMIZATION_DEBUG "")
set(CONFIG_BOOTLOADER_COMPILER_OPTIMIZATION_PERF "")
set(CONFIG_BOOTLOADER_LOG_VERSION_1 "y")
set(CONFIG_BOOTLOADER_LOG_VERSION "1")
set(CONFIG_BOOTLOADER_LOG_LEVEL_NONE "")
set(CONFIG_BOOTLOADER_LOG_LEVEL_ERROR "")
set(CONFIG_BOOTLOADER_LOG_LEVEL_WARN "")
set(CONFIG_BOOTLOADER_LOG_LEVEL_INFO "y")
set(CONFIG_BOOTLOADER_LOG_LEVEL_DEBUG "")
set(CONFIG_BOOTLOADER_LOG_LEVEL_VERBOSE "")
set(CONFIG_BOOTLOADER_LOG_LEVEL "3")
set(CONFIG_BOOTLOADER_LOG_COLORS "")
set(CONFIG_BOOTLOADER_LOG_TIMESTAMP_SOURCE_CPU_TICKS "y")
set(CONFIG_BOOTLOADER_LOG_MODE_TEXT_EN "y")
set(CONFIG_BOOTLOADER_LOG_MODE_TEXT "y")
set(CONFIG_BOOTLOADER_FLASH_DC_AWARE "")
set(CONFIG_BOOTLOADER_FLASH_XMC_SUPPORT "y")
set(CONFIG_BOOTLOADER_FACTORY_RESET "")
set(CONFIG_BOOTLOADER_APP_TEST "")
set(CONFIG_BOOTLOADER_REGION_PROTECTION_ENABLE "y")
set(CONFIG_BOOTLOADER_WDT_ENABLE "y")
set(CONFIG_BOOTLOADER_WDT_DISABLE_IN_USER_CODE "")
set(CONFIG_BOOTLOADER_WDT_TIME_MS "9000")
set(CONFIG_BOOTLOADER_SKIP_VALIDATE_IN_DEEP_SLEEP "")
set(CONFIG_BOOTLOADER_SKIP_VALIDATE_ON_POWER_ON "")
set(CONFIG_BOOTLOADER_SKIP_VALIDATE_ALWAYS "")
set(CONFIG_BOOTLOADER_RESERVE_RTC_SIZE "0x0")
set(CONFIG_BOOTLOADER_CUSTOM_RESERVE_RTC "")
set(CONFIG_SECURE_BOOT_V2_RSA_SUPPORTED "y")
set(CONFIG_SECURE_BOOT_V2_ECC_SUPPORTED "y")
set(CONFIG_SECURE_BOOT_V2_PREFERRED "y")
set(CONFIG_SECURE_SIGNED_APPS_NO_SECURE_BOOT "")
set(CONFIG_SECURE_BOOT "")
set(CONFIG_SECURE_FLASH_ENC_ENABLED "")
set(CONFIG_SECURE_ROM_DL_MODE_ENABLED "y")
set(CONFIG_APP_COMPILE_TIME_DATE "y")
set(CONFIG_APP_EXCLUDE_PROJECT_VER_VAR "")
set(CONFIG_APP_EXCLUDE_PROJECT_NAME_VAR "")
set(CONFIG_APP_PROJECT_VER_FROM_CONFIG "")
set(CONFIG_APP_RETRIEVE_LEN_ELF_SHA "9")
set(CONFIG_ESP_ROM_HAS_CRC_LE "y")
set(CONFIG_ESP_ROM_HAS_CRC_BE "y")
set(CONFIG_ESP_ROM_UART_CLK_IS_XTAL "y")
set(CONFIG_ESP_ROM_USB_SERIAL_DEVICE_NUM "6")
set(CONFIG_ESP_ROM_USB_OTG_NUM "5")
set(CONFIG_ESP_ROM_HAS_RETARGETABLE_LOCKING "y")
set(CONFIG_ESP_ROM_GET_CLK_FREQ "y")
set(CONFIG_ESP_ROM_HAS_RVFPLIB "y")
set(CONFIG_ESP_ROM_HAS_HAL_WDT "y")
set(CONFIG_ESP_ROM_HAS_HAL_SYSTIMER "y")
set(CONFIG_ESP_ROM_HAS_LAYOUT_TABLE "y")
set(CONFIG_ESP_ROM_WDT_INIT_PATCH "y")
set(CONFIG_ESP_ROM_HAS_LP_ROM "y")
set(CONFIG_ESP_ROM_WITHOUT_REGI2C "y")
set(CONFIG_ESP_ROM_HAS_NEWLIB "y")
set(CONFIG_ESP_ROM_HAS_NEWLIB_NANO_FORMAT "y")
set(CONFIG_ESP_ROM_HAS_NEWLIB_NANO_PRINTF_FLOAT_BUG "y")
set(CONFIG_ESP_ROM_HAS_VERSION "y")
set(CONFIG_ESP_ROM_CLIC_INT_TYPE_PATCH "y")
set(CONFIG_ESP_ROM_HAS_OUTPUT_PUTC_FUNC "y")
set(CONFIG_ESP_ROM_HAS_SUBOPTIMAL_NEWLIB_ON_MISALIGNED_MEMORY "y")
set(CONFIG_BOOT_ROM_LOG_ALWAYS_ON "y")
set(CONFIG_BOOT_ROM_LOG_ALWAYS_OFF "")
set(CONFIG_BOOT_ROM_LOG_ON_GPIO_HIGH "")
set(CONFIG_BOOT_ROM_LOG_ON_GPIO_LOW "")
set(CONFIG_ESPTOOLPY_NO_STUB "")
set(CONFIG_ESPTOOLPY_FLASHMODE_QIO "")
set(CONFIG_ESPTOOLPY_FLASHMODE_QOUT "")
set(CONFIG_ESPTOOLPY_FLASHMODE_DIO "y")
set(CONFIG_ESPTOOLPY_FLASHMODE_DOUT "")
set(CONFIG_ESPTOOLPY_FLASH_SAMPLE_MODE_STR "y")
set(CONFIG_ESPTOOLPY_FLASHMODE "dio")
set(CONFIG_ESPTOOLPY_FLASHFREQ_80M "y")
set(CONFIG_ESPTOOLPY_FLASHFREQ_40M "")
set(CONFIG_ESPTOOLPY_FLASHFREQ_20M "")
set(CONFIG_ESPTOOLPY_FLASHFREQ_VAL "80")
set(CONFIG_ESPTOOLPY_FLASHFREQ "80m")
set(CONFIG_ESPTOOLPY_FLASHSIZE_1MB "")
set(CONFIG_ESPTOOLPY_FLASHSIZE_2MB "")
set(CONFIG_ESPTOOLPY_FLASHSIZE_4MB "")
set(CONFIG_ESPTOOLPY_FLASHSIZE_8MB "")
set(CONFIG_ESPTOOLPY_FLASHSIZE_16MB "y")
set(CONFIG_ESPTOOLPY_FLASHSIZE_32MB "")
set(CONFIG_ESPTOOLPY_FLASHSIZE_64MB "")
set(CONFIG_ESPTOOLPY_FLASHSIZE_128MB "")
set(CONFIG_ESPTOOLPY_FLASHSIZE "16MB")
set(CONFIG_ESPTOOLPY_HEADER_FLASHSIZE_UPDATE "")
set(CONFIG_ESPTOOLPY_BEFORE_RESET "y")
set(CONFIG_ESPTOOLPY_BEFORE_NORESET "")
set(CONFIG_ESPTOOLPY_BEFORE "default_reset")
set(CONFIG_ESPTOOLPY_AFTER_RESET "y")
set(CONFIG_ESPTOOLPY_AFTER_NORESET "")
set(CONFIG_ESPTOOLPY_AFTER "hard_reset")
set(CONFIG_ESPTOOLPY_MONITOR_BAUD "115200")
set(CONFIG_PARTITION_TABLE_SINGLE_APP "")
set(CONFIG_PARTITION_TABLE_SINGLE_APP_LARGE "")
set(CONFIG_PARTITION_TABLE_TWO_OTA "")
set(CONFIG_PARTITION_TABLE_TWO_OTA_LARGE "")
set(CONFIG_PARTITION_TABLE_CUSTOM "y")
set(CONFIG_PARTITION_TABLE_CUSTOM_FILENAME "partitions.csv")
set(CONFIG_PARTITION_TABLE_FILENAME "partitions.csv")
set(CONFIG_PARTITION_TABLE_OFFSET "0x8000")
set(CONFIG_PARTITION_TABLE_MD5 "y")
set(CONFIG_COMPILER_OPTIMIZATION_DEBUG "y")
set(CONFIG_COMPILER_OPTIMIZATION_SIZE "")
set(CONFIG_COMPILER_OPTIMIZATION_PERF "")
set(CONFIG_COMPILER_OPTIMIZATION_NONE "")
set(CONFIG_COMPILER_OPTIMIZATION_ASSERTIONS_ENABLE "y")
set(CONFIG_COMPILER_OPTIMIZATION_ASSERTIONS_SILENT "")
set(CONFIG_COMPILER_OPTIMIZATION_ASSERTIONS_DISABLE "")
set(CONFIG_COMPILER_ASSERT_NDEBUG_EVALUATE "y")
set(CONFIG_COMPILER_FLOAT_LIB_FROM_GCCLIB "")
set(CONFIG_COMPILER_FLOAT_LIB_FROM_RVFPLIB "y")
set(CONFIG_COMPILER_OPTIMIZATION_ASSERTION_LEVEL "2")
set(CONFIG_COMPILER_OPTIMIZATION_CHECKS_SILENT "")
set(CONFIG_COMPILER_HIDE_PATHS_MACROS "y")
set(CONFIG_COMPILER_CXX_EXCEPTIONS "")
set(CONFIG_COMPILER_CXX_RTTI "")
set(CONFIG_COMPILER_STACK_CHECK_MODE_NONE "y")
set(CONFIG_COMPILER_STACK_CHECK_MODE_NORM "")
set(CONFIG_COMPILER_STACK_CHECK_MODE_STRONG "")
set(CONFIG_COMPILER_STACK_CHECK_MODE_ALL "")
set(CONFIG_COMPILER_NO_MERGE_CONSTANTS "")
set(CONFIG_COMPILER_WARN_WRITE_STRINGS "")
set(CONFIG_COMPILER_SAVE_RESTORE_LIBCALLS "")
set(CONFIG_COMPILER_DISABLE_DEFAULT_ERRORS "y")
set(CONFIG_COMPILER_DISABLE_GCC12_WARNINGS "")
set(CONFIG_COMPILER_DISABLE_GCC13_WARNINGS "")
set(CONFIG_COMPILER_DISABLE_GCC14_WARNINGS "")
set(CONFIG_COMPILER_DUMP_RTL_FILES "")
set(CONFIG_COMPILER_RT_LIB_GCCLIB "y")
set(CONFIG_COMPILER_RT_LIB_NAME "gcc")
set(CONFIG_COMPILER_ORPHAN_SECTIONS_WARNING "y")
set(CONFIG_COMPILER_ORPHAN_SECTIONS_PLACE "")
set(CONFIG_COMPILER_STATIC_ANALYZER "")
set(CONFIG_EFUSE_CUSTOM_TABLE "")
set(CONFIG_EFUSE_VIRTUAL "")
set(CONFIG_EFUSE_MAX_BLK_LEN "256")
set(CONFIG_ESP_ERR_TO_NAME_LOOKUP "y")
set(CONFIG_ESP32P4_REV_MIN_0 "")
set(CONFIG_ESP32P4_REV_MIN_1 "y")
set(CONFIG_ESP32P4_REV_MIN_100 "")
set(CONFIG_ESP32P4_REV_MIN_FULL "1")
set(CONFIG_ESP_REV_MIN_FULL "1")
set(CONFIG_ESP32P4_REV_MAX_FULL "199")
set(CONFIG_ESP_REV_MAX_FULL "199")
set(CONFIG_ESP_EFUSE_BLOCK_REV_MIN_FULL "0")
set(CONFIG_ESP_EFUSE_BLOCK_REV_MAX_FULL "99")
set(CONFIG_ESP_MAC_ADDR_UNIVERSE_ETH "y")
set(CONFIG_ESP_MAC_UNIVERSAL_MAC_ADDRESSES_ONE "y")
set(CONFIG_ESP_MAC_UNIVERSAL_MAC_ADDRESSES "1")
set(CONFIG_ESP32P4_UNIVERSAL_MAC_ADDRESSES_ONE "y")
set(CONFIG_ESP32P4_UNIVERSAL_MAC_ADDRESSES "1")
set(CONFIG_ESP_MAC_USE_CUSTOM_MAC_AS_BASE_MAC "")
set(CONFIG_ESP_SLEEP_FLASH_LEAKAGE_WORKAROUND "y")
set(CONFIG_ESP_SLEEP_MSPI_NEED_ALL_IO_PU "")
set(CONFIG_ESP_SLEEP_GPIO_RESET_WORKAROUND "")
set(CONFIG_ESP_SLEEP_WAIT_FLASH_READY_EXTRA_DELAY "0")
set(CONFIG_ESP_SLEEP_CACHE_SAFE_ASSERTION "")
set(CONFIG_ESP_SLEEP_DEBUG "")
set(CONFIG_ESP_SLEEP_GPIO_ENABLE_INTERNAL_RESISTORS "y")
set(CONFIG_RTC_CLK_SRC_INT_RC "y")
set(CONFIG_RTC_CLK_SRC_EXT_CRYS "")
set(CONFIG_RTC_CLK_CAL_CYCLES "1024")
set(CONFIG_RTC_FAST_CLK_SRC_RC_FAST "y")
set(CONFIG_RTC_FAST_CLK_SRC_XTAL "")
set(CONFIG_ESP_PERIPH_CTRL_FUNC_IN_IRAM "y")
set(CONFIG_ESP_REGI2C_CTRL_FUNC_IN_IRAM "y")
set(CONFIG_ETM_ENABLE_DEBUG_LOG "")
set(CONFIG_GDMA_CTRL_FUNC_IN_IRAM "y")
set(CONFIG_GDMA_ISR_HANDLER_IN_IRAM "y")
set(CONFIG_GDMA_OBJ_DRAM_SAFE "y")
set(CONFIG_GDMA_ENABLE_DEBUG_LOG "")
set(CONFIG_GDMA_ISR_IRAM_SAFE "")
set(CONFIG_DW_GDMA_ENABLE_DEBUG_LOG "")
set(CONFIG_DMA2D_OPERATION_FUNC_IN_IRAM "")
set(CONFIG_DMA2D_ISR_IRAM_SAFE "")
set(CONFIG_XTAL_FREQ_40 "y")
set(CONFIG_XTAL_FREQ "40")
set(CONFIG_ESP_SLEEP_KEEP_DCDC_ALWAYS_ON "y")
set(CONFIG_ESP_SLEEP_DCM_VSET_VAL_IN_SLEEP "14")
set(CONFIG_ESP_LDO_RESERVE_SPI_NOR_FLASH "y")
set(CONFIG_ESP_LDO_CHAN_SPI_NOR_FLASH_DOMAIN "1")
set(CONFIG_ESP_LDO_VOLTAGE_SPI_NOR_FLASH_3300_MV "y")
set(CONFIG_ESP_LDO_VOLTAGE_SPI_NOR_FLASH_DOMAIN "3300")
set(CONFIG_ESP_LDO_RESERVE_PSRAM "y")
set(CONFIG_ESP_LDO_CHAN_PSRAM_DOMAIN "2")
set(CONFIG_ESP_LDO_VOLTAGE_PSRAM_1900_MV "y")
set(CONFIG_ESP_LDO_VOLTAGE_PSRAM_DOMAIN "1900")
set(CONFIG_ESP_BROWNOUT_DET "y")
set(CONFIG_ESP_BROWNOUT_DET_LVL_SEL_7 "y")
set(CONFIG_ESP_BROWNOUT_DET_LVL_SEL_6 "")
set(CONFIG_ESP_BROWNOUT_DET_LVL_SEL_5 "")
set(CONFIG_ESP_BROWNOUT_DET_LVL "7")
set(CONFIG_ESP_BROWNOUT_USE_INTR "y")
set(CONFIG_ESP_VBAT_INIT_AUTO "")
set(CONFIG_ESP_VBAT_WAKEUP_CHIP_ON_VBAT_BROWNOUT "")
set(CONFIG_ESP_INTR_IN_IRAM "y")
set(CONFIG_ESP_ROM_PRINT_IN_IRAM "y")
set(CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ_360 "y")
set(CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ "360")
set(CONFIG_CACHE_L2_CACHE_128KB "y")
set(CONFIG_CACHE_L2_CACHE_256KB "")
set(CONFIG_CACHE_L2_CACHE_512KB "")
set(CONFIG_CACHE_L2_CACHE_SIZE "0x20000")
set(CONFIG_CACHE_L2_CACHE_LINE_64B "y")
set(CONFIG_CACHE_L2_CACHE_LINE_128B "")
set(CONFIG_CACHE_L2_CACHE_LINE_SIZE "64")
set(CONFIG_CACHE_L1_CACHE_LINE_SIZE "64")
set(CONFIG_ESP_SYSTEM_PANIC_PRINT_HALT "")
set(CONFIG_ESP_SYSTEM_PANIC_PRINT_REBOOT "y")
set(CONFIG_ESP_SYSTEM_PANIC_SILENT_REBOOT "")
set(CONFIG_ESP_SYSTEM_PANIC_REBOOT_DELAY_SECONDS "0")
set(CONFIG_ESP_SYSTEM_RTC_FAST_MEM_AS_HEAP_DEPCHECK "y")
set(CONFIG_ESP_SYSTEM_ALLOW_RTC_FAST_MEM_AS_HEAP "y")
set(CONFIG_ESP_SYSTEM_NO_BACKTRACE "y")
set(CONFIG_ESP_SYSTEM_USE_EH_FRAME "")
set(CONFIG_ESP_SYSTEM_USE_FRAME_POINTER "")
set(CONFIG_ESP_SYSTEM_PMP_IDRAM_SPLIT "y")
set(CONFIG_ESP_SYSTEM_PMP_LP_CORE_RESERVE_MEM_EXECUTABLE "")
set(CONFIG_ESP_SYSTEM_EVENT_QUEUE_SIZE "32")
set(CONFIG_ESP_SYSTEM_EVENT_TASK_STACK_SIZE "2304")
set(CONFIG_ESP_MAIN_TASK_STACK_SIZE "3584")
set(CONFIG_ESP_MAIN_TASK_AFFINITY_CPU0 "y")
set(CONFIG_ESP_MAIN_TASK_AFFINITY_CPU1 "")
set(CONFIG_ESP_MAIN_TASK_AFFINITY_NO_AFFINITY "")
set(CONFIG_ESP_MAIN_TASK_AFFINITY "0x0")
set(CONFIG_ESP_MINIMAL_SHARED_STACK_SIZE "2048")
set(CONFIG_ESP_CONSOLE_UART_DEFAULT "y")
set(CONFIG_ESP_CONSOLE_USB_SERIAL_JTAG "")
set(CONFIG_ESP_CONSOLE_UART_CUSTOM "")
set(CONFIG_ESP_CONSOLE_NONE "")
set(CONFIG_ESP_CONSOLE_SECONDARY_NONE "")
set(CONFIG_ESP_CONSOLE_SECONDARY_USB_SERIAL_JTAG "y")
set(CONFIG_ESP_CONSOLE_USB_SERIAL_JTAG_ENABLED "y")
set(CONFIG_ESP_CONSOLE_UART "y")
set(CONFIG_ESP_CONSOLE_UART_NUM "0")
set(CONFIG_ESP_CONSOLE_ROM_SERIAL_PORT_NUM "0")
set(CONFIG_ESP_CONSOLE_UART_BAUDRATE "115200")
set(CONFIG_ESP_INT_WDT "y")
set(CONFIG_ESP_INT_WDT_TIMEOUT_MS "300")
set(CONFIG_ESP_INT_WDT_CHECK_CPU1 "y")
set(CONFIG_ESP_TASK_WDT_EN "y")
set(CONFIG_ESP_TASK_WDT_INIT "y")
set(CONFIG_ESP_TASK_WDT_PANIC "")
set(CONFIG_ESP_TASK_WDT_TIMEOUT_S "5")
set(CONFIG_ESP_TASK_WDT_CHECK_IDLE_TASK_CPU0 "y")
set(CONFIG_ESP_TASK_WDT_CHECK_IDLE_TASK_CPU1 "y")
set(CONFIG_ESP_PANIC_HANDLER_IRAM "")
set(CONFIG_ESP_DEBUG_STUBS_ENABLE "")
set(CONFIG_ESP_DEBUG_OCDAWARE "y")
set(CONFIG_ESP_SYSTEM_CHECK_INT_LEVEL_4 "y")
set(CONFIG_ESP_SYSTEM_HW_STACK_GUARD "y")
set(CONFIG_ESP_SYSTEM_HW_PC_RECORD "y")
set(CONFIG_ESP_IPC_TASK_STACK_SIZE "1024")
set(CONFIG_ESP_IPC_USES_CALLERS_PRIORITY "y")
set(CONFIG_ESP_IPC_ISR_ENABLE "y")
set(CONFIG_FREERTOS_UNICORE "")
set(CONFIG_FREERTOS_HZ "100")
set(CONFIG_FREERTOS_CHECK_STACKOVERFLOW_NONE "")
set(CONFIG_FREERTOS_CHECK_STACKOVERFLOW_PTRVAL "")
set(CONFIG_FREERTOS_CHECK_STACKOVERFLOW_CANARY "y")
set(CONFIG_FREERTOS_THREAD_LOCAL_STORAGE_POINTERS "1")
set(CONFIG_FREERTOS_IDLE_TASK_STACKSIZE "1536")
set(CONFIG_FREERTOS_USE_IDLE_HOOK "")
set(CONFIG_FREERTOS_USE_TICK_HOOK "")
set(CONFIG_FREERTOS_MAX_TASK_NAME_LEN "16")
set(CONFIG_FREERTOS_ENABLE_BACKWARD_COMPATIBILITY "")
set(CONFIG_FREERTOS_USE_TIMERS "y")
set(CONFIG_FREERTOS_TIMER_SERVICE_TASK_NAME "Tmr Svc")
set(CONFIG_FREERTOS_TIMER_TASK_AFFINITY_CPU0 "")
set(CONFIG_FREERTOS_TIMER_TASK_AFFINITY_CPU1 "")
set(CONFIG_FREERTOS_TIMER_TASK_NO_AFFINITY "y")
set(CONFIG_FREERTOS_TIMER_SERVICE_TASK_CORE_AFFINITY "0x7fffffff")
set(CONFIG_FREERTOS_TIMER_TASK_PRIORITY "1")
set(CONFIG_FREERTOS_TIMER_TASK_STACK_DEPTH "2048")
set(CONFIG_FREERTOS_TIMER_QUEUE_LENGTH "10")
set(CONFIG_FREERTOS_QUEUE_REGISTRY_SIZE "0")
set(CONFIG_FREERTOS_TASK_NOTIFICATION_ARRAY_ENTRIES "1")
set(CONFIG_FREERTOS_USE_TRACE_FACILITY "")
set(CONFIG_FREERTOS_USE_LIST_DATA_INTEGRITY_CHECK_BYTES "")
set(CONFIG_FREERTOS_GENERATE_RUN_TIME_STATS "")
set(CONFIG_FREERTOS_USE_APPLICATION_TASK_TAG "")
set(CONFIG_FREERTOS_TASK_FUNCTION_WRAPPER "y")
set(CONFIG_FREERTOS_WATCHPOINT_END_OF_STACK "")
set(CONFIG_FREERTOS_TLSP_DELETION_CALLBACKS "y")
set(CONFIG_FREERTOS_TASK_PRE_DELETION_HOOK "")
set(CONFIG_FREERTOS_ENABLE_STATIC_TASK_CLEAN_UP "")
set(CONFIG_FREERTOS_CHECK_MUTEX_GIVEN_BY_OWNER "y")
set(CONFIG_FREERTOS_ISR_STACKSIZE "1536")
set(CONFIG_FREERTOS_INTERRUPT_BACKTRACE "y")
set(CONFIG_FREERTOS_TICK_SUPPORT_SYSTIMER "y")
set(CONFIG_FREERTOS_CORETIMER_SYSTIMER_LVL1 "y")
set(CONFIG_FREERTOS_CORETIMER_SYSTIMER_LVL3 "")
set(CONFIG_FREERTOS_SYSTICK_USES_SYSTIMER "y")
set(CONFIG_FREERTOS_PLACE_FUNCTIONS_INTO_FLASH "")
set(CONFIG_FREERTOS_CHECK_PORT_CRITICAL_COMPLIANCE "")
set(CONFIG_FREERTOS_PORT "y")
set(CONFIG_FREERTOS_NO_AFFINITY "0x7fffffff")
set(CONFIG_FREERTOS_SUPPORT_STATIC_ALLOCATION "y")
set(CONFIG_FREERTOS_DEBUG_OCDAWARE "y")
set(CONFIG_FREERTOS_ENABLE_TASK_SNAPSHOT "y")
set(CONFIG_FREERTOS_PLACE_SNAPSHOT_FUNS_INTO_FLASH "y")
set(CONFIG_FREERTOS_NUMBER_OF_CORES "2")
set(CONFIG_FREERTOS_IN_IRAM "y")
set(CONFIG_HAL_ASSERTION_EQUALS_SYSTEM "y")
set(CONFIG_HAL_ASSERTION_DISABLE "")
set(CONFIG_HAL_ASSERTION_SILENT "")
set(CONFIG_HAL_ASSERTION_ENABLE "")
set(CONFIG_HAL_DEFAULT_ASSERTION_LEVEL "2")
set(CONFIG_HAL_SYSTIMER_USE_ROM_IMPL "y")
set(CONFIG_HAL_WDT_USE_ROM_IMPL "y")
set(CONFIG_LOG_VERSION_1 "y")
set(CONFIG_LOG_VERSION_2 "")
set(CONFIG_LOG_VERSION "1")
set(CONFIG_LOG_DEFAULT_LEVEL_NONE "")
set(CONFIG_LOG_DEFAULT_LEVEL_ERROR "")
set(CONFIG_LOG_DEFAULT_LEVEL_WARN "")
set(CONFIG_LOG_DEFAULT_LEVEL_INFO "y")
set(CONFIG_LOG_DEFAULT_LEVEL_DEBUG "")
set(CONFIG_LOG_DEFAULT_LEVEL_VERBOSE "")
set(CONFIG_LOG_DEFAULT_LEVEL "3")
set(CONFIG_LOG_MAXIMUM_EQUALS_DEFAULT "y")
set(CONFIG_LOG_MAXIMUM_LEVEL_DEBUG "")
set(CONFIG_LOG_MAXIMUM_LEVEL_VERBOSE "")
set(CONFIG_LOG_MAXIMUM_LEVEL "3")
set(CONFIG_LOG_MASTER_LEVEL "")
set(CONFIG_LOG_DYNAMIC_LEVEL_CONTROL "y")
set(CONFIG_LOG_TAG_LEVEL_IMPL_NONE "")
set(CONFIG_LOG_TAG_LEVEL_IMPL_LINKED_LIST "")
set(CONFIG_LOG_TAG_LEVEL_IMPL_CACHE_AND_LINKED_LIST "y")
set(CONFIG_LOG_TAG_LEVEL_CACHE_ARRAY "")
set(CONFIG_LOG_TAG_LEVEL_CACHE_BINARY_MIN_HEAP "y")
set(CONFIG_LOG_TAG_LEVEL_IMPL_CACHE_SIZE "31")
set(CONFIG_LOG_COLORS "")
set(CONFIG_LOG_TIMESTAMP_SOURCE_RTOS "y")
set(CONFIG_LOG_TIMESTAMP_SOURCE_SYSTEM "")
set(CONFIG_LOG_MODE_TEXT_EN "y")
set(CONFIG_LOG_MODE_TEXT "y")
set(CONFIG_LOG_IN_IRAM "y")
set(CONFIG_LIBC_NEWLIB "y")
set(CONFIG_LIBC_MISC_IN_IRAM "y")
set(CONFIG_LIBC_LOCKS_PLACE_IN_IRAM "y")
set(CONFIG_LIBC_NEWLIB_NANO_FORMAT "")
set(CONFIG_LIBC_TIME_SYSCALL_USE_RTC_HRT "y")
set(CONFIG_LIBC_TIME_SYSCALL_USE_RTC "")
set(CONFIG_LIBC_TIME_SYSCALL_USE_HRT "")
set(CONFIG_LIBC_TIME_SYSCALL_USE_NONE "")
set(CONFIG_LIBC_OPTIMIZED_MISALIGNED_ACCESS "")
set(CONFIG_MMU_PAGE_SIZE_64KB "y")
set(CONFIG_MMU_PAGE_MODE "64KB")
set(CONFIG_MMU_PAGE_SIZE "0x10000")
set(CONFIG_SPI_FLASH_BROWNOUT_RESET_XMC "y")
set(CONFIG_SPI_FLASH_BROWNOUT_RESET "y")
set(CONFIG_SPI_FLASH_HPM_ENA "")
set(CONFIG_SPI_FLASH_HPM_AUTO "y")
set(CONFIG_SPI_FLASH_HPM_DIS "")
set(CONFIG_SPI_FLASH_HPM_ON "y")
set(CONFIG_SPI_FLASH_HPM_DC_AUTO "y")
set(CONFIG_SPI_FLASH_HPM_DC_DISABLE "")
set(CONFIG_SPI_FLASH_AUTO_SUSPEND "")
set(CONFIG_SPI_FLASH_SUSPEND_TSUS_VAL_US "50")
set(CONFIG_SPI_FLASH_FORCE_ENABLE_XMC_C_SUSPEND "")
set(CONFIG_SPI_FLASH_FORCE_ENABLE_C6_H2_SUSPEND "")
set(CONFIG_SPI_FLASH_PLACE_FUNCTIONS_IN_IRAM "y")
set(CONFIG_SPI_FLASH_VERIFY_WRITE "")
set(CONFIG_SPI_FLASH_ENABLE_COUNTERS "")
set(CONFIG_SPI_FLASH_ROM_DRIVER_PATCH "y")
set(CONFIG_SPI_FLASH_DANGEROUS_WRITE_ABORTS "y")
set(CONFIG_SPI_FLASH_DANGEROUS_WRITE_FAILS "")
set(CONFIG_SPI_FLASH_DANGEROUS_WRITE_ALLOWED "")
set(CONFIG_SPI_FLASH_BYPASS_BLOCK_ERASE "")
set(CONFIG_SPI_FLASH_YIELD_DURING_ERASE "y")
set(CONFIG_SPI_FLASH_ERASE_YIELD_DURATION_MS "20")
set(CONFIG_SPI_FLASH_ERASE_YIELD_TICKS "1")
set(CONFIG_SPI_FLASH_WRITE_CHUNK_SIZE "8192")
set(CONFIG_SPI_FLASH_SIZE_OVERRIDE "")
set(CONFIG_SPI_FLASH_CHECK_ERASE_TIMEOUT_DISABLED "")
set(CONFIG_SPI_FLASH_OVERRIDE_CHIP_DRIVER_LIST "")
set(CONFIG_SPI_FLASH_VENDOR_XMC_SUPPORTED "y")
set(CONFIG_SPI_FLASH_SUPPORT_ISSI_CHIP "")
set(CONFIG_SPI_FLASH_SUPPORT_MXIC_CHIP "")
set(CONFIG_SPI_FLASH_SUPPORT_GD_CHIP "")
set(CONFIG_SPI_FLASH_SUPPORT_WINBOND_CHIP "")
set(CONFIG_SPI_FLASH_SUPPORT_BOYA_CHIP "")
set(CONFIG_SPI_FLASH_SUPPORT_TH_CHIP "")
set(CONFIG_SPI_FLASH_ENABLE_ENCRYPTED_READ_WRITE "y")
set(CONFIG_IDF_EXPERIMENTAL_FEATURES "")
set(CONFIGS_LIST CONFIG_SOC_ADC_SUPPORTED;CONFIG_SOC_ANA_CMPR_SUPPORTED;CONFIG_SOC_DEDICATED_GPIO_SUPPORTED;CONFIG_SOC_UART_SUPPORTED;CONFIG_SOC_GDMA_SUPPORTED;CONFIG_SOC_UHCI_SUPPORTED;CONFIG_SOC_AHB_GDMA_SUPPORTED;CONFIG_SOC_AXI_GDMA_SUPPORTED;CONFIG_SOC_DW_GDMA_SUPPORTED;CONFIG_SOC_DMA2D_SUPPORTED;CONFIG_SOC_GPTIMER_SUPPORTED;CONFIG_SOC_PCNT_SUPPORTED;CONFIG_SOC_LCDCAM_SUPPORTED;CONFIG_SOC_LCDCAM_CAM_SUPPORTED;CONFIG_SOC_LCDCAM_I80_LCD_SUPPORTED;CONFIG_SOC_LCDCAM_RGB_LCD_SUPPORTED;CONFIG_SOC_MIPI_CSI_SUPPORTED;CONFIG_SOC_MIPI_DSI_SUPPORTED;CONFIG_SOC_MCPWM_SUPPORTED;CONFIG_SOC_TWAI_SUPPORTED;CONFIG_SOC_ETM_SUPPORTED;CONFIG_SOC_PARLIO_SUPPORTED;CONFIG_SOC_ASYNC_MEMCPY_SUPPORTED;CONFIG_SOC_EMAC_SUPPORTED;CONFIG_SOC_USB_OTG_SUPPORTED;CONFIG_SOC_WIRELESS_HOST_SUPPORTED;CONFIG_SOC_USB_SERIAL_JTAG_SUPPORTED;CONFIG_SOC_TEMP_SENSOR_SUPPORTED;CONFIG_SOC_SUPPORTS_SECURE_DL_MODE;CONFIG_SOC_ULP_SUPPORTED;CONFIG_SOC_LP_CORE_SUPPORTED;CONFIG_SOC_EFUSE_KEY_PURPOSE_FIELD;CONFIG_SOC_EFUSE_SUPPORTED;CONFIG_SOC_RTC_FAST_MEM_SUPPORTED;CONFIG_SOC_RTC_MEM_SUPPORTED;CONFIG_SOC_RMT_SUPPORTED;CONFIG_SOC_I2S_SUPPORTED;CONFIG_SOC_SDM_SUPPORTED;CONFIG_SOC_GPSPI_SUPPORTED;CONFIG_SOC_LEDC_SUPPORTED;CONFIG_SOC_ISP_SUPPORTED;CONFIG_SOC_I2C_SUPPORTED;CONFIG_SOC_SYSTIMER_SUPPORTED;CONFIG_SOC_AES_SUPPORTED;CONFIG_SOC_MPI_SUPPORTED;CONFIG_SOC_SHA_SUPPORTED;CONFIG_SOC_HMAC_SUPPORTED;CONFIG_SOC_DIG_SIGN_SUPPORTED;CONFIG_SOC_ECC_SUPPORTED;CONFIG_SOC_ECC_EXTENDED_MODES_SUPPORTED;CONFIG_SOC_FLASH_ENC_SUPPORTED;CONFIG_SOC_SECURE_BOOT_SUPPORTED;CONFIG_SOC_BOD_SUPPORTED;CONFIG_SOC_VBAT_SUPPORTED;CONFIG_SOC_APM_SUPPORTED;CONFIG_SOC_PMU_SUPPORTED;CONFIG_SOC_DCDC_SUPPORTED;CONFIG_SOC_PAU_SUPPORTED;CONFIG_SOC_LP_TIMER_SUPPORTED;CONFIG_SOC_ULP_LP_UART_SUPPORTED;CONFIG_SOC_LP_GPIO_MATRIX_SUPPORTED;CONFIG_SOC_LP_PERIPHERALS_SUPPORTED;CONFIG_SOC_LP_I2C_SUPPORTED;CONFIG_SOC_LP_I2S_SUPPORTED;CONFIG_SOC_LP_SPI_SUPPORTED;CONFIG_SOC_LP_ADC_SUPPORTED;CONFIG_SOC_LP_VAD_SUPPORTED;CONFIG_SOC_SPIRAM_SUPPORTED;CONFIG_SOC_PSRAM_DMA_CAPABLE;CONFIG_SOC_SDMMC_HOST_SUPPORTED;CONFIG_SOC_CLK_TREE_SUPPORTED;CONFIG_SOC_ASSIST_DEBUG_SUPPORTED;CONFIG_SOC_DEBUG_PROBE_SUPPORTED;CONFIG_SOC_WDT_SUPPORTED;CONFIG_SOC_SPI_FLASH_SUPPORTED;CONFIG_SOC_TOUCH_SENSOR_SUPPORTED;CONFIG_SOC_RNG_SUPPORTED;CONFIG_SOC_GP_LDO_SUPPORTED;CONFIG_SOC_PPA_SUPPORTED;CONFIG_SOC_LIGHT_SLEEP_SUPPORTED;CONFIG_SOC_DEEP_SLEEP_SUPPORTED;CONFIG_SOC_PM_SUPPORTED;CONFIG_SOC_BITSCRAMBLER_SUPPORTED;CONFIG_SOC_SIMD_INSTRUCTION_SUPPORTED;CONFIG_SOC_I3C_MASTER_SUPPORTED;CONFIG_SOC_XTAL_SUPPORT_40M;CONFIG_SOC_AES_SUPPORT_DMA;CONFIG_SOC_AES_SUPPORT_GCM;CONFIG_SOC_AES_GDMA;CONFIG_SOC_AES_SUPPORT_AES_128;CONFIG_SOC_AES_SUPPORT_AES_256;CONFIG_SOC_ADC_RTC_CTRL_SUPPORTED;CONFIG_SOC_ADC_DIG_CTRL_SUPPORTED;CONFIG_SOC_ADC_DMA_SUPPORTED;CONFIG_SOC_ADC_PERIPH_NUM;CONFIG_SOC_ADC_MAX_CHANNEL_NUM;CONFIG_SOC_ADC_ATTEN_NUM;CONFIG_SOC_ADC_DIGI_CONTROLLER_NUM;CONFIG_SOC_ADC_PATT_LEN_MAX;CONFIG_SOC_ADC_DIGI_MAX_BITWIDTH;CONFIG_SOC_ADC_DIGI_MIN_BITWIDTH;CONFIG_SOC_ADC_DIGI_IIR_FILTER_NUM;CONFIG_SOC_ADC_DIGI_MONITOR_NUM;CONFIG_SOC_ADC_DIGI_RESULT_BYTES;CONFIG_SOC_ADC_DIGI_DATA_BYTES_PER_CONV;CONFIG_SOC_ADC_SAMPLE_FREQ_THRES_HIGH;CONFIG_SOC_ADC_SAMPLE_FREQ_THRES_LOW;CONFIG_SOC_ADC_RTC_MIN_BITWIDTH;CONFIG_SOC_ADC_RTC_MAX_BITWIDTH;CONFIG_SOC_ADC_CALIBRATION_V1_SUPPORTED;CONFIG_SOC_ADC_SELF_HW_CALI_SUPPORTED;CONFIG_SOC_ADC_CALIB_CHAN_COMPENS_SUPPORTED;CONFIG_SOC_ADC_SHARED_POWER;CONFIG_SOC_BROWNOUT_RESET_SUPPORTED;CONFIG_SOC_SHARED_IDCACHE_SUPPORTED;CONFIG_SOC_CACHE_WRITEBACK_SUPPORTED;CONFIG_SOC_CACHE_FREEZE_SUPPORTED;CONFIG_SOC_CACHE_INTERNAL_MEM_VIA_L1CACHE;CONFIG_SOC_CPU_CORES_NUM;CONFIG_SOC_CPU_INTR_NUM;CONFIG_SOC_CPU_HAS_FLEXIBLE_INTC;CONFIG_SOC_INT_CLIC_SUPPORTED;CONFIG_SOC_INT_HW_NESTED_SUPPORTED;CONFIG_SOC_BRANCH_PREDICTOR_SUPPORTED;CONFIG_SOC_CPU_COPROC_NUM;CONFIG_SOC_CPU_HAS_FPU;CONFIG_SOC_CPU_HAS_FPU_EXT_ILL_BUG;CONFIG_SOC_CPU_HAS_HWLOOP;CONFIG_SOC_CPU_HAS_HWLOOP_STATE_BUG;CONFIG_SOC_CPU_HAS_PIE;CONFIG_SOC_HP_CPU_HAS_MULTIPLE_CORES;CONFIG_SOC_CPU_BREAKPOINTS_NUM;CONFIG_SOC_CPU_WATCHPOINTS_NUM;CONFIG_SOC_CPU_WATCHPOINT_MAX_REGION_SIZE;CONFIG_SOC_CPU_HAS_PMA;CONFIG_SOC_CPU_IDRAM_SPLIT_USING_PMP;CONFIG_SOC_CPU_PMP_REGION_GRANULARITY;CONFIG_SOC_CPU_HAS_LOCKUP_RESET;CONFIG_SOC_SIMD_PREFERRED_DATA_ALIGNMENT;CONFIG_SOC_DS_SIGNATURE_MAX_BIT_LEN;CONFIG_SOC_DS_KEY_PARAM_MD_IV_LENGTH;CONFIG_SOC_DS_KEY_CHECK_MAX_WAIT_US;CONFIG_SOC_DMA_CAN_ACCESS_FLASH;CONFIG_SOC_AHB_GDMA_VERSION;CONFIG_SOC_GDMA_SUPPORT_CRC;CONFIG_SOC_GDMA_NUM_GROUPS_MAX;CONFIG_SOC_GDMA_PAIRS_PER_GROUP_MAX;CONFIG_SOC_AXI_GDMA_SUPPORT_PSRAM;CONFIG_SOC_GDMA_SUPPORT_ETM;CONFIG_SOC_GDMA_SUPPORT_SLEEP_RETENTION;CONFIG_SOC_AXI_DMA_EXT_MEM_ENC_ALIGNMENT;CONFIG_SOC_DMA2D_GROUPS;CONFIG_SOC_DMA2D_TX_CHANNELS_PER_GROUP;CONFIG_SOC_DMA2D_RX_CHANNELS_PER_GROUP;CONFIG_SOC_ETM_GROUPS;CONFIG_SOC_ETM_CHANNELS_PER_GROUP;CONFIG_SOC_ETM_SUPPORT_SLEEP_RETENTION;CONFIG_SOC_GPIO_PORT;CONFIG_SOC_GPIO_PIN_COUNT;CONFIG_SOC_GPIO_SUPPORT_PIN_GLITCH_FILTER;CONFIG_SOC_GPIO_FLEX_GLITCH_FILTER_NUM;CONFIG_SOC_GPIO_SUPPORT_PIN_HYS_FILTER;CONFIG_SOC_GPIO_SUPPORT_ETM;CONFIG_SOC_GPIO_SUPPORT_RTC_INDEPENDENT;CONFIG_SOC_GPIO_SUPPORT_DEEPSLEEP_WAKEUP;CONFIG_SOC_LP_IO_HAS_INDEPENDENT_WAKEUP_SOURCE;CONFIG_SOC_LP_IO_CLOCK_IS_INDEPENDENT;CONFIG_SOC_GPIO_VALID_GPIO_MASK;CONFIG_SOC_GPIO_IN_RANGE_MAX;CONFIG_SOC_GPIO_OUT_RANGE_MAX;CONFIG_SOC_GPIO_DEEP_SLEEP_WAKE_VALID_GPIO_MASK;CONFIG_SOC_GPIO_DEEP_SLEEP_WAKE_SUPPORTED_PIN_CNT;CONFIG_SOC_GPIO_VALID_DIGITAL_IO_PAD_MASK;CONFIG_SOC_GPIO_CLOCKOUT_BY_GPIO_MATRIX;CONFIG_SOC_GPIO_CLOCKOUT_CHANNEL_NUM;CONFIG_SOC_CLOCKOUT_SUPPORT_CHANNEL_DIVIDER;CONFIG_SOC_DEBUG_PROBE_NUM_UNIT;CONFIG_SOC_DEBUG_PROBE_MAX_OUTPUT_WIDTH;CONFIG_SOC_GPIO_SUPPORT_FORCE_HOLD;CONFIG_SOC_RTCIO_PIN_COUNT;CONFIG_SOC_RTCIO_INPUT_OUTPUT_SUPPORTED;CONFIG_SOC_RTCIO_HOLD_SUPPORTED;CONFIG_SOC_RTCIO_WAKE_SUPPORTED;CONFIG_SOC_RTCIO_EDGE_WAKE_SUPPORTED;CONFIG_SOC_DEDIC_GPIO_OUT_CHANNELS_NUM;CONFIG_SOC_DEDIC_GPIO_IN_CHANNELS_NUM;CONFIG_SOC_DEDIC_PERIPH_ALWAYS_ENABLE;CONFIG_SOC_ANA_CMPR_NUM;CONFIG_SOC_ANA_CMPR_CAN_DISTINGUISH_EDGE;CONFIG_SOC_ANA_CMPR_SUPPORT_ETM;CONFIG_SOC_I2C_NUM;CONFIG_SOC_HP_I2C_NUM;CONFIG_SOC_I2C_FIFO_LEN;CONFIG_SOC_I2C_CMD_REG_NUM;CONFIG_SOC_I2C_SUPPORT_SLAVE;CONFIG_SOC_I2C_SUPPORT_HW_FSM_RST;CONFIG_SOC_I2C_SUPPORT_HW_CLR_BUS;CONFIG_SOC_I2C_SUPPORT_XTAL;CONFIG_SOC_I2C_SUPPORT_RTC;CONFIG_SOC_I2C_SUPPORT_10BIT_ADDR;CONFIG_SOC_I2C_SLAVE_SUPPORT_BROADCAST;CONFIG_SOC_I2C_SLAVE_CAN_GET_STRETCH_CAUSE;CONFIG_SOC_I2C_SLAVE_SUPPORT_I2CRAM_ACCESS;CONFIG_SOC_I2C_SLAVE_SUPPORT_SLAVE_UNMATCH;CONFIG_SOC_I2C_SUPPORT_SLEEP_RETENTION;CONFIG_SOC_LP_I2C_NUM;CONFIG_SOC_LP_I2C_FIFO_LEN;CONFIG_SOC_I2S_NUM;CONFIG_SOC_I2S_HW_VERSION_2;CONFIG_SOC_I2S_SUPPORTS_ETM;CONFIG_SOC_I2S_SUPPORTS_XTAL;CONFIG_SOC_I2S_SUPPORTS_APLL;CONFIG_SOC_I2S_SUPPORTS_PCM;CONFIG_SOC_I2S_SUPPORTS_PDM;CONFIG_SOC_I2S_SUPPORTS_PDM_TX;CONFIG_SOC_I2S_SUPPORTS_PCM2PDM;CONFIG_SOC_I2S_SUPPORTS_PDM_RX;CONFIG_SOC_I2S_SUPPORTS_PDM2PCM;CONFIG_SOC_I2S_SUPPORTS_PDM_RX_HP_FILTER;CONFIG_SOC_I2S_SUPPORTS_TX_SYNC_CNT;CONFIG_SOC_I2S_SUPPORTS_TDM;CONFIG_SOC_I2S_PDM_MAX_TX_LINES;CONFIG_SOC_I2S_PDM_MAX_RX_LINES;CONFIG_SOC_I2S_TDM_FULL_DATA_WIDTH;CONFIG_SOC_I2S_SUPPORT_SLEEP_RETENTION;CONFIG_SOC_LP_I2S_NUM;CONFIG_SOC_ISP_BF_SUPPORTED;CONFIG_SOC_ISP_CCM_SUPPORTED;CONFIG_SOC_ISP_DEMOSAIC_SUPPORTED;CONFIG_SOC_ISP_DVP_SUPPORTED;CONFIG_SOC_ISP_SHARPEN_SUPPORTED;CONFIG_SOC_ISP_COLOR_SUPPORTED;CONFIG_SOC_ISP_LSC_SUPPORTED;CONFIG_SOC_ISP_SHARE_CSI_BRG;CONFIG_SOC_ISP_NUMS;CONFIG_SOC_ISP_DVP_CTLR_NUMS;CONFIG_SOC_ISP_AE_CTLR_NUMS;CONFIG_SOC_ISP_AE_BLOCK_X_NUMS;CONFIG_SOC_ISP_AE_BLOCK_Y_NUMS;CONFIG_SOC_ISP_AF_CTLR_NUMS;CONFIG_SOC_ISP_AF_WINDOW_NUMS;CONFIG_SOC_ISP_BF_TEMPLATE_X_NUMS;CONFIG_SOC_ISP_BF_TEMPLATE_Y_NUMS;CONFIG_SOC_ISP_CCM_DIMENSION;CONFIG_SOC_ISP_DEMOSAIC_GRAD_RATIO_INT_BITS;CONFIG_SOC_ISP_DEMOSAIC_GRAD_RATIO_DEC_BITS;CONFIG_SOC_ISP_DEMOSAIC_GRAD_RATIO_RES_BITS;CONFIG_SOC_ISP_DVP_DATA_WIDTH_MAX;CONFIG_SOC_ISP_SHARPEN_TEMPLATE_X_NUMS;CONFIG_SOC_ISP_SHARPEN_TEMPLATE_Y_NUMS;CONFIG_SOC_ISP_SHARPEN_H_FREQ_COEF_INT_BITS;CONFIG_SOC_ISP_SHARPEN_H_FREQ_COEF_DEC_BITS;CONFIG_SOC_ISP_SHARPEN_H_FREQ_COEF_RES_BITS;CONFIG_SOC_ISP_SHARPEN_M_FREQ_COEF_INT_BITS;CONFIG_SOC_ISP_SHARPEN_M_FREQ_COEF_DEC_BITS;CONFIG_SOC_ISP_SHARPEN_M_FREQ_COEF_RES_BITS;CONFIG_SOC_ISP_HIST_CTLR_NUMS;CONFIG_SOC_ISP_HIST_BLOCK_X_NUMS;CONFIG_SOC_ISP_HIST_BLOCK_Y_NUMS;CONFIG_SOC_ISP_HIST_SEGMENT_NUMS;CONFIG_SOC_ISP_HIST_INTERVAL_NUMS;CONFIG_SOC_ISP_LSC_GRAD_RATIO_INT_BITS;CONFIG_SOC_ISP_LSC_GRAD_RATIO_DEC_BITS;CONFIG_SOC_ISP_LSC_GRAD_RATIO_RES_BITS;CONFIG_SOC_LEDC_SUPPORT_PLL_DIV_CLOCK;CONFIG_SOC_LEDC_SUPPORT_XTAL_CLOCK;CONFIG_SOC_LEDC_TIMER_NUM;CONFIG_SOC_LEDC_CHANNEL_NUM;CONFIG_SOC_LEDC_TIMER_BIT_WIDTH;CONFIG_SOC_LEDC_GAMMA_CURVE_FADE_SUPPORTED;CONFIG_SOC_LEDC_GAMMA_CURVE_FADE_RANGE_MAX;CONFIG_SOC_LEDC_SUPPORT_FADE_STOP;CONFIG_SOC_LEDC_FADE_PARAMS_BIT_WIDTH;CONFIG_SOC_LEDC_SUPPORT_SLEEP_RETENTION;CONFIG_SOC_MMU_PERIPH_NUM;CONFIG_SOC_MMU_LINEAR_ADDRESS_REGION_NUM;CONFIG_SOC_MMU_DI_VADDR_SHARED;CONFIG_SOC_MMU_PER_EXT_MEM_TARGET;CONFIG_SOC_MPU_MIN_REGION_SIZE;CONFIG_SOC_MPU_REGIONS_MAX_NUM;CONFIG_SOC_PCNT_GROUPS;CONFIG_SOC_PCNT_UNITS_PER_GROUP;CONFIG_SOC_PCNT_CHANNELS_PER_UNIT;CONFIG_SOC_PCNT_THRES_POINT_PER_UNIT;CONFIG_SOC_PCNT_SUPPORT_RUNTIME_THRES_UPDATE;CONFIG_SOC_PCNT_SUPPORT_CLEAR_SIGNAL;CONFIG_SOC_PCNT_SUPPORT_SLEEP_RETENTION;CONFIG_SOC_RMT_GROUPS;CONFIG_SOC_RMT_TX_CANDIDATES_PER_GROUP;CONFIG_SOC_RMT_RX_CANDIDATES_PER_GROUP;CONFIG_SOC_RMT_CHANNELS_PER_GROUP;CONFIG_SOC_RMT_MEM_WORDS_PER_CHANNEL;CONFIG_SOC_RMT_SUPPORT_RX_PINGPONG;CONFIG_SOC_RMT_SUPPORT_RX_DEMODULATION;CONFIG_SOC_RMT_SUPPORT_TX_ASYNC_STOP;CONFIG_SOC_RMT_SUPPORT_TX_LOOP_COUNT;CONFIG_SOC_RMT_SUPPORT_TX_LOOP_AUTO_STOP;CONFIG_SOC_RMT_SUPPORT_TX_SYNCHRO;CONFIG_SOC_RMT_SUPPORT_TX_CARRIER_DATA_ONLY;CONFIG_SOC_RMT_SUPPORT_XTAL;CONFIG_SOC_RMT_SUPPORT_RC_FAST;CONFIG_SOC_RMT_SUPPORT_DMA;CONFIG_SOC_RMT_SUPPORT_SLEEP_RETENTION;CONFIG_SOC_LCD_I80_SUPPORTED;CONFIG_SOC_LCD_RGB_SUPPORTED;CONFIG_SOC_LCDCAM_I80_NUM_BUSES;CONFIG_SOC_LCDCAM_I80_BUS_WIDTH;CONFIG_SOC_LCDCAM_RGB_NUM_PANELS;CONFIG_SOC_LCDCAM_RGB_DATA_WIDTH;CONFIG_SOC_LCD_SUPPORT_RGB_YUV_CONV;CONFIG_SOC_MCPWM_GROUPS;CONFIG_SOC_MCPWM_TIMERS_PER_GROUP;CONFIG_SOC_MCPWM_OPERATORS_PER_GROUP;CONFIG_SOC_MCPWM_COMPARATORS_PER_OPERATOR;CONFIG_SOC_MCPWM_EVENT_COMPARATORS_PER_OPERATOR;CONFIG_SOC_MCPWM_GENERATORS_PER_OPERATOR;CONFIG_SOC_MCPWM_TRIGGERS_PER_OPERATOR;CONFIG_SOC_MCPWM_GPIO_FAULTS_PER_GROUP;CONFIG_SOC_MCPWM_CAPTURE_TIMERS_PER_GROUP;CONFIG_SOC_MCPWM_CAPTURE_CHANNELS_PER_TIMER;CONFIG_SOC_MCPWM_GPIO_SYNCHROS_PER_GROUP;CONFIG_SOC_MCPWM_SWSYNC_CAN_PROPAGATE;CONFIG_SOC_MCPWM_SUPPORT_ETM;CONFIG_SOC_MCPWM_SUPPORT_EVENT_COMPARATOR;CONFIG_SOC_MCPWM_CAPTURE_CLK_FROM_GROUP;CONFIG_SOC_MCPWM_SUPPORT_SLEEP_RETENTION;CONFIG_SOC_USB_OTG_PERIPH_NUM;CONFIG_SOC_USB_UTMI_PHY_NUM;CONFIG_SOC_USB_UTMI_PHY_NO_POWER_OFF_ISO;CONFIG_SOC_PARLIO_GROUPS;CONFIG_SOC_PARLIO_TX_UNITS_PER_GROUP;CONFIG_SOC_PARLIO_RX_UNITS_PER_GROUP;CONFIG_SOC_PARLIO_TX_UNIT_MAX_DATA_WIDTH;CONFIG_SOC_PARLIO_RX_UNIT_MAX_DATA_WIDTH;CONFIG_SOC_PARLIO_TX_CLK_SUPPORT_GATING;CONFIG_SOC_PARLIO_RX_CLK_SUPPORT_GATING;CONFIG_SOC_PARLIO_RX_CLK_SUPPORT_OUTPUT;CONFIG_SOC_PARLIO_TRANS_BIT_ALIGN;CONFIG_SOC_PARLIO_TX_SUPPORT_LOOP_TRANSMISSION;CONFIG_SOC_PARLIO_SUPPORT_SLEEP_RETENTION;CONFIG_SOC_PARLIO_SUPPORT_SPI_LCD;CONFIG_SOC_PARLIO_SUPPORT_I80_LCD;CONFIG_SOC_MPI_MEM_BLOCKS_NUM;CONFIG_SOC_MPI_OPERATIONS_NUM;CONFIG_SOC_RSA_MAX_BIT_LEN;CONFIG_SOC_SDMMC_USE_IOMUX;CONFIG_SOC_SDMMC_USE_GPIO_MATRIX;CONFIG_SOC_SDMMC_NUM_SLOTS;CONFIG_SOC_SDMMC_DELAY_PHASE_NUM;CONFIG_SOC_SDMMC_IO_POWER_EXTERNAL;CONFIG_SOC_SDMMC_PSRAM_DMA_CAPABLE;CONFIG_SOC_SDMMC_UHS_I_SUPPORTED;CONFIG_SOC_SHA_DMA_MAX_BUFFER_SIZE;CONFIG_SOC_SHA_SUPPORT_DMA;CONFIG_SOC_SHA_SUPPORT_RESUME;CONFIG_SOC_SHA_GDMA;CONFIG_SOC_SHA_SUPPORT_SHA1;CONFIG_SOC_SHA_SUPPORT_SHA224;CONFIG_SOC_SHA_SUPPORT_SHA256;CONFIG_SOC_SHA_SUPPORT_SHA384;CONFIG_SOC_SHA_SUPPORT_SHA512;CONFIG_SOC_SHA_SUPPORT_SHA512_224;CONFIG_SOC_SHA_SUPPORT_SHA512_256;CONFIG_SOC_SHA_SUPPORT_SHA512_T;CONFIG_SOC_ECDSA_SUPPORT_EXPORT_PUBKEY;CONFIG_SOC_ECDSA_SUPPORT_DETERMINISTIC_MODE;CONFIG_SOC_ECDSA_USES_MPI;CONFIG_SOC_SDM_GROUPS;CONFIG_SOC_SDM_CHANNELS_PER_GROUP;CONFIG_SOC_SDM_CLK_SUPPORT_PLL_F80M;CONFIG_SOC_SDM_CLK_SUPPORT_XTAL;CONFIG_SOC_SPI_PERIPH_NUM;CONFIG_SOC_SPI_MAX_CS_NUM;CONFIG_SOC_SPI_MAXIMUM_BUFFER_SIZE;CONFIG_SOC_SPI_SUPPORT_SLEEP_RETENTION;CONFIG_SOC_SPI_SUPPORT_SLAVE_HD_VER2;CONFIG_SOC_SPI_SLAVE_SUPPORT_SEG_TRANS;CONFIG_SOC_SPI_SUPPORT_DDRCLK;CONFIG_SOC_SPI_SUPPORT_CD_SIG;CONFIG_SOC_SPI_SUPPORT_OCT;CONFIG_SOC_SPI_SUPPORT_CLK_XTAL;CONFIG_SOC_SPI_SUPPORT_CLK_RC_FAST;CONFIG_SOC_SPI_SUPPORT_CLK_SPLL;CONFIG_SOC_MSPI_HAS_INDEPENT_IOMUX;CONFIG_SOC_MEMSPI_IS_INDEPENDENT;CONFIG_SOC_SPI_MAX_PRE_DIVIDER;CONFIG_SOC_LP_SPI_PERIPH_NUM;CONFIG_SOC_LP_SPI_MAXIMUM_BUFFER_SIZE;CONFIG_SOC_SPIRAM_XIP_SUPPORTED;CONFIG_SOC_SPI_MEM_SUPPORT_AUTO_WAIT_IDLE;CONFIG_SOC_SPI_MEM_SUPPORT_AUTO_SUSPEND;CONFIG_SOC_SPI_MEM_SUPPORT_AUTO_RESUME;CONFIG_SOC_SPI_MEM_SUPPORT_IDLE_INTR;CONFIG_SOC_SPI_MEM_SUPPORT_SW_SUSPEND;CONFIG_SOC_SPI_MEM_SUPPORT_CHECK_SUS;CONFIG_SOC_SPI_MEM_SUPPORT_TIMING_TUNING;CONFIG_SOC_MEMSPI_TIMING_TUNING_BY_DQS;CONFIG_SOC_MEMSPI_TIMING_TUNING_BY_FLASH_DELAY;CONFIG_SOC_SPI_MEM_SUPPORT_CACHE_32BIT_ADDR_MAP;CONFIG_SOC_SPI_PERIPH_SUPPORT_CONTROL_DUMMY_OUT;CONFIG_SOC_MEMSPI_SRC_FREQ_80M_SUPPORTED;CONFIG_SOC_MEMSPI_SRC_FREQ_40M_SUPPORTED;CONFIG_SOC_MEMSPI_SRC_FREQ_20M_SUPPORTED;CONFIG_SOC_MEMSPI_SRC_FREQ_120M_SUPPORTED;CONFIG_SOC_MEMSPI_FLASH_PSRAM_INDEPENDENT;CONFIG_SOC_SYSTIMER_COUNTER_NUM;CONFIG_SOC_SYSTIMER_ALARM_NUM;CONFIG_SOC_SYSTIMER_BIT_WIDTH_LO;CONFIG_SOC_SYSTIMER_BIT_WIDTH_HI;CONFIG_SOC_SYSTIMER_FIXED_DIVIDER;CONFIG_SOC_SYSTIMER_SUPPORT_RC_FAST;CONFIG_SOC_SYSTIMER_INT_LEVEL;CONFIG_SOC_SYSTIMER_ALARM_MISS_COMPENSATE;CONFIG_SOC_SYSTIMER_SUPPORT_ETM;CONFIG_SOC_LP_TIMER_BIT_WIDTH_LO;CONFIG_SOC_LP_TIMER_BIT_WIDTH_HI;CONFIG_SOC_TIMER_GROUPS;CONFIG_SOC_TIMER_GROUP_TIMERS_PER_GROUP;CONFIG_SOC_TIMER_GROUP_COUNTER_BIT_WIDTH;CONFIG_SOC_TIMER_GROUP_SUPPORT_XTAL;CONFIG_SOC_TIMER_GROUP_SUPPORT_RC_FAST;CONFIG_SOC_TIMER_GROUP_TOTAL_TIMERS;CONFIG_SOC_TIMER_SUPPORT_ETM;CONFIG_SOC_TIMER_SUPPORT_SLEEP_RETENTION;CONFIG_SOC_MWDT_SUPPORT_XTAL;CONFIG_SOC_MWDT_SUPPORT_SLEEP_RETENTION;CONFIG_SOC_TOUCH_SENSOR_VERSION;CONFIG_SOC_TOUCH_SENSOR_NUM;CONFIG_SOC_TOUCH_MIN_CHAN_ID;CONFIG_SOC_TOUCH_MAX_CHAN_ID;CONFIG_SOC_TOUCH_SUPPORT_SLEEP_WAKEUP;CONFIG_SOC_TOUCH_SUPPORT_BENCHMARK;CONFIG_SOC_TOUCH_SUPPORT_WATERPROOF;CONFIG_SOC_TOUCH_SUPPORT_PROX_SENSING;CONFIG_SOC_TOUCH_PROXIMITY_CHANNEL_NUM;CONFIG_SOC_TOUCH_PROXIMITY_MEAS_DONE_SUPPORTED;CONFIG_SOC_TOUCH_SUPPORT_FREQ_HOP;CONFIG_SOC_TOUCH_SAMPLE_CFG_NUM;CONFIG_SOC_TWAI_CONTROLLER_NUM;CONFIG_SOC_TWAI_MASK_FILTER_NUM;CONFIG_SOC_TWAI_CLK_SUPPORT_XTAL;CONFIG_SOC_TWAI_BRP_MIN;CONFIG_SOC_TWAI_BRP_MAX;CONFIG_SOC_TWAI_SUPPORTS_RX_STATUS;CONFIG_SOC_TWAI_SUPPORT_SLEEP_RETENTION;CONFIG_SOC_EFUSE_DIS_PAD_JTAG;CONFIG_SOC_EFUSE_DIS_USB_JTAG;CONFIG_SOC_EFUSE_DIS_DIRECT_BOOT;CONFIG_SOC_EFUSE_SOFT_DIS_JTAG;CONFIG_SOC_EFUSE_DIS_DOWNLOAD_MSPI;CONFIG_SOC_EFUSE_ECDSA_KEY;CONFIG_SOC_KEY_MANAGER_ECDSA_KEY_DEPLOY;CONFIG_SOC_KEY_MANAGER_FE_KEY_DEPLOY;CONFIG_SOC_SECURE_BOOT_V2_RSA;CONFIG_SOC_SECURE_BOOT_V2_ECC;CONFIG_SOC_EFUSE_SECURE_BOOT_KEY_DIGESTS;CONFIG_SOC_EFUSE_REVOKE_BOOT_KEY_DIGESTS;CONFIG_SOC_SUPPORT_SECURE_BOOT_REVOKE_KEY;CONFIG_SOC_FLASH_ENCRYPTED_XTS_AES_BLOCK_MAX;CONFIG_SOC_FLASH_ENCRYPTION_XTS_AES;CONFIG_SOC_FLASH_ENCRYPTION_XTS_AES_OPTIONS;CONFIG_SOC_FLASH_ENCRYPTION_XTS_AES_128;CONFIG_SOC_FLASH_ENCRYPTION_XTS_AES_256;CONFIG_SOC_UART_NUM;CONFIG_SOC_UART_HP_NUM;CONFIG_SOC_UART_LP_NUM;CONFIG_SOC_UART_FIFO_LEN;CONFIG_SOC_LP_UART_FIFO_LEN;CONFIG_SOC_UART_BITRATE_MAX;CONFIG_SOC_UART_SUPPORT_PLL_F80M_CLK;CONFIG_SOC_UART_SUPPORT_RTC_CLK;CONFIG_SOC_UART_SUPPORT_XTAL_CLK;CONFIG_SOC_UART_SUPPORT_WAKEUP_INT;CONFIG_SOC_UART_HAS_LP_UART;CONFIG_SOC_UART_SUPPORT_SLEEP_RETENTION;CONFIG_SOC_UART_SUPPORT_FSM_TX_WAIT_SEND;CONFIG_SOC_UART_WAKEUP_CHARS_SEQ_MAX_LEN;CONFIG_SOC_UART_WAKEUP_SUPPORT_ACTIVE_THRESH_MODE;CONFIG_SOC_UART_WAKEUP_SUPPORT_FIFO_THRESH_MODE;CONFIG_SOC_UART_WAKEUP_SUPPORT_START_BIT_MODE;CONFIG_SOC_UART_WAKEUP_SUPPORT_CHAR_SEQ_MODE;CONFIG_SOC_LP_I2S_SUPPORT_VAD;CONFIG_SOC_UHCI_NUM;CONFIG_SOC_COEX_HW_PTI;CONFIG_SOC_PHY_DIG_REGS_MEM_SIZE;CONFIG_SOC_WIFI_LIGHT_SLEEP_CLK_WIDTH;CONFIG_SOC_PM_SUPPORT_EXT1_WAKEUP;CONFIG_SOC_PM_SUPPORT_EXT1_WAKEUP_MODE_PER_PIN;CONFIG_SOC_PM_EXT1_WAKEUP_BY_PMU;CONFIG_SOC_PM_SUPPORT_WIFI_WAKEUP;CONFIG_SOC_PM_SUPPORT_TOUCH_SENSOR_WAKEUP;CONFIG_SOC_PM_SUPPORT_XTAL32K_PD;CONFIG_SOC_PM_SUPPORT_RC32K_PD;CONFIG_SOC_PM_SUPPORT_RC_FAST_PD;CONFIG_SOC_PM_SUPPORT_VDDSDIO_PD;CONFIG_SOC_PM_SUPPORT_TOP_PD;CONFIG_SOC_PM_SUPPORT_CNNT_PD;CONFIG_SOC_PM_SUPPORT_RTC_PERIPH_PD;CONFIG_SOC_PM_SUPPORT_DEEPSLEEP_CHECK_STUB_ONLY;CONFIG_SOC_PM_CPU_RETENTION_BY_SW;CONFIG_SOC_PM_CACHE_RETENTION_BY_PAU;CONFIG_SOC_PM_PAU_LINK_NUM;CONFIG_SOC_PM_PAU_REGDMA_LINK_MULTI_ADDR;CONFIG_SOC_PAU_IN_TOP_DOMAIN;CONFIG_SOC_CPU_IN_TOP_DOMAIN;CONFIG_SOC_PM_PAU_REGDMA_UPDATE_CACHE_BEFORE_WAIT_COMPARE;CONFIG_SOC_SLEEP_SYSTIMER_STALL_WORKAROUND;CONFIG_SOC_SLEEP_TGWDT_STOP_WORKAROUND;CONFIG_SOC_PM_RETENTION_MODULE_NUM;CONFIG_SOC_PSRAM_VDD_POWER_MPLL;CONFIG_SOC_CLK_RC_FAST_SUPPORT_CALIBRATION;CONFIG_SOC_CLK_APLL_SUPPORTED;CONFIG_SOC_CLK_MPLL_SUPPORTED;CONFIG_SOC_CLK_SDIO_PLL_SUPPORTED;CONFIG_SOC_CLK_XTAL32K_SUPPORTED;CONFIG_SOC_CLK_RC32K_SUPPORTED;CONFIG_SOC_CLK_LP_FAST_SUPPORT_LP_PLL;CONFIG_SOC_CLK_LP_FAST_SUPPORT_XTAL;CONFIG_SOC_PERIPH_CLK_CTRL_SHARED;CONFIG_SOC_CLK_ANA_I2C_MST_HAS_ROOT_GATE;CONFIG_SOC_TEMPERATURE_SENSOR_LP_PLL_SUPPORT;CONFIG_SOC_TEMPERATURE_SENSOR_INTR_SUPPORT;CONFIG_SOC_TSENS_IS_INDEPENDENT_FROM_ADC;CONFIG_SOC_TEMPERATURE_SENSOR_SUPPORT_ETM;CONFIG_SOC_TEMPERATURE_SENSOR_SUPPORT_SLEEP_RETENTION;CONFIG_SOC_MEM_TCM_SUPPORTED;CONFIG_SOC_MEM_NON_CONTIGUOUS_SRAM;CONFIG_SOC_ASYNCHRONOUS_BUS_ERROR_MODE;CONFIG_SOC_EMAC_IEEE1588V2_SUPPORTED;CONFIG_SOC_EMAC_USE_MULTI_IO_MUX;CONFIG_SOC_EMAC_MII_USE_GPIO_MATRIX;CONFIG_SOC_JPEG_CODEC_SUPPORTED;CONFIG_SOC_JPEG_DECODE_SUPPORTED;CONFIG_SOC_JPEG_ENCODE_SUPPORTED;CONFIG_SOC_LCDCAM_CAM_SUPPORT_RGB_YUV_CONV;CONFIG_SOC_LCDCAM_CAM_PERIPH_NUM;CONFIG_SOC_LCDCAM_CAM_DATA_WIDTH_MAX;CONFIG_SOC_I3C_MASTER_PERIPH_NUM;CONFIG_SOC_I3C_MASTER_ADDRESS_TABLE_NUM;CONFIG_SOC_I3C_MASTER_COMMAND_TABLE_NUM;CONFIG_SOC_LP_CORE_SUPPORT_ETM;CONFIG_SOC_LP_CORE_SUPPORT_LP_ADC;CONFIG_SOC_LP_CORE_SUPPORT_LP_VAD;CONFIG_SOC_LP_CORE_SUPPORT_STORE_LOAD_EXCEPTIONS;CONFIG_IDF_CMAKE;CONFIG_IDF_TOOLCHAIN;CONFIG_IDF_TOOLCHAIN_GCC;CONFIG_IDF_TARGET_ARCH_RISCV;CONFIG_IDF_TARGET_ARCH;CONFIG_IDF_TARGET;CONFIG_IDF_INIT_VERSION;CONFIG_IDF_TARGET_ESP32P4;CONFIG_IDF_FIRMWARE_CHIP_ID;CONFIG_APP_BUILD_TYPE_APP_2NDBOOT;CONFIG_APP_BUILD_TYPE_RAM;CONFIG_APP_BUILD_TYPE_ELF_RAM;CONFIG_APP_BUILD_GENERATE_BINARIES;CONFIG_APP_BUILD_BOOTLOADER;CONFIG_APP_BUILD_USE_FLASH_SECTIONS;CONFIG_APP_REPRODUCIBLE_BUILD;CONFIG_APP_NO_BLOBS;CONFIG_NO_BLOBS;CONFIG_BOOTLOADER_COMPILE_TIME_DATE;CONFIG_BOOTLOADER_PROJECT_VER;CONFIG_BOOTLOADER_APP_ROLLBACK_ENABLE;CONFIG_APP_ROLLBACK_ENABLE;CONFIG_BOOTLOADER_OFFSET_IN_FLASH;CONFIG_BOOTLOADER_COMPILER_OPTIMIZATION_SIZE;CONFIG_BOOTLOADER_COMPILER_OPTIMIZATION_DEBUG;CONFIG_BOOTLOADER_COMPILER_OPTIMIZATION_PERF;CONFIG_BOOTLOADER_LOG_VERSION_1;CONFIG_BOOTLOADER_LOG_VERSION;CONFIG_BOOTLOADER_LOG_LEVEL_NONE;CONFIG_LOG_BOOTLOADER_LEVEL_NONE;CONFIG_BOOTLOADER_LOG_LEVEL_ERROR;CONFIG_LOG_BOOTLOADER_LEVEL_ERROR;CONFIG_BOOTLOADER_LOG_LEVEL_WARN;CONFIG_LOG_BOOTLOADER_LEVEL_WARN;CONFIG_BOOTLOADER_LOG_LEVEL_INFO;CONFIG_LOG_BOOTLOADER_LEVEL_INFO;CONFIG_BOOTLOADER_LOG_LEVEL_DEBUG;CONFIG_LOG_BOOTLOADER_LEVEL_DEBUG;CONFIG_BOOTLOADER_LOG_LEVEL_VERBOSE;CONFIG_LOG_BOOTLOADER_LEVEL_VERBOSE;CONFIG_BOOTLOADER_LOG_LEVEL;CONFIG_LOG_BOOTLOADER_LEVEL;CONFIG_BOOTLOADER_LOG_COLORS;CONFIG_BOOTLOADER_LOG_TIMESTAMP_SOURCE_CPU_TICKS;CONFIG_BOOTLOADER_LOG_MODE_TEXT_EN;CONFIG_BOOTLOADER_LOG_MODE_TEXT;CONFIG_BOOTLOADER_FLASH_DC_AWARE;CONFIG_BOOTLOADER_FLASH_XMC_SUPPORT;CONFIG_BOOTLOADER_FACTORY_RESET;CONFIG_BOOTLOADER_APP_TEST;CONFIG_BOOTLOADER_REGION_PROTECTION_ENABLE;CONFIG_BOOTLOADER_WDT_ENABLE;CONFIG_BOOTLOADER_WDT_DISABLE_IN_USER_CODE;CONFIG_BOOTLOADER_WDT_TIME_MS;CONFIG_BOOTLOADER_SKIP_VALIDATE_IN_DEEP_SLEEP;CONFIG_BOOTLOADER_SKIP_VALIDATE_ON_POWER_ON;CONFIG_BOOTLOADER_SKIP_VALIDATE_ALWAYS;CONFIG_BOOTLOADER_RESERVE_RTC_SIZE;CONFIG_BOOTLOADER_CUSTOM_RESERVE_RTC;CONFIG_SECURE_BOOT_V2_RSA_SUPPORTED;CONFIG_SECURE_BOOT_V2_ECC_SUPPORTED;CONFIG_SECURE_BOOT_V2_PREFERRED;CONFIG_SECURE_SIGNED_APPS_NO_SECURE_BOOT;CONFIG_SECURE_BOOT;CONFIG_SECURE_FLASH_ENC_ENABLED;CONFIG_FLASH_ENCRYPTION_ENABLED;CONFIG_SECURE_ROM_DL_MODE_ENABLED;CONFIG_APP_COMPILE_TIME_DATE;CONFIG_APP_EXCLUDE_PROJECT_VER_VAR;CONFIG_APP_EXCLUDE_PROJECT_NAME_VAR;CONFIG_APP_PROJECT_VER_FROM_CONFIG;CONFIG_APP_RETRIEVE_LEN_ELF_SHA;CONFIG_ESP_ROM_HAS_CRC_LE;CONFIG_ESP_ROM_HAS_CRC_BE;CONFIG_ESP_ROM_UART_CLK_IS_XTAL;CONFIG_ESP_ROM_USB_SERIAL_DEVICE_NUM;CONFIG_ESP_ROM_USB_OTG_NUM;CONFIG_ESP_ROM_HAS_RETARGETABLE_LOCKING;CONFIG_ESP_ROM_GET_CLK_FREQ;CONFIG_ESP_ROM_HAS_RVFPLIB;CONFIG_ESP_ROM_HAS_HAL_WDT;CONFIG_ESP_ROM_HAS_HAL_SYSTIMER;CONFIG_ESP_ROM_HAS_LAYOUT_TABLE;CONFIG_ESP_ROM_WDT_INIT_PATCH;CONFIG_ESP_ROM_HAS_LP_ROM;CONFIG_ESP_ROM_WITHOUT_REGI2C;CONFIG_ESP_ROM_HAS_NEWLIB;CONFIG_ESP_ROM_HAS_NEWLIB_NANO_FORMAT;CONFIG_ESP_ROM_HAS_NEWLIB_NANO_PRINTF_FLOAT_BUG;CONFIG_ESP_ROM_HAS_VERSION;CONFIG_ESP_ROM_CLIC_INT_TYPE_PATCH;CONFIG_ESP_ROM_HAS_OUTPUT_PUTC_FUNC;CONFIG_ESP_ROM_HAS_SUBOPTIMAL_NEWLIB_ON_MISALIGNED_MEMORY;CONFIG_BOOT_ROM_LOG_ALWAYS_ON;CONFIG_BOOT_ROM_LOG_ALWAYS_OFF;CONFIG_BOOT_ROM_LOG_ON_GPIO_HIGH;CONFIG_BOOT_ROM_LOG_ON_GPIO_LOW;CONFIG_ESPTOOLPY_NO_STUB;CONFIG_ESPTOOLPY_FLASHMODE_QIO;CONFIG_FLASHMODE_QIO;CONFIG_ESPTOOLPY_FLASHMODE_QOUT;CONFIG_FLASHMODE_QOUT;CONFIG_ESPTOOLPY_FLASHMODE_DIO;CONFIG_FLASHMODE_DIO;CONFIG_ESPTOOLPY_FLASHMODE_DOUT;CONFIG_FLASHMODE_DOUT;CONFIG_ESPTOOLPY_FLASH_SAMPLE_MODE_STR;CONFIG_ESPTOOLPY_FLASHMODE;CONFIG_ESPTOOLPY_FLASHFREQ_80M;CONFIG_ESPTOOLPY_FLASHFREQ_40M;CONFIG_ESPTOOLPY_FLASHFREQ_20M;CONFIG_ESPTOOLPY_FLASHFREQ_VAL;CONFIG_ESPTOOLPY_FLASHFREQ;CONFIG_ESPTOOLPY_FLASHSIZE_1MB;CONFIG_ESPTOOLPY_FLASHSIZE_2MB;CONFIG_ESPTOOLPY_FLASHSIZE_4MB;CONFIG_ESPTOOLPY_FLASHSIZE_8MB;CONFIG_ESPTOOLPY_FLASHSIZE_16MB;CONFIG_ESPTOOLPY_FLASHSIZE_32MB;CONFIG_ESPTOOLPY_FLASHSIZE_64MB;CONFIG_ESPTOOLPY_FLASHSIZE_128MB;CONFIG_ESPTOOLPY_FLASHSIZE;CONFIG_ESPTOOLPY_HEADER_FLASHSIZE_UPDATE;CONFIG_ESPTOOLPY_BEFORE_RESET;CONFIG_ESPTOOLPY_BEFORE_NORESET;CONFIG_ESPTOOLPY_BEFORE;CONFIG_ESPTOOLPY_AFTER_RESET;CONFIG_ESPTOOLPY_AFTER_NORESET;CONFIG_ESPTOOLPY_AFTER;CONFIG_ESPTOOLPY_MONITOR_BAUD;CONFIG_MONITOR_BAUD;CONFIG_PARTITION_TABLE_SINGLE_APP;CONFIG_PARTITION_TABLE_SINGLE_APP_LARGE;CONFIG_PARTITION_TABLE_TWO_OTA;CONFIG_PARTITION_TABLE_TWO_OTA_LARGE;CONFIG_PARTITION_TABLE_CUSTOM;CONFIG_PARTITION_TABLE_CUSTOM_FILENAME;CONFIG_PARTITION_TABLE_FILENAME;CONFIG_PARTITION_TABLE_OFFSET;CONFIG_PARTITION_TABLE_MD5;CONFIG_COMPILER_OPTIMIZATION_DEBUG;CONFIG_OPTIMIZATION_LEVEL_DEBUG;CONFIG_COMPILER_OPTIMIZATION_LEVEL_DEBUG;CONFIG_COMPILER_OPTIMIZATION_DEFAULT;CONFIG_COMPILER_OPTIMIZATION_SIZE;CONFIG_OPTIMIZATION_LEVEL_RELEASE;CONFIG_COMPILER_OPTIMIZATION_LEVEL_RELEASE;CONFIG_COMPILER_OPTIMIZATION_PERF;CONFIG_COMPILER_OPTIMIZATION_NONE;CONFIG_COMPILER_OPTIMIZATION_ASSERTIONS_ENABLE;CONFIG_OPTIMIZATION_ASSERTIONS_ENABLED;CONFIG_COMPILER_OPTIMIZATION_ASSERTIONS_SILENT;CONFIG_OPTIMIZATION_ASSERTIONS_SILENT;CONFIG_COMPILER_OPTIMIZATION_ASSERTIONS_DISABLE;CONFIG_OPTIMIZATION_ASSERTIONS_DISABLED;CONFIG_COMPILER_ASSERT_NDEBUG_EVALUATE;CONFIG_COMPILER_FLOAT_LIB_FROM_GCCLIB;CONFIG_COMPILER_FLOAT_LIB_FROM_RVFPLIB;CONFIG_COMPILER_OPTIMIZATION_ASSERTION_LEVEL;CONFIG_OPTIMIZATION_ASSERTION_LEVEL;CONFIG_COMPILER_OPTIMIZATION_CHECKS_SILENT;CONFIG_COMPILER_HIDE_PATHS_MACROS;CONFIG_COMPILER_CXX_EXCEPTIONS;CONFIG_CXX_EXCEPTIONS;CONFIG_COMPILER_CXX_RTTI;CONFIG_COMPILER_STACK_CHECK_MODE_NONE;CONFIG_STACK_CHECK_NONE;CONFIG_COMPILER_STACK_CHECK_MODE_NORM;CONFIG_STACK_CHECK_NORM;CONFIG_COMPILER_STACK_CHECK_MODE_STRONG;CONFIG_STACK_CHECK_STRONG;CONFIG_COMPILER_STACK_CHECK_MODE_ALL;CONFIG_STACK_CHECK_ALL;CONFIG_COMPILER_NO_MERGE_CONSTANTS;CONFIG_COMPILER_WARN_WRITE_STRINGS;CONFIG_WARN_WRITE_STRINGS;CONFIG_COMPILER_SAVE_RESTORE_LIBCALLS;CONFIG_COMPILER_DISABLE_DEFAULT_ERRORS;CONFIG_COMPILER_DISABLE_GCC12_WARNINGS;CONFIG_COMPILER_DISABLE_GCC13_WARNINGS;CONFIG_COMPILER_DISABLE_GCC14_WARNINGS;CONFIG_COMPILER_DUMP_RTL_FILES;CONFIG_COMPILER_RT_LIB_GCCLIB;CONFIG_COMPILER_RT_LIB_NAME;CONFIG_COMPILER_ORPHAN_SECTIONS_WARNING;CONFIG_COMPILER_ORPHAN_SECTIONS_PLACE;CONFIG_COMPILER_STATIC_ANALYZER;CONFIG_EFUSE_CUSTOM_TABLE;CONFIG_EFUSE_VIRTUAL;CONFIG_EFUSE_MAX_BLK_LEN;CONFIG_ESP_ERR_TO_NAME_LOOKUP;CONFIG_ESP32P4_REV_MIN_0;CONFIG_ESP32P4_REV_MIN_1;CONFIG_ESP32P4_REV_MIN_100;CONFIG_ESP32P4_REV_MIN_FULL;CONFIG_ESP_REV_MIN_FULL;CONFIG_ESP32P4_REV_MAX_FULL;CONFIG_ESP_REV_MAX_FULL;CONFIG_ESP_EFUSE_BLOCK_REV_MIN_FULL;CONFIG_ESP_EFUSE_BLOCK_REV_MAX_FULL;CONFIG_ESP_MAC_ADDR_UNIVERSE_ETH;CONFIG_ESP_MAC_UNIVERSAL_MAC_ADDRESSES_ONE;CONFIG_ESP_MAC_UNIVERSAL_MAC_ADDRESSES;CONFIG_ESP32P4_UNIVERSAL_MAC_ADDRESSES_ONE;CONFIG_ESP32P4_UNIVERSAL_MAC_ADDRESSES;CONFIG_ESP_MAC_USE_CUSTOM_MAC_AS_BASE_MAC;CONFIG_ESP_SLEEP_FLASH_LEAKAGE_WORKAROUND;CONFIG_ESP_SLEEP_MSPI_NEED_ALL_IO_PU;CONFIG_ESP_SLEEP_GPIO_RESET_WORKAROUND;CONFIG_ESP_SLEEP_WAIT_FLASH_READY_EXTRA_DELAY;CONFIG_ESP_SLEEP_CACHE_SAFE_ASSERTION;CONFIG_ESP_SLEEP_DEBUG;CONFIG_ESP_SLEEP_GPIO_ENABLE_INTERNAL_RESISTORS;CONFIG_RTC_CLK_SRC_INT_RC;CONFIG_RTC_CLK_SRC_EXT_CRYS;CONFIG_RTC_CLK_CAL_CYCLES;CONFIG_RTC_FAST_CLK_SRC_RC_FAST;CONFIG_RTC_FAST_CLK_SRC_XTAL;CONFIG_ESP_PERIPH_CTRL_FUNC_IN_IRAM;CONFIG_PERIPH_CTRL_FUNC_IN_IRAM;CONFIG_ESP_REGI2C_CTRL_FUNC_IN_IRAM;CONFIG_ETM_ENABLE_DEBUG_LOG;CONFIG_GDMA_CTRL_FUNC_IN_IRAM;CONFIG_GDMA_ISR_HANDLER_IN_IRAM;CONFIG_GDMA_OBJ_DRAM_SAFE;CONFIG_GDMA_ENABLE_DEBUG_LOG;CONFIG_GDMA_ISR_IRAM_SAFE;CONFIG_DW_GDMA_ENABLE_DEBUG_LOG;CONFIG_DMA2D_OPERATION_FUNC_IN_IRAM;CONFIG_DMA2D_ISR_IRAM_SAFE;CONFIG_XTAL_FREQ_40;CONFIG_XTAL_FREQ;CONFIG_ESP_SLEEP_KEEP_DCDC_ALWAYS_ON;CONFIG_ESP_SLEEP_DCM_VSET_VAL_IN_SLEEP;CONFIG_ESP_LDO_RESERVE_SPI_NOR_FLASH;CONFIG_ESP_LDO_CHAN_SPI_NOR_FLASH_DOMAIN;CONFIG_ESP_LDO_VOLTAGE_SPI_NOR_FLASH_3300_MV;CONFIG_ESP_LDO_VOLTAGE_SPI_NOR_FLASH_DOMAIN;CONFIG_ESP_LDO_RESERVE_PSRAM;CONFIG_ESP_LDO_CHAN_PSRAM_DOMAIN;CONFIG_ESP_LDO_VOLTAGE_PSRAM_1900_MV;CONFIG_ESP_LDO_VOLTAGE_PSRAM_DOMAIN;CONFIG_ESP_BROWNOUT_DET;CONFIG_BROWNOUT_DET;CONFIG_ESP_BROWNOUT_DET_LVL_SEL_7;CONFIG_BROWNOUT_DET_LVL_SEL_7;CONFIG_ESP_BROWNOUT_DET_LVL_SEL_6;CONFIG_BROWNOUT_DET_LVL_SEL_6;CONFIG_ESP_BROWNOUT_DET_LVL_SEL_5;CONFIG_BROWNOUT_DET_LVL_SEL_5;CONFIG_ESP_BROWNOUT_DET_LVL;CONFIG_BROWNOUT_DET_LVL;CONFIG_ESP_BROWNOUT_USE_INTR;CONFIG_ESP_SYSTEM_BROWNOUT_INTR;CONFIG_ESP_VBAT_INIT_AUTO;CONFIG_ESP_VBAT_WAKEUP_CHIP_ON_VBAT_BROWNOUT;CONFIG_ESP_INTR_IN_IRAM;CONFIG_ESP_ROM_PRINT_IN_IRAM;CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ_360;CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ;CONFIG_CACHE_L2_CACHE_128KB;CONFIG_CACHE_L2_CACHE_256KB;CONFIG_CACHE_L2_CACHE_512KB;CONFIG_CACHE_L2_CACHE_SIZE;CONFIG_CACHE_L2_CACHE_LINE_64B;CONFIG_CACHE_L2_CACHE_LINE_128B;CONFIG_CACHE_L2_CACHE_LINE_SIZE;CONFIG_CACHE_L1_CACHE_LINE_SIZE;CONFIG_ESP_SYSTEM_PANIC_PRINT_HALT;CONFIG_ESP_SYSTEM_PANIC_PRINT_REBOOT;CONFIG_ESP_SYSTEM_PANIC_SILENT_REBOOT;CONFIG_ESP_SYSTEM_PANIC_REBOOT_DELAY_SECONDS;CONFIG_ESP_SYSTEM_RTC_FAST_MEM_AS_HEAP_DEPCHECK;CONFIG_ESP_SYSTEM_ALLOW_RTC_FAST_MEM_AS_HEAP;CONFIG_ESP_SYSTEM_NO_BACKTRACE;CONFIG_ESP_SYSTEM_USE_EH_FRAME;CONFIG_ESP_SYSTEM_USE_FRAME_POINTER;CONFIG_ESP_SYSTEM_PMP_IDRAM_SPLIT;CONFIG_ESP_SYSTEM_PMP_LP_CORE_RESERVE_MEM_EXECUTABLE;CONFIG_ESP_SYSTEM_EVENT_QUEUE_SIZE;CONFIG_SYSTEM_EVENT_QUEUE_SIZE;CONFIG_ESP_SYSTEM_EVENT_TASK_STACK_SIZE;CONFIG_SYSTEM_EVENT_TASK_STACK_SIZE;CONFIG_ESP_MAIN_TASK_STACK_SIZE;CONFIG_MAIN_TASK_STACK_SIZE;CONFIG_ESP_MAIN_TASK_AFFINITY_CPU0;CONFIG_ESP_MAIN_TASK_AFFINITY_CPU1;CONFIG_ESP_MAIN_TASK_AFFINITY_NO_AFFINITY;CONFIG_ESP_MAIN_TASK_AFFINITY;CONFIG_ESP_MINIMAL_SHARED_STACK_SIZE;CONFIG_ESP_CONSOLE_UART_DEFAULT;CONFIG_CONSOLE_UART_DEFAULT;CONFIG_ESP_CONSOLE_USB_SERIAL_JTAG;CONFIG_ESP_CONSOLE_UART_CUSTOM;CONFIG_CONSOLE_UART_CUSTOM;CONFIG_ESP_CONSOLE_NONE;CONFIG_CONSOLE_UART_NONE;CONFIG_ESP_CONSOLE_UART_NONE;CONFIG_ESP_CONSOLE_SECONDARY_NONE;CONFIG_ESP_CONSOLE_SECONDARY_USB_SERIAL_JTAG;CONFIG_ESP_CONSOLE_USB_SERIAL_JTAG_ENABLED;CONFIG_ESP_CONSOLE_UART;CONFIG_CONSOLE_UART;CONFIG_ESP_CONSOLE_UART_NUM;CONFIG_CONSOLE_UART_NUM;CONFIG_ESP_CONSOLE_ROM_SERIAL_PORT_NUM;CONFIG_ESP_CONSOLE_UART_BAUDRATE;CONFIG_CONSOLE_UART_BAUDRATE;CONFIG_ESP_INT_WDT;CONFIG_INT_WDT;CONFIG_ESP_INT_WDT_TIMEOUT_MS;CONFIG_INT_WDT_TIMEOUT_MS;CONFIG_ESP_INT_WDT_CHECK_CPU1;CONFIG_INT_WDT_CHECK_CPU1;CONFIG_ESP_TASK_WDT_EN;CONFIG_ESP_TASK_WDT_INIT;CONFIG_TASK_WDT;CONFIG_ESP_TASK_WDT;CONFIG_ESP_TASK_WDT_PANIC;CONFIG_TASK_WDT_PANIC;CONFIG_ESP_TASK_WDT_TIMEOUT_S;CONFIG_TASK_WDT_TIMEOUT_S;CONFIG_ESP_TASK_WDT_CHECK_IDLE_TASK_CPU0;CONFIG_TASK_WDT_CHECK_IDLE_TASK_CPU0;CONFIG_ESP_TASK_WDT_CHECK_IDLE_TASK_CPU1;CONFIG_TASK_WDT_CHECK_IDLE_TASK_CPU1;CONFIG_ESP_PANIC_HANDLER_IRAM;CONFIG_ESP_DEBUG_STUBS_ENABLE;CONFIG_ESP32_DEBUG_STUBS_ENABLE;CONFIG_ESP_DEBUG_OCDAWARE;CONFIG_ESP_SYSTEM_CHECK_INT_LEVEL_4;CONFIG_ESP_SYSTEM_HW_STACK_GUARD;CONFIG_ESP_SYSTEM_HW_PC_RECORD;CONFIG_ESP_IPC_TASK_STACK_SIZE;CONFIG_IPC_TASK_STACK_SIZE;CONFIG_ESP_IPC_USES_CALLERS_PRIORITY;CONFIG_ESP_IPC_ISR_ENABLE;CONFIG_FREERTOS_UNICORE;CONFIG_FREERTOS_HZ;CONFIG_FREERTOS_CHECK_STACKOVERFLOW_NONE;CONFIG_FREERTOS_CHECK_STACKOVERFLOW_PTRVAL;CONFIG_FREERTOS_CHECK_STACKOVERFLOW_CANARY;CONFIG_FREERTOS_THREAD_LOCAL_STORAGE_POINTERS;CONFIG_FREERTOS_IDLE_TASK_STACKSIZE;CONFIG_FREERTOS_USE_IDLE_HOOK;CONFIG_FREERTOS_USE_TICK_HOOK;CONFIG_FREERTOS_MAX_TASK_NAME_LEN;CONFIG_FREERTOS_ENABLE_BACKWARD_COMPATIBILITY;CONFIG_FREERTOS_USE_TIMERS;CONFIG_FREERTOS_TIMER_SERVICE_TASK_NAME;CONFIG_FREERTOS_TIMER_TASK_AFFINITY_CPU0;CONFIG_FREERTOS_TIMER_TASK_AFFINITY_CPU1;CONFIG_FREERTOS_TIMER_TASK_NO_AFFINITY;CONFIG_FREERTOS_TIMER_SERVICE_TASK_CORE_AFFINITY;CONFIG_FREERTOS_TIMER_TASK_PRIORITY;CONFIG_TIMER_TASK_PRIORITY;CONFIG_FREERTOS_TIMER_TASK_STACK_DEPTH;CONFIG_TIMER_TASK_STACK_DEPTH;CONFIG_FREERTOS_TIMER_QUEUE_LENGTH;CONFIG_TIMER_QUEUE_LENGTH;CONFIG_FREERTOS_QUEUE_REGISTRY_SIZE;CONFIG_FREERTOS_TASK_NOTIFICATION_ARRAY_ENTRIES;CONFIG_FREERTOS_USE_TRACE_FACILITY;CONFIG_FREERTOS_USE_LIST_DATA_INTEGRITY_CHECK_BYTES;CONFIG_FREERTOS_GENERATE_RUN_TIME_STATS;CONFIG_FREERTOS_USE_APPLICATION_TASK_TAG;CONFIG_FREERTOS_TASK_FUNCTION_WRAPPER;CONFIG_FREERTOS_WATCHPOINT_END_OF_STACK;CONFIG_FREERTOS_TLSP_DELETION_CALLBACKS;CONFIG_FREERTOS_TASK_PRE_DELETION_HOOK;CONFIG_FREERTOS_ENABLE_STATIC_TASK_CLEAN_UP;CONFIG_ENABLE_STATIC_TASK_CLEAN_UP_HOOK;CONFIG_FREERTOS_CHECK_MUTEX_GIVEN_BY_OWNER;CONFIG_FREERTOS_ISR_STACKSIZE;CONFIG_FREERTOS_INTERRUPT_BACKTRACE;CONFIG_FREERTOS_TICK_SUPPORT_SYSTIMER;CONFIG_FREERTOS_CORETIMER_SYSTIMER_LVL1;CONFIG_FREERTOS_CORETIMER_SYSTIMER_LVL3;CONFIG_FREERTOS_SYSTICK_USES_SYSTIMER;CONFIG_FREERTOS_PLACE_FUNCTIONS_INTO_FLASH;CONFIG_FREERTOS_CHECK_PORT_CRITICAL_COMPLIANCE;CONFIG_FREERTOS_PORT;CONFIG_FREERTOS_NO_AFFINITY;CONFIG_FREERTOS_SUPPORT_STATIC_ALLOCATION;CONFIG_FREERTOS_DEBUG_OCDAWARE;CONFIG_FREERTOS_ENABLE_TASK_SNAPSHOT;CONFIG_FREERTOS_PLACE_SNAPSHOT_FUNS_INTO_FLASH;CONFIG_FREERTOS_NUMBER_OF_CORES;CONFIG_FREERTOS_IN_IRAM;CONFIG_HAL_ASSERTION_EQUALS_SYSTEM;CONFIG_HAL_ASSERTION_DISABLE;CONFIG_HAL_ASSERTION_SILENT;CONFIG_HAL_ASSERTION_SILIENT;CONFIG_HAL_ASSERTION_ENABLE;CONFIG_HAL_DEFAULT_ASSERTION_LEVEL;CONFIG_HAL_SYSTIMER_USE_ROM_IMPL;CONFIG_HAL_WDT_USE_ROM_IMPL;CONFIG_LOG_VERSION_1;CONFIG_LOG_VERSION_2;CONFIG_LOG_VERSION;CONFIG_LOG_DEFAULT_LEVEL_NONE;CONFIG_LOG_DEFAULT_LEVEL_ERROR;CONFIG_LOG_DEFAULT_LEVEL_WARN;CONFIG_LOG_DEFAULT_LEVEL_INFO;CONFIG_LOG_DEFAULT_LEVEL_DEBUG;CONFIG_LOG_DEFAULT_LEVEL_VERBOSE;CONFIG_LOG_DEFAULT_LEVEL;CONFIG_LOG_MAXIMUM_EQUALS_DEFAULT;CONFIG_LOG_MAXIMUM_LEVEL_DEBUG;CONFIG_LOG_MAXIMUM_LEVEL_VERBOSE;CONFIG_LOG_MAXIMUM_LEVEL;CONFIG_LOG_MASTER_LEVEL;CONFIG_LOG_DYNAMIC_LEVEL_CONTROL;CONFIG_LOG_TAG_LEVEL_IMPL_NONE;CONFIG_LOG_TAG_LEVEL_IMPL_LINKED_LIST;CONFIG_LOG_TAG_LEVEL_IMPL_CACHE_AND_LINKED_LIST;CONFIG_LOG_TAG_LEVEL_CACHE_ARRAY;CONFIG_LOG_TAG_LEVEL_CACHE_BINARY_MIN_HEAP;CONFIG_LOG_TAG_LEVEL_IMPL_CACHE_SIZE;CONFIG_LOG_COLORS;CONFIG_LOG_TIMESTAMP_SOURCE_RTOS;CONFIG_LOG_TIMESTAMP_SOURCE_SYSTEM;CONFIG_LOG_MODE_TEXT_EN;CONFIG_LOG_MODE_TEXT;CONFIG_LOG_IN_IRAM;CONFIG_LIBC_NEWLIB;CONFIG_LIBC_MISC_IN_IRAM;CONFIG_LIBC_LOCKS_PLACE_IN_IRAM;CONFIG_LIBC_NEWLIB_NANO_FORMAT;CONFIG_NEWLIB_NANO_FORMAT;CONFIG_LIBC_TIME_SYSCALL_USE_RTC_HRT;CONFIG_NEWLIB_TIME_SYSCALL_USE_RTC_HRT;CONFIG_LIBC_TIME_SYSCALL_USE_RTC;CONFIG_NEWLIB_TIME_SYSCALL_USE_RTC;CONFIG_LIBC_TIME_SYSCALL_USE_HRT;CONFIG_NEWLIB_TIME_SYSCALL_USE_HRT;CONFIG_LIBC_TIME_SYSCALL_USE_NONE;CONFIG_NEWLIB_TIME_SYSCALL_USE_NONE;CONFIG_LIBC_OPTIMIZED_MISALIGNED_ACCESS;CONFIG_MMU_PAGE_SIZE_64KB;CONFIG_MMU_PAGE_MODE;CONFIG_MMU_PAGE_SIZE;CONFIG_SPI_FLASH_BROWNOUT_RESET_XMC;CONFIG_SPI_FLASH_BROWNOUT_RESET;CONFIG_SPI_FLASH_HPM_ENA;CONFIG_SPI_FLASH_HPM_AUTO;CONFIG_SPI_FLASH_HPM_DIS;CONFIG_SPI_FLASH_HPM_ON;CONFIG_SPI_FLASH_HPM_DC_AUTO;CONFIG_SPI_FLASH_HPM_DC_DISABLE;CONFIG_SPI_FLASH_AUTO_SUSPEND;CONFIG_SPI_FLASH_SUSPEND_TSUS_VAL_US;CONFIG_SPI_FLASH_FORCE_ENABLE_XMC_C_SUSPEND;CONFIG_SPI_FLASH_FORCE_ENABLE_C6_H2_SUSPEND;CONFIG_SPI_FLASH_PLACE_FUNCTIONS_IN_IRAM;CONFIG_SPI_FLASH_VERIFY_WRITE;CONFIG_SPI_FLASH_ENABLE_COUNTERS;CONFIG_SPI_FLASH_ROM_DRIVER_PATCH;CONFIG_SPI_FLASH_DANGEROUS_WRITE_ABORTS;CONFIG_SPI_FLASH_WRITING_DANGEROUS_REGIONS_ABORTS;CONFIG_SPI_FLASH_DANGEROUS_WRITE_FAILS;CONFIG_SPI_FLASH_WRITING_DANGEROUS_REGIONS_FAILS;CONFIG_SPI_FLASH_DANGEROUS_WRITE_ALLOWED;CONFIG_SPI_FLASH_WRITING_DANGEROUS_REGIONS_ALLOWED;CONFIG_SPI_FLASH_BYPASS_BLOCK_ERASE;CONFIG_SPI_FLASH_YIELD_DURING_ERASE;CONFIG_SPI_FLASH_ERASE_YIELD_DURATION_MS;CONFIG_SPI_FLASH_ERASE_YIELD_TICKS;CONFIG_SPI_FLASH_WRITE_CHUNK_SIZE;CONFIG_SPI_FLASH_SIZE_OVERRIDE;CONFIG_SPI_FLASH_CHECK_ERASE_TIMEOUT_DISABLED;CONFIG_SPI_FLASH_OVERRIDE_CHIP_DRIVER_LIST;CONFIG_SPI_FLASH_VENDOR_XMC_SUPPORTED;CONFIG_SPI_FLASH_SUPPORT_ISSI_CHIP;CONFIG_SPI_FLASH_SUPPORT_MXIC_CHIP;CONFIG_SPI_FLASH_SUPPORT_GD_CHIP;CONFIG_SPI_FLASH_SUPPORT_WINBOND_CHIP;CONFIG_SPI_FLASH_SUPPORT_BOYA_CHIP;CONFIG_SPI_FLASH_SUPPORT_TH_CHIP;CONFIG_SPI_FLASH_ENABLE_ENCRYPTED_READ_WRITE;CONFIG_IDF_EXPERIMENTAL_FEATURES)
# List of deprecated options for backward compatibility
set(CONFIG_APP_BUILD_TYPE_ELF_RAM "")
set(CONFIG_NO_BLOBS "")
set(CONFIG_APP_ROLLBACK_ENABLE "")
set(CONFIG_LOG_BOOTLOADER_LEVEL_NONE "")
set(CONFIG_LOG_BOOTLOADER_LEVEL_ERROR "")
set(CONFIG_LOG_BOOTLOADER_LEVEL_WARN "")
set(CONFIG_LOG_BOOTLOADER_LEVEL_INFO "y")
set(CONFIG_LOG_BOOTLOADER_LEVEL_DEBUG "")
set(CONFIG_LOG_BOOTLOADER_LEVEL_VERBOSE "")
set(CONFIG_LOG_BOOTLOADER_LEVEL "3")
set(CONFIG_FLASH_ENCRYPTION_ENABLED "")
set(CONFIG_FLASHMODE_QIO "")
set(CONFIG_FLASHMODE_QOUT "")
set(CONFIG_FLASHMODE_DIO "y")
set(CONFIG_FLASHMODE_DOUT "")
set(CONFIG_MONITOR_BAUD "115200")
set(CONFIG_OPTIMIZATION_LEVEL_DEBUG "y")
set(CONFIG_COMPILER_OPTIMIZATION_LEVEL_DEBUG "y")
set(CONFIG_COMPILER_OPTIMIZATION_DEFAULT "y")
set(CONFIG_OPTIMIZATION_LEVEL_RELEASE "")
set(CONFIG_COMPILER_OPTIMIZATION_LEVEL_RELEASE "")
set(CONFIG_OPTIMIZATION_ASSERTIONS_ENABLED "y")
set(CONFIG_OPTIMIZATION_ASSERTIONS_SILENT "")
set(CONFIG_OPTIMIZATION_ASSERTIONS_DISABLED "")
set(CONFIG_OPTIMIZATION_ASSERTION_LEVEL "2")
set(CONFIG_CXX_EXCEPTIONS "")
set(CONFIG_STACK_CHECK_NONE "y")
set(CONFIG_STACK_CHECK_NORM "")
set(CONFIG_STACK_CHECK_STRONG "")
set(CONFIG_STACK_CHECK_ALL "")
set(CONFIG_WARN_WRITE_STRINGS "")
set(CONFIG_PERIPH_CTRL_FUNC_IN_IRAM "y")
set(CONFIG_BROWNOUT_DET "y")
set(CONFIG_BROWNOUT_DET_LVL_SEL_7 "y")
set(CONFIG_BROWNOUT_DET_LVL_SEL_6 "")
set(CONFIG_BROWNOUT_DET_LVL_SEL_5 "")
set(CONFIG_BROWNOUT_DET_LVL "7")
set(CONFIG_ESP_SYSTEM_BROWNOUT_INTR "y")
set(CONFIG_SYSTEM_EVENT_QUEUE_SIZE "32")
set(CONFIG_SYSTEM_EVENT_TASK_STACK_SIZE "2304")
set(CONFIG_MAIN_TASK_STACK_SIZE "3584")
set(CONFIG_CONSOLE_UART_DEFAULT "y")
set(CONFIG_CONSOLE_UART_CUSTOM "")
set(CONFIG_CONSOLE_UART_NONE "")
set(CONFIG_ESP_CONSOLE_UART_NONE "")
set(CONFIG_CONSOLE_UART "y")
set(CONFIG_CONSOLE_UART_NUM "0")
set(CONFIG_CONSOLE_UART_BAUDRATE "115200")
set(CONFIG_INT_WDT "y")
set(CONFIG_INT_WDT_TIMEOUT_MS "300")
set(CONFIG_INT_WDT_CHECK_CPU1 "y")
set(CONFIG_TASK_WDT "y")
set(CONFIG_ESP_TASK_WDT "y")
set(CONFIG_TASK_WDT_PANIC "")
set(CONFIG_TASK_WDT_TIMEOUT_S "5")
set(CONFIG_TASK_WDT_CHECK_IDLE_TASK_CPU0 "y")
set(CONFIG_TASK_WDT_CHECK_IDLE_TASK_CPU1 "y")
set(CONFIG_ESP32_DEBUG_STUBS_ENABLE "")
set(CONFIG_IPC_TASK_STACK_SIZE "1024")
set(CONFIG_TIMER_TASK_PRIORITY "1")
set(CONFIG_TIMER_TASK_STACK_DEPTH "2048")
set(CONFIG_TIMER_QUEUE_LENGTH "10")
set(CONFIG_ENABLE_STATIC_TASK_CLEAN_UP_HOOK "")
set(CONFIG_HAL_ASSERTION_SILIENT "")
set(CONFIG_NEWLIB_NANO_FORMAT "")
set(CONFIG_NEWLIB_TIME_SYSCALL_USE_RTC_HRT "y")
set(CONFIG_NEWLIB_TIME_SYSCALL_USE_RTC "")
set(CONFIG_NEWLIB_TIME_SYSCALL_USE_HRT "")
set(CONFIG_NEWLIB_TIME_SYSCALL_USE_NONE "")
set(CONFIG_SPI_FLASH_WRITING_DANGEROUS_REGIONS_ABORTS "y")
set(CONFIG_SPI_FLASH_WRITING_DANGEROUS_REGIONS_FAILS "")
set(CONFIG_SPI_FLASH_WRITING_DANGEROUS_REGIONS_ALLOWED "")
