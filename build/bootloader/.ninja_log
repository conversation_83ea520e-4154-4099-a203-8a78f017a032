# ninja log v6
4	60	1753764502901891206	esp-idf/log/CMakeFiles/__idf_log.dir/src/util.c.obj	966aa400b839f70
6	63	1753764502903969074	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_crc.c.obj	9a2033966c075d50
4	72	1753764502901547916	esp-idf/log/CMakeFiles/__idf_log.dir/src/noos/util.c.obj	bb96ff383805a3d
4	75	1753764502901318166	esp-idf/log/CMakeFiles/__idf_log.dir/src/noos/log_lock.c.obj	432f87ace6a8c235
5	78	1753764502902471746	esp-idf/log/CMakeFiles/__idf_log.dir/src/log_print.c.obj	bbb5c3500174d0a3
3	84	1753764502900977917	esp-idf/log/CMakeFiles/__idf_log.dir/src/noos/log_timestamp.c.obj	280cf80b85ecba8c
4	88	1753764502901197250	esp-idf/log/CMakeFiles/__idf_log.dir/src/log_timestamp_common.c.obj	20c485623af4b98b
5	90	1753764502902798745	esp-idf/log/CMakeFiles/__idf_log.dir/src/log.c.obj	3d3455fb577fc597
6	95	1753764502903247576	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_sys.c.obj	b8bd6a6ad73e9ba8
5	97	1753764502902177872	esp-idf/log/CMakeFiles/__idf_log.dir/src/log_format_text.c.obj	b80668bd41d23fcf
4	114	1753764502901438291	esp-idf/log/CMakeFiles/__idf_log.dir/src/buffer/log_buffers.c.obj	61216c654a679b1f
72	134	1753764502969422062	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_efuse.c.obj	feed12429451d555
63	147	1753764502960400384	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_spiflash.c.obj	149d220bacb94039
75	149	1753764502973045508	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_gpio.c.obj	b9fd2ddb19e2de36
60	152	1753764502958005434	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_uart.c.obj	bca6012cb2914a95
84	154	1753764502981768063	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_systimer.c.obj	9505b0dde0ecf3b0
6	168	1753764502903670200	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_print.c.obj	c3b81799acdb1be2
114	179	1753764503011427296	esp-idf/log/liblog.a	305f92dc17638a81
79	181	1753764502976237914	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_regi2c_esp32p4.c.obj	dd6a0b9c93e99bff
95	185	1753764502992327360	esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/src/esp_err_to_name.c.obj	3f1bfb31d9b7b096
90	209	1753764502987347252	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_clic.c.obj	4f3721adc10be988
134	214	1753764503031820686	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32p4/esp_cpu_intr.c.obj	d083e69d00d1da92
149	227	1753764503046769219	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32p4/cpu_region_protect.c.obj	a64878977dee506f
147	228	1753764503044714101	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_memory_utils.c.obj	243c9784d48e2d8f
88	275	1753764502985890674	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_wdt.c.obj	3b1f54991dfc3fb6
214	279	1753764503112040874	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/esp_err.c.obj	8da0ba17f2988fce
168	280	1753764503065835030	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32p4/pmu_param.c.obj	abac2d9164849b76
209	285	1753764503106798559	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32p4/chip_info.c.obj	7da0951c3e79f941
227	292	1753764503124328666	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32p4/esp_efuse_table.c.obj	5727b3cd826017dd
229	313	1753764503126138702	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32p4/esp_efuse_fields.c.obj	a3f39b41854efe57
153	320	1753764503050467706	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32p4/rtc_clk_init.c.obj	178f16bcd2a71055
275	331	1753764503172273630	esp-idf/esp_rom/libesp_rom.a	dc06a7aafd68431e
97	339	1753764502994201604	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/cpu.c.obj	ad52f4a55e489df
280	363	1753764503177508654	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32p4/esp_efuse_utility.c.obj	5066fe3f11f9206c
292	365	1753764503189744030	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_fields.c.obj	e44ee0ff40bdef52
331	370	1753764503228324858	esp-idf/esp_common/libesp_common.a	aa1632f7c159f145
179	382	1753764503076699410	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32p4/pmu_init.c.obj	83bc63c5ec25e2b
285	408	1753764503182857345	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_api.c.obj	b893ee2344fd80c5
370	429	1753764503267659643	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.obj	e521234e9ad75104
279	437	1753764503176621449	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32p4/esp_efuse_rtc_calib.c.obj	70079c77338c9813
382	447	1753764503279649769	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.obj	24a098083c7d4715
185	448	1753764503082624723	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32p4/rtc_time.c.obj	4a61e6d1a6249834
365	448	1753764503262460160	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.obj	12083e79f014c27c
408	479	1753764503305742973	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.obj	4252e3f7e14860cf
363	493	1753764503260464375	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.obj	ec585689f86f8c2
437	505	1753764503334274460	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.obj	89f332019735fe28
447	519	1753764503344930716	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32p4.c.obj	a6c60171a3e58b31
429	552	1753764503326440362	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.obj	ba92baa5fdc0a5af
505	566	1753764503403012854	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.obj	c4a5d8c31d052cdd
340	578	1753764503237134537	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.obj	4f6b76d7cbccb799
449	579	1753764503346157462	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.obj	c7d6ba3c6f8cc284
313	579	1753764503210320835	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_utility.c.obj	ec0a9a37d65fa514
320	581	1753764503217612186	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c.obj	c7d8c3d6b411999f
552	627	1753764503449670614	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_sha.c.obj	316d6cbec8a2e411
581	628	1753764503478909266	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32p4/bootloader_soc.c.obj	b182d800af692d8f
578	634	1753764503475767151	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.obj	f44b778eca94284e
579	643	1753764503476519982	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.obj	ec68031b54d2f0b7
579	680	1753764503476233566	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.obj	2f34eace5e35d215
634	687	1753764503531671922	esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/esp_bootloader_desc.c.obj	84449e996a3e858f
566	689	1753764503463756900	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.obj	3273a92c09b8fc37
479	692	1753764503376542777	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32p4.c.obj	bb7019d323fe505c
628	705	1753764503525777650	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.obj	5c144f41cc822aae
181	718	1753764503078657903	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32p4/pmu_sleep.c.obj	b3276414fa5f7f6f
155	719	1753764503052175034	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32p4/rtc_clk.c.obj	576da682317504c8
692	735	1753764503589795226	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/interrupts.c.obj	f09cca6e51a9faeb
687	741	1753764503585035367	esp-idf/soc/CMakeFiles/__idf_soc.dir/lldesc.c.obj	9b0b0499ff9e1a1d
689	744	1753764503586993735	esp-idf/soc/CMakeFiles/__idf_soc.dir/dport_access_common.c.obj	d57a23a8eacbdccf
643	752	1753764503540711475	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_wrap.c.obj	95e7f7409f46fb57
627	756	1753764503524551279	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32p4/bootloader_esp32p4.c.obj	e2dbec07bbb8e07f
706	761	1753764503603175139	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/gpio_periph.c.obj	b7ad0319c8c7201b
719	764	1753764503616139638	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/uart_periph.c.obj	53064314e19c49a5
720	780	1753764503617118676	esp-idf/esp_hw_support/libesp_hw_support.a	b469aface32f6f66
735	785	1753764503632401250	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/adc_periph.c.obj	ee8192e4bf5d0d42
741	786	1753764503638449854	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/ana_cmpr_periph.c.obj	4388b0ee58d0e162
744	786	1753764503641542802	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/dedic_gpio_periph.c.obj	a914e37f933c77fd
752	792	1753764503649552525	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/debug_probe_periph.c.obj	fd2383ebd913c0a7
761	799	1753764503658340871	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/etm_periph.c.obj	a2b053d3379e4f5e
756	805	1753764503653830011	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/emac_periph.c.obj	26f904f27d64876b
764	811	1753764503661317152	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/gdma_periph.c.obj	5ab125ef1e57be4e
780	822	1753764503678019971	esp-idf/esp_system/libesp_system.a	5f41739e52e0d8bd
792	830	1753764503689210809	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/pcnt_periph.c.obj	6e00d9c8f550ddb7
786	831	1753764503683639994	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/ledc_periph.c.obj	7059e8479617d434
785	832	1753764503682439581	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/dma2d_periph.c.obj	edc6b997a64e7d24
799	837	1753764503696371535	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/rmt_periph.c.obj	7a28a20eb7c97da4
786	839	1753764503683427703	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/spi_periph.c.obj	ab1efcba6753ff11
805	841	1753764503702472139	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/sdm_periph.c.obj	c5148384ccc1733b
811	846	1753764503709098200	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/isp_periph.c.obj	7dde174b83dde317
832	865	1753764503729278340	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/i3c_master_periph.c.obj	df7ad05fa32b79ef
830	866	1753764503727214639	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/i2s_periph.c.obj	ba34b52b0268500e
822	879	1753764503719604373	esp-idf/efuse/libefuse.a	f97283cb78a4ea74
841	879	1753764503739061266	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/lcd_periph.c.obj	220bd7f782ef651e
831	880	1753764503728649717	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/i2c_periph.c.obj	fea9fa4a5cbde9ea
846	883	1753764503743952791	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/mipi_dsi_periph.c.obj	97f07ddddcefe23f
837	885	1753764503735068113	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/temperature_sensor_periph.c.obj	a0e197441237fb47
839	889	1753764503736378150	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/timer_periph.c.obj	224074794535edc1
865	905	1753764503762303104	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/mipi_csi_periph.c.obj	bfbfadb37124435f
866	905	1753764503763289059	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/parlio_periph.c.obj	218a9fe39ff4416b
883	918	1753764503780175128	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/touch_sensor_periph.c.obj	4aa696e03c72aa78
879	922	1753764503776278891	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/mcpwm_periph.c.obj	4022029df8c7cdc2
879	924	1753764503777028596	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/mpi_periph.c.obj	7c6407d99aa7f20
885	927	1753764503782455870	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/twai_periph.c.obj	aee78afd598890c2
880	928	1753764503777697678	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/sdmmc_periph.c.obj	60fe1c2d76429bc6
889	933	1753764503786328690	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/wdt_periph.c.obj	e6780fad6988542
905	946	1753764503802530261	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/usb_dwc_periph.c.obj	8f0b9eabec321d12
905	958	1753764503802816051	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/rtc_io_periph.c.obj	37a10d94cb3f108c
924	962	1753764503821597447	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/power_supply_periph.c.obj	ebe66cdf61cac3a
923	969	1753764503820176410	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/cam_periph.c.obj	ce66c49ae4de8bf8
927	985	1753764503824349479	esp-idf/riscv/CMakeFiles/__idf_riscv.dir/rv_utils.c.obj	fa9daede869e4f78
919	985	1753764503816127715	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32p4/system_retention_periph.c.obj	820680be1219eb4c
493	996	1753764503390903312	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.obj	76d1da9a276702a8
985	1001	1753764503897654524	project_elf_src_esp32p4.c	13cbeb640e6d74cf
985	1001	1753764503897654524	/Users/<USER>/Documents/Arduino/libraries/ESP32-S3-Touch-LCD-4.3-Demo/Arduino/examples/esp32P4/build/bootloader/project_elf_src_esp32p4.c	13cbeb640e6d74cf
933	1004	1753764503830655791	esp-idf/hal/CMakeFiles/__idf_hal.dir/efuse_hal.c.obj	c621bb50a0c6fd3e
928	1012	1753764503825908224	esp-idf/hal/CMakeFiles/__idf_hal.dir/hal_utils.c.obj	ba05d55608df780b
946	1018	1753764503843645248	esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32p4/efuse_hal.c.obj	116896a22f579584
448	1029	1753764503345391756	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.obj	850211be80cc4c68
958	1029	1753764503855304292	esp-idf/hal/CMakeFiles/__idf_hal.dir/lp_timer_hal.c.obj	64843d81edd47856
1001	1034	1753764503898823562	CMakeFiles/bootloader.elf.dir/project_elf_src_esp32p4.c.obj	1fd61e877a21bb9
985	1042	1753764503883041949	esp-idf/main/CMakeFiles/__idf_main.dir/bootloader_start.c.obj	d1897c55c5afb3be
519	1052	1753764503417020390	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.obj	a0ab3d6a0e2a763b
962	1075	1753764503859521819	esp-idf/hal/CMakeFiles/__idf_hal.dir/mmu_hal.c.obj	8de17df3dceee697
969	1087	1753764503866453004	esp-idf/hal/CMakeFiles/__idf_hal.dir/cache_hal.c.obj	84452f8f0dddb27c
1052	1102	1753764503949446267	esp-idf/bootloader_support/libbootloader_support.a	99adeb2ba332817f
1102	1123	1753764503999180600	esp-idf/esp_bootloader_format/libesp_bootloader_format.a	bea510b3c5ee0f5d
1123	1145	1753764504020926026	esp-idf/spi_flash/libspi_flash.a	6bbd894823bd9d6e
680	1207	1753764503577163852	esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/uECC_verify_antifault.c.obj	81ad1b5f1151dc84
1207	1241	1753764504104482579	esp-idf/micro-ecc/libmicro-ecc.a	ff52a78eb539bc3
1241	1321	1753764504138448840	esp-idf/soc/libsoc.a	c13e485099ec6df0
1321	1353	1753764504218602278	esp-idf/riscv/libriscv.a	5d6d6d39103779ff
1353	1393	1753764504250560296	esp-idf/hal/libhal.a	30732d0aacef65ca
1393	1412	1753764504290434037	esp-idf/main/libmain.a	4ec17e4663eef116
1412	1447	1753764504309808263	bootloader.elf	824443831cd268e5
1447	1533	1753764504429719652	.bin_timestamp	c30529093720180e
1447	1533	1753764504429719652	/Users/<USER>/Documents/Arduino/libraries/ESP32-S3-Touch-LCD-4.3-Demo/Arduino/examples/esp32P4/build/bootloader/.bin_timestamp	c30529093720180e
1533	1558	1753764504430274150	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	f0b10af7ddb9e4f4
1533	1558	1753764504430274150	/Users/<USER>/Documents/Arduino/libraries/ESP32-S3-Touch-LCD-4.3-Demo/Arduino/examples/esp32P4/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	f0b10af7ddb9e4f4
4	31	1753764965285956923	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	f0b10af7ddb9e4f4
4	31	1753764965285956923	/Users/<USER>/Documents/Arduino/libraries/ESP32-S3-Touch-LCD-4.3-Demo/Arduino/examples/esp32P4/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	f0b10af7ddb9e4f4
6	33	1753766903078869534	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	f0b10af7ddb9e4f4
6	33	1753766903078869534	/Users/<USER>/Documents/Arduino/libraries/ESP32-S3-Touch-LCD-4.3-Demo/Arduino/examples/esp32P4/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	f0b10af7ddb9e4f4
7	39	1753767641202561132	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	f0b10af7ddb9e4f4
7	39	1753767641202561132	/Users/<USER>/Documents/Arduino/libraries/ESP32-S3-Touch-LCD-4.3-Demo/Arduino/examples/esp32P4/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	f0b10af7ddb9e4f4
6	32	1753768425212418287	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	f0b10af7ddb9e4f4
6	32	1753768425212418287	/Users/<USER>/Documents/Arduino/libraries/ESP32-S3-Touch-LCD-4.3-Demo/Arduino/examples/esp32P4/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	f0b10af7ddb9e4f4
4	30	1753768609057349107	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	f0b10af7ddb9e4f4
4	30	1753768609057349107	/Users/<USER>/Documents/Arduino/libraries/ESP32-S3-Touch-LCD-4.3-Demo/Arduino/examples/esp32P4/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	f0b10af7ddb9e4f4
5	29	1753769572793804869	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	f0b10af7ddb9e4f4
5	29	1753769572793804869	/Users/<USER>/Documents/Arduino/libraries/ESP32-S3-Touch-LCD-4.3-Demo/Arduino/examples/esp32P4/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	f0b10af7ddb9e4f4
