Command: /Users/<USER>/.espressif/python_env/idf5.5_py3.9_env/bin/python /Users/<USER>/esp/v5.5/esp-idf/tools/idf_monitor.py -p /dev/cu.wchusbserial5A7A0449951 -b 115200 --toolchain-prefix riscv32-esp-elf- --target esp32p4 --revision 1 --decode-panic backtrace /Users/<USER>/Documents/Arduino/libraries/ESP32-S3-Touch-LCD-4.3-Demo/Arduino/examples/esp32P4/build/HelloWorld.elf /Users/<USER>/Documents/Arduino/libraries/ESP32-S3-Touch-LCD-4.3-Demo/Arduino/examples/esp32P4/build/bootloader/bootloader.elf -m '/Users/<USER>/.espressif/python_env/idf5.5_py3.9_env/bin/python' '/Users/<USER>/esp/v5.5/esp-idf/tools/idf.py'
ESP-ROM:esp32p4-eco2-20240710
Build:Jul 10 2024
rst:0x1 (POWERON),boot:0x30f (SPI_FAST_FLASH_BOOT)
SPI mode:DIO, clock div:1
load:0x4ff33ce0,len:0x164c
load:0x4ff29ed0,len:0xd64
load:0x4ff2cbd0,len:0x3364
entry 0x4ff29eda
[0;32mI (25) boot: ESP-IDF v5.5-dirty 2nd stage bootloader[0m
[0;32mI (26) boot: compile time Jul 29 2025 12:48:23[0m
[0;32mI (26) boot: Multicore bootloader[0m
[0;32mI (27) boot: chip revision: v1.0[0m
[0;32mI (29) boot: efuse block revision: v0.3[0m
[0;32mI (33) boot.esp32p4: SPI Speed      : 80MHz[0m
[0;32mI (36) boot.esp32p4: SPI Mode       : DIO[0m
[0;32mI (40) boot.esp32p4: SPI Flash Size : 16MB[0m
[0;32mI (44) boot: Enabling RNG early entropy source...[0m
[0;32mI (49) boot: Partition Table:[0m
[0;32mI (51) boot: ## Label            Usage          Type ST Offset   Length[0m
[0;32mI (57) boot:  0 nvs              WiFi data        01 02 00009000 00006000[0m
[0;32mI (64) boot:  1 phy_init         RF data          01 01 0000f000 00001000[0m
[0;32mI (70) boot:  2 factory          factory app      00 00 00010000 00800000[0m
[0;32mI (77) boot:  3 storage          Unknown data     01 82 00810000 00600000[0m
[0;32mI (83) boot:  4 coredump         Unknown data     01 03 00e10000 00010000[0m
[0;32mI (90) boot:  5 nvs_key          NVS keys         01 04 00e20000 00001000[0m
[0;32mI (96) boot:  6 data             Unknown data     01 81 00e30000 001d0000[0m
[0;32mI (104) boot: End of partition table[0m
[0;32mI (107) esp_image: segment 0: paddr=00010020 vaddr=40020020 size=0bdd8h ( 48600) map[0m
[0;32mI (123) esp_image: segment 1: paddr=0001be00 vaddr=30100000 size=00044h (    68) load[0m
[0;32mI (125) esp_image: segment 2: paddr=0001be4c vaddr=4ff00000 size=041cch ( 16844) load[0m
[0;32mI (133) esp_image: segment 3: paddr=00020020 vaddr=40000020 size=1dc9ch (122012) map[0m
[0;32mI (158) esp_image: segment 4: paddr=0003dcc4 vaddr=4ff041cc size=0b7a4h ( 47012) load[0m
[0;32mI (169) esp_image: segment 5: paddr=00049470 vaddr=4ff0f980 size=01da8h (  7592) load[0m
[0;32mI (175) boot: Loaded app from partition at offset 0x10000[0m
[0;32mI (175) boot: Disabling RNG early entropy source...[0m
[0;32mI (186) cpu_start: Multicore app[0m
[0;32mI (195) cpu_start: Pro cpu start user code[0m
[0;32mI (195) cpu_start: cpu freq: 360000000 Hz[0m
[0;32mI (195) app_init: Application information:[0m
[0;32mI (196) app_init: Project name:     HelloWorld[0m
[0;32mI (199) app_init: App version:      1[0m
[0;32mI (203) app_init: Compile time:     Jul 29 2025 12:48:17[0m
[0;32mI (208) app_init: ELF file SHA256:  b5cdcca61...[0m
[0;32mI (212) app_init: ESP-IDF:          v5.5-dirty[0m
[0;32mI (216) efuse_init: Min chip rev:     v0.1[0m
[0;32mI (220) efuse_init: Max chip rev:     v1.99 [0m
[0;32mI (224) efuse_init: Chip rev:         v1.0[0m
[0;32mI (228) heap_init: Initializing. RAM available for dynamic allocation:[0m
[0;32mI (234) heap_init: At 4FF12E90 len 00028130 (160 KiB): RAM[0m
[0;32mI (239) heap_init: At 4FF3AFC0 len 00004BF0 (18 KiB): RAM[0m
[0;32mI (245) heap_init: At 4FF40000 len 00060000 (384 KiB): RAM[0m
[0;32mI (250) heap_init: At 50108080 len 00007F80 (31 KiB): RTCRAM[0m
[0;32mI (255) heap_init: At 30100044 len 00001FBC (7 KiB): TCM[0m
[0;33mW (261) spi_flash: GigaDevice detected but related driver is not linked, please check option `SPI_FLASH_SUPPORT_GD_CHIP`[0m
[0;32mI (271) spi_flash: detected chip: generic[0m
[0;32mI (275) spi_flash: flash io: dio[0m
[0;32mI (278) main_task: Started on CPU0[0m
[0;32mI (318) main_task: Calling app_main()[0m
[0;32mI (318) RS485_LOOPBACK: === ESP32-P4 RS485回环测试程序 ===[0m
[0;32mI (318) RS485_LOOPBACK: 🎯 测试目标: 验证HVD75扩展板回环功能[0m
[0;32mI (318) RS485_LOOPBACK: 📋 测试条件:[0m
[0;32mI (328) RS485_LOOPBACK:    - HVD75扩展板A+和B-已短接[0m
[0;32mI (328) RS485_LOOPBACK:    - ESP32-P4 GPIO21 → 扩展板RX (TX_PIN)[0m
[0;32mI (338) RS485_LOOPBACK:    - ESP32-P4 GPIO20 ← 扩展板TX (RX_PIN)[0m
[0;32mI (348) RS485_LOOPBACK:    - 3.3V供电正常[0m
[0;32mI (348) RS485_LOOPBACK: 🚀 开始测试...[0m

[0;32mI (348) RS485_LOOPBACK: 🔧 RS485回环测试开始[0m
[0;32mI (358) RS485_LOOPBACK:    UART: 2, TX: GPIO21, RX: GPIO20, 波特率: 9600[0m
[0;32mI (368) RS485_LOOPBACK:    前提条件: HVD75扩展板A+和B-已短接[0m
[0;32mI (368) RS485_LOOPBACK: ✅ UART配置完成[0m
[0;32mI (378) RS485_LOOPBACK: === 第1次回环测试 ===[0m
[0;32mI (378) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (388) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (388) main_task: Returned from app_main()[0m
[0;32mI (388) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (418) RS485_LOOPBACK: 📥 接收到 5 字节，总计 5/5[0m
[0;32mI (418) RS485_LOOPBACK: 📥 接收完成: "HELLO" (5字节)[0m
[0;32mI (418) RS485_LOOPBACK: ⏱️  总耗时: 29 ms[0m
[0;32mI (418) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (428) RS485_LOOPBACK: 📊 统计: 1/1 成功 (100.0%)[0m
[0;32mI (428) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (3438) RS485_LOOPBACK: === 第2次回环测试 ===[0m
[0;32mI (3438) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (3438) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (3448) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (3468) RS485_LOOPBACK: 📥 接收到 10 字节，总计 10/10[0m
[0;32mI (3468) RS485_LOOPBACK: 📥 接收完成: "RS485_TEST" (10字节)[0m
[0;32mI (3468) RS485_LOOPBACK: ⏱️  总耗时: 29 ms[0m
[0;32mI (3468) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (3478) RS485_LOOPBACK: 📊 统计: 2/2 成功 (100.0%)[0m
[0;32mI (3478) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (6488) RS485_LOOPBACK: === 第3次回环测试 ===[0m
[0;32mI (6488) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (6488) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (6498) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (6518) RS485_LOOPBACK: 📥 接收到 11 字节，总计 11/11[0m
[0;32mI (6518) RS485_LOOPBACK: 📥 接收完成: "LOOPBACK_OK" (11字节)[0m
[0;32mI (6518) RS485_LOOPBACK: ⏱️  总耗时: 29 ms[0m
[0;32mI (6518) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (6528) RS485_LOOPBACK: 📊 统计: 3/3 成功 (100.0%)[0m
[0;32mI (6528) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (9538) RS485_LOOPBACK: === 第4次回环测试 ===[0m
[0;32mI (9538) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (9538) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (9548) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (9568) RS485_LOOPBACK: 📥 接收到 13 字节，总计 13/13[0m
[0;32mI (9568) RS485_LOOPBACK: 📥 接收完成: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (9568) RS485_LOOPBACK: ⏱️  总耗时: 29 ms[0m
[0;32mI (9568) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (9578) RS485_LOOPBACK: 📊 统计: 4/4 成功 (100.0%)[0m
[0;32mI (9578) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (12588) RS485_LOOPBACK: === 第5次回环测试 ===[0m
[0;32mI (12588) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (12588) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (12598) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (12618) RS485_LOOPBACK: 📥 接收到 16 字节，总计 16/16[0m
[0;32mI (12618) RS485_LOOPBACK: 📥 接收完成: "0123456789ABCDEF" (16字节)[0m
[0;32mI (12618) RS485_LOOPBACK: ⏱️  总耗时: 29 ms[0m
[0;32mI (12618) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (12628) RS485_LOOPBACK: 📊 统计: 5/5 成功 (100.0%)[0m
[0;32mI (12628) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (15638) RS485_LOOPBACK: === 第6次回环测试 ===[0m
[0;32mI (15638) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (15638) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (15638) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (15658) RS485_LOOPBACK: 📥 接收到 5 字节，总计 5/5[0m
[0;32mI (15658) RS485_LOOPBACK: 📥 接收完成: "HELLO" (5字节)[0m
[0;32mI (15658) RS485_LOOPBACK: ⏱️  总耗时: 19 ms[0m
[0;32mI (15658) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (15668) RS485_LOOPBACK: 📊 统计: 6/6 成功 (100.0%)[0m
[0;32mI (15668) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (18678) RS485_LOOPBACK: === 第7次回环测试 ===[0m
[0;32mI (18678) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (18678) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (18688) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (18708) RS485_LOOPBACK: 📥 接收到 10 字节，总计 10/10[0m
[0;32mI (18708) RS485_LOOPBACK: 📥 接收完成: "RS485_TEST" (10字节)[0m
[0;32mI (18708) RS485_LOOPBACK: ⏱️  总耗时: 29 ms[0m
[0;32mI (18708) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (18718) RS485_LOOPBACK: 📊 统计: 7/7 成功 (100.0%)[0m
[0;32mI (18718) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (21728) RS485_LOOPBACK: === 第8次回环测试 ===[0m
[0;32mI (21728) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (21728) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (21738) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (21758) RS485_LOOPBACK: 📥 接收到 11 字节，总计 11/11[0m
[0;32mI (21758) RS485_LOOPBACK: 📥 接收完成: "LOOPBACK_OK" (11字节)[0m
[0;32mI (21758) RS485_LOOPBACK: ⏱️  总耗时: 29 ms[0m
[0;32mI (21758) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (21768) RS485_LOOPBACK: 📊 统计: 8/8 成功 (100.0%)[0m
[0;32mI (21768) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (24778) RS485_LOOPBACK: === 第9次回环测试 ===[0m
[0;32mI (24778) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (24778) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (24788) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (24808) RS485_LOOPBACK: 📥 接收到 13 字节，总计 13/13[0m
[0;32mI (24808) RS485_LOOPBACK: 📥 接收完成: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (24808) RS485_LOOPBACK: ⏱️  总耗时: 29 ms[0m
[0;32mI (24808) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (24818) RS485_LOOPBACK: 📊 统计: 9/9 成功 (100.0%)[0m
[0;32mI (24818) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (27828) RS485_LOOPBACK: === 第10次回环测试 ===[0m
[0;32mI (27828) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (27828) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (27838) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (27858) RS485_LOOPBACK: 📥 接收到 16 字节，总计 16/16[0m
[0;32mI (27858) RS485_LOOPBACK: 📥 接收完成: "0123456789ABCDEF" (16字节)[0m
[0;32mI (27858) RS485_LOOPBACK: ⏱️  总耗时: 29 ms[0m
[0;32mI (27858) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (27868) RS485_LOOPBACK: 📊 统计: 10/10 成功 (100.0%)[0m
[0;32mI (27868) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (30878) RS485_LOOPBACK: === 第11次回环测试 ===[0m
[0;32mI (30878) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (30878) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (30878) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (30898) RS485_LOOPBACK: 📥 接收到 5 字节，总计 5/5[0m
[0;32mI (30898) RS485_LOOPBACK: 📥 接收完成: "HELLO" (5字节)[0m
[0;32mI (30898) RS485_LOOPBACK: ⏱️  总耗时: 19 ms[0m
[0;32mI (30898) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (30908) RS485_LOOPBACK: 📊 统计: 11/11 成功 (100.0%)[0m
[0;32mI (30908) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (33918) RS485_LOOPBACK: === 第12次回环测试 ===[0m
[0;32mI (33918) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (33918) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (33928) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (33948) RS485_LOOPBACK: 📥 接收到 10 字节，总计 10/10[0m
[0;32mI (33948) RS485_LOOPBACK: 📥 接收完成: "RS485_TEST" (10字节)[0m
[0;32mI (33948) RS485_LOOPBACK: ⏱️  总耗时: 29 ms[0m
[0;32mI (33948) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (33958) RS485_LOOPBACK: 📊 统计: 12/12 成功 (100.0%)[0m
[0;32mI (33958) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (36968) RS485_LOOPBACK: === 第13次回环测试 ===[0m
[0;32mI (36968) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (36968) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (36978) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (36998) RS485_LOOPBACK: 📥 接收到 11 字节，总计 11/11[0m
[0;32mI (36998) RS485_LOOPBACK: 📥 接收完成: "LOOPBACK_OK" (11字节)[0m
[0;32mI (36998) RS485_LOOPBACK: ⏱️  总耗时: 29 ms[0m
[0;32mI (36998) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (37008) RS485_LOOPBACK: 📊 统计: 13/13 成功 (100.0%)[0m
[0;32mI (37008) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (40018) RS485_LOOPBACK: === 第14次回环测试 ===[0m
[0;32mI (40018) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (40018) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (40028) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (40048) RS485_LOOPBACK: 📥 接收到 13 字节，总计 13/13[0m
[0;32mI (40048) RS485_LOOPBACK: 📥 接收完成: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (40048) RS485_LOOPBACK: ⏱️  总耗时: 29 ms[0m
[0;32mI (40048) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (40058) RS485_LOOPBACK: 📊 统计: 14/14 成功 (100.0%)[0m
[0;32mI (40058) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (43068) RS485_LOOPBACK: === 第15次回环测试 ===[0m
[0;32mI (43068) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (43068) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (43078) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (43098) RS485_LOOPBACK: 📥 接收到 16 字节，总计 16/16[0m
[0;32mI (43098) RS485_LOOPBACK: 📥 接收完成: "0123456789ABCDEF" (16字节)[0m
[0;32mI (43098) RS485_LOOPBACK: ⏱️  总耗时: 29 ms[0m
[0;32mI (43098) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (43108) RS485_LOOPBACK: 📊 统计: 15/15 成功 (100.0%)[0m
[0;32mI (43108) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (46118) RS485_LOOPBACK: === 第16次回环测试 ===[0m
[0;32mI (46118) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (46118) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (46118) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (46138) RS485_LOOPBACK: 📥 接收到 5 字节，总计 5/5[0m
[0;32mI (46138) RS485_LOOPBACK: 📥 接收完成: "HELLO" (5字节)[0m
[0;32mI (46138) RS485_LOOPBACK: ⏱️  总耗时: 19 ms[0m
[0;32mI (46138) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (46148) RS485_LOOPBACK: 📊 统计: 16/16 成功 (100.0%)[0m
[0;32mI (46148) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (49158) RS485_LOOPBACK: === 第17次回环测试 ===[0m
[0;32mI (49158) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (49158) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (49168) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (49188) RS485_LOOPBACK: 📥 接收到 10 字节，总计 10/10[0m
[0;32mI (49188) RS485_LOOPBACK: 📥 接收完成: "RS485_TEST" (10字节)[0m
[0;32mI (49188) RS485_LOOPBACK: ⏱️  总耗时: 29 ms[0m
[0;32mI (49188) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (49198) RS485_LOOPBACK: 📊 统计: 17/17 成功 (100.0%)[0m
[0;32mI (49198) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (52208) RS485_LOOPBACK: === 第18次回环测试 ===[0m
[0;32mI (52208) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (52208) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (52218) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (52238) RS485_LOOPBACK: 📥 接收到 11 字节，总计 11/11[0m
[0;32mI (52238) RS485_LOOPBACK: 📥 接收完成: "LOOPBACK_OK" (11字节)[0m
[0;32mI (52238) RS485_LOOPBACK: ⏱️  总耗时: 29 ms[0m
[0;32mI (52238) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (52248) RS485_LOOPBACK: 📊 统计: 18/18 成功 (100.0%)[0m
[0;32mI (52248) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (55258) RS485_LOOPBACK: === 第19次回环测试 ===[0m
[0;32mI (55258) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (55258) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (55268) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (55288) RS485_LOOPBACK: 📥 接收到 13 字节，总计 13/13[0m
[0;32mI (55288) RS485_LOOPBACK: 📥 接收完成: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (55288) RS485_LOOPBACK: ⏱️  总耗时: 29 ms[0m
[0;32mI (55288) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (55298) RS485_LOOPBACK: 📊 统计: 19/19 成功 (100.0%)[0m
[0;32mI (55298) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (58308) RS485_LOOPBACK: === 第20次回环测试 ===[0m
[0;32mI (58308) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (58308) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (58318) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (58338) RS485_LOOPBACK: 📥 接收到 16 字节，总计 16/16[0m
[0;32mI (58338) RS485_LOOPBACK: 📥 接收完成: "0123456789ABCDEF" (16字节)[0m
[0;32mI (58338) RS485_LOOPBACK: ⏱️  总耗时: 29 ms[0m
[0;32mI (58338) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (58348) RS485_LOOPBACK: 📊 统计: 20/20 成功 (100.0%)[0m
[0;32mI (58348) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (61358) RS485_LOOPBACK: === 第21次回环测试 ===[0m
[0;32mI (61358) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (61358) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (61358) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (61378) RS485_LOOPBACK: 📥 接收到 5 字节，总计 5/5[0m
[0;32mI (61378) RS485_LOOPBACK: 📥 接收完成: "HELLO" (5字节)[0m
[0;32mI (61378) RS485_LOOPBACK: ⏱️  总耗时: 19 ms[0m
[0;32mI (61378) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (61388) RS485_LOOPBACK: 📊 统计: 21/21 成功 (100.0%)[0m
[0;32mI (61388) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (64398) RS485_LOOPBACK: === 第22次回环测试 ===[0m
[0;32mI (64398) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (64398) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (64408) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (64428) RS485_LOOPBACK: 📥 接收到 10 字节，总计 10/10[0m
[0;32mI (64428) RS485_LOOPBACK: 📥 接收完成: "RS485_TEST" (10字节)[0m
[0;32mI (64428) RS485_LOOPBACK: ⏱️  总耗时: 29 ms[0m
[0;32mI (64428) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (64438) RS485_LOOPBACK: 📊 统计: 22/22 成功 (100.0%)[0m
[0;32mI (64438) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (67448) RS485_LOOPBACK: === 第23次回环测试 ===[0m
[0;32mI (67448) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (67448) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (67458) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (67478) RS485_LOOPBACK: 📥 接收到 11 字节，总计 11/11[0m
[0;32mI (67478) RS485_LOOPBACK: 📥 接收完成: "LOOPBACK_OK" (11字节)[0m
[0;32mI (67478) RS485_LOOPBACK: ⏱️  总耗时: 29 ms[0m
[0;32mI (67478) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (67488) RS485_LOOPBACK: 📊 统计: 23/23 成功 (100.0%)[0m
[0;32mI (67488) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (70498) RS485_LOOPBACK: === 第24次回环测试 ===[0m
[0;32mI (70498) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (70498) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (70508) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (70528) RS485_LOOPBACK: 📥 接收到 13 字节，总计 13/13[0m
[0;32mI (70528) RS485_LOOPBACK: 📥 接收完成: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (70528) RS485_LOOPBACK: ⏱️  总耗时: 29 ms[0m
[0;32mI (70528) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (70538) RS485_LOOPBACK: 📊 统计: 24/24 成功 (100.0%)[0m
[0;32mI (70538) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (73548) RS485_LOOPBACK: === 第25次回环测试 ===[0m
[0;32mI (73548) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (73548) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (73558) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (73578) RS485_LOOPBACK: 📥 接收到 16 字节，总计 16/16[0m
[0;32mI (73578) RS485_LOOPBACK: 📥 接收完成: "0123456789ABCDEF" (16字节)[0m
[0;32mI (73578) RS485_LOOPBACK: ⏱️  总耗时: 29 ms[0m
[0;32mI (73578) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (73588) RS485_LOOPBACK: 📊 统计: 25/25 成功 (100.0%)[0m
[0;32mI (73588) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (76598) RS485_LOOPBACK: === 第26次回环测试 ===[0m
[0;32mI (76598) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (76598) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (76598) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (76618) RS485_LOOPBACK: 📥 接收到 5 字节，总计 5/5[0m
[0;32mI (76618) RS485_LOOPBACK: 📥 接收完成: "HELLO" (5字节)[0m
[0;32mI (76618) RS485_LOOPBACK: ⏱️  总耗时: 19 ms[0m
[0;32mI (76618) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (76628) RS485_LOOPBACK: 📊 统计: 26/26 成功 (100.0%)[0m
[0;32mI (76628) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (79638) RS485_LOOPBACK: === 第27次回环测试 ===[0m
[0;32mI (79638) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (79638) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (79648) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (79668) RS485_LOOPBACK: 📥 接收到 10 字节，总计 10/10[0m
[0;32mI (79668) RS485_LOOPBACK: 📥 接收完成: "RS485_TEST" (10字节)[0m
[0;32mI (79668) RS485_LOOPBACK: ⏱️  总耗时: 29 ms[0m
[0;32mI (79668) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (79678) RS485_LOOPBACK: 📊 统计: 27/27 成功 (100.0%)[0m
[0;32mI (79678) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (82688) RS485_LOOPBACK: === 第28次回环测试 ===[0m
[0;32mI (82688) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (82688) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (82698) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (82718) RS485_LOOPBACK: 📥 接收到 11 字节，总计 11/11[0m
[0;32mI (82718) RS485_LOOPBACK: 📥 接收完成: "LOOPBACK_OK" (11字节)[0m
[0;32mI (82718) RS485_LOOPBACK: ⏱️  总耗时: 29 ms[0m
[0;32mI (82718) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (82728) RS485_LOOPBACK: 📊 统计: 28/28 成功 (100.0%)[0m
[0;32mI (82728) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (85738) RS485_LOOPBACK: === 第29次回环测试 ===[0m
[0;32mI (85738) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (85738) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (85748) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (85768) RS485_LOOPBACK: 📥 接收到 13 字节，总计 13/13[0m
[0;32mI (85768) RS485_LOOPBACK: 📥 接收完成: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (85768) RS485_LOOPBACK: ⏱️  总耗时: 29 ms[0m
[0;32mI (85768) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (85778) RS485_LOOPBACK: 📊 统计: 29/29 成功 (100.0%)[0m
[0;32mI (85778) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (88788) RS485_LOOPBACK: === 第30次回环测试 ===[0m
[0;32mI (88788) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (88788) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (88798) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (88818) RS485_LOOPBACK: 📥 接收到 16 字节，总计 16/16[0m
[0;32mI (88818) RS485_LOOPBACK: 📥 接收完成: "0123456789ABCDEF" (16字节)[0m
[0;32mI (88818) RS485_LOOPBACK: ⏱️  总耗时: 29 ms[0m
[0;32mI (88818) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (88828) RS485_LOOPBACK: 📊 统计: 30/30 成功 (100.0%)[0m
[0;32mI (88828) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (91838) RS485_LOOPBACK: === 第31次回环测试 ===[0m
[0;32mI (91838) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (91838) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (91838) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (91858) RS485_LOOPBACK: 📥 接收到 5 字节，总计 5/5[0m
[0;32mI (91858) RS485_LOOPBACK: 📥 接收完成: "HELLO" (5字节)[0m
[0;32mI (91858) RS485_LOOPBACK: ⏱️  总耗时: 19 ms[0m
[0;32mI (91858) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (91868) RS485_LOOPBACK: 📊 统计: 31/31 成功 (100.0%)[0m
[0;32mI (91868) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (94878) RS485_LOOPBACK: === 第32次回环测试 ===[0m
[0;32mI (94878) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (94878) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (94888) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (94908) RS485_LOOPBACK: 📥 接收到 10 字节，总计 10/10[0m
[0;32mI (94908) RS485_LOOPBACK: 📥 接收完成: "RS485_TEST" (10字节)[0m
[0;32mI (94908) RS485_LOOPBACK: ⏱️  总耗时: 29 ms[0m
[0;32mI (94908) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (94918) RS485_LOOPBACK: 📊 统计: 32/32 成功 (100.0%)[0m
[0;32mI (94918) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (97928) RS485_LOOPBACK: === 第33次回环测试 ===[0m
[0;32mI (97928) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (97928) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (97938) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (97958) RS485_LOOPBACK: 📥 接收到 11 字节，总计 11/11[0m
[0;32mI (97958) RS485_LOOPBACK: 📥 接收完成: "LOOPBACK_OK" (11字节)[0m
[0;32mI (97958) RS485_LOOPBACK: ⏱️  总耗时: 29 ms[0m
[0;32mI (97958) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (97968) RS485_LOOPBACK: 📊 统计: 33/33 成功 (100.0%)[0m
[0;32mI (97968) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (100978) RS485_LOOPBACK: === 第34次回环测试 ===[0m
[0;32mI (100978) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (100978) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (100988) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (101008) RS485_LOOPBACK: 📥 接收到 13 字节，总计 13/13[0m
[0;32mI (101008) RS485_LOOPBACK: 📥 接收完成: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (101008) RS485_LOOPBACK: ⏱️  总耗时: 29 ms[0m
[0;32mI (101008) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (101018) RS485_LOOPBACK: 📊 统计: 34/34 成功 (100.0%)[0m
[0;32mI (101018) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (104028) RS485_LOOPBACK: === 第35次回环测试 ===[0m
[0;32mI (104028) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (104028) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (104038) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (104058) RS485_LOOPBACK: 📥 接收到 16 字节，总计 16/16[0m
[0;32mI (104058) RS485_LOOPBACK: 📥 接收完成: "0123456789ABCDEF" (16字节)[0m
[0;32mI (104058) RS485_LOOPBACK: ⏱️  总耗时: 29 ms[0m
[0;32mI (104058) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (104068) RS485_LOOPBACK: 📊 统计: 35/35 成功 (100.0%)[0m
[0;32mI (104068) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (107078) RS485_LOOPBACK: === 第36次回环测试 ===[0m
[0;32mI (107078) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (107078) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (107078) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (107098) RS485_LOOPBACK: 📥 接收到 5 字节，总计 5/5[0m
[0;32mI (107098) RS485_LOOPBACK: 📥 接收完成: "HELLO" (5字节)[0m
[0;32mI (107098) RS485_LOOPBACK: ⏱️  总耗时: 19 ms[0m
[0;32mI (107098) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (107108) RS485_LOOPBACK: 📊 统计: 36/36 成功 (100.0%)[0m
[0;32mI (107108) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (110118) RS485_LOOPBACK: === 第37次回环测试 ===[0m
[0;32mI (110118) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (110118) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (110128) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (110148) RS485_LOOPBACK: 📥 接收到 10 字节，总计 10/10[0m
[0;32mI (110148) RS485_LOOPBACK: 📥 接收完成: "RS485_TEST" (10字节)[0m
[0;32mI (110148) RS485_LOOPBACK: ⏱️  总耗时: 29 ms[0m
[0;32mI (110148) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (110158) RS485_LOOPBACK: 📊 统计: 37/37 成功 (100.0%)[0m
[0;32mI (110158) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (113168) RS485_LOOPBACK: === 第38次回环测试 ===[0m
[0;32mI (113168) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (113168) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (113178) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (113198) RS485_LOOPBACK: 📥 接收到 11 字节，总计 11/11[0m
[0;32mI (113198) RS485_LOOPBACK: 📥 接收完成: "LOOPBACK_OK" (11字节)[0m
[0;32mI (113198) RS485_LOOPBACK: ⏱️  总耗时: 29 ms[0m
[0;32mI (113198) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (113208) RS485_LOOPBACK: 📊 统计: 38/38 成功 (100.0%)[0m
[0;32mI (113208) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (116218) RS485_LOOPBACK: === 第39次回环测试 ===[0m
[0;32mI (116218) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (116218) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (116228) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (116248) RS485_LOOPBACK: 📥 接收到 13 字节，总计 13/13[0m
[0;32mI (116248) RS485_LOOPBACK: 📥 接收完成: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (116248) RS485_LOOPBACK: ⏱️  总耗时: 29 ms[0m
[0;32mI (116248) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (116258) RS485_LOOPBACK: 📊 统计: 39/39 成功 (100.0%)[0m
[0;32mI (116258) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (119268) RS485_LOOPBACK: === 第40次回环测试 ===[0m
[0;32mI (119268) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (119268) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (119278) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (119298) RS485_LOOPBACK: 📥 接收到 16 字节，总计 16/16[0m
[0;32mI (119298) RS485_LOOPBACK: 📥 接收完成: "0123456789ABCDEF" (16字节)[0m
[0;32mI (119298) RS485_LOOPBACK: ⏱️  总耗时: 29 ms[0m
[0;32mI (119298) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (119308) RS485_LOOPBACK: 📊 统计: 40/40 成功 (100.0%)[0m
[0;32mI (119308) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (122318) RS485_LOOPBACK: === 第41次回环测试 ===[0m
[0;32mI (122318) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (122318) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (122318) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (122338) RS485_LOOPBACK: 📥 接收到 5 字节，总计 5/5[0m
[0;32mI (122338) RS485_LOOPBACK: 📥 接收完成: "HELLO" (5字节)[0m
[0;32mI (122338) RS485_LOOPBACK: ⏱️  总耗时: 19 ms[0m
[0;32mI (122338) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (122348) RS485_LOOPBACK: 📊 统计: 41/41 成功 (100.0%)[0m
[0;32mI (122348) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (125358) RS485_LOOPBACK: === 第42次回环测试 ===[0m
[0;32mI (125358) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (125358) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (125368) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (125388) RS485_LOOPBACK: 📥 接收到 10 字节，总计 10/10[0m
[0;32mI (125388) RS485_LOOPBACK: 📥 接收完成: "RS485_TEST" (10字节)[0m
[0;32mI (125388) RS485_LOOPBACK: ⏱️  总耗时: 29 ms[0m
[0;32mI (125388) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (125398) RS485_LOOPBACK: 📊 统计: 42/42 成功 (100.0%)[0m
[0;32mI (125398) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (128408) RS485_LOOPBACK: === 第43次回环测试 ===[0m
[0;32mI (128408) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (128408) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (128418) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (128438) RS485_LOOPBACK: 📥 接收到 11 字节，总计 11/11[0m
[0;32mI (128438) RS485_LOOPBACK: 📥 接收完成: "LOOPBACK_OK" (11字节)[0m
[0;32mI (128438) RS485_LOOPBACK: ⏱️  总耗时: 29 ms[0m
[0;32mI (128438) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (128448) RS485_LOOPBACK: 📊 统计: 43/43 成功 (100.0%)[0m
[0;32mI (128448) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (131458) RS485_LOOPBACK: === 第44次回环测试 ===[0m
[0;32mI (131458) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (131458) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (131468) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (131488) RS485_LOOPBACK: 📥 接收到 13 字节，总计 13/13[0m
[0;32mI (131488) RS485_LOOPBACK: 📥 接收完成: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (131488) RS485_LOOPBACK: ⏱️  总耗时: 29 ms[0m
[0;32mI (131488) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (131498) RS485_LOOPBACK: 📊 统计: 44/44 成功 (100.0%)[0m
[0;32mI (131498) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (134508) RS485_LOOPBACK: === 第45次回环测试 ===[0m
[0;32mI (134508) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (134508) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (134518) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (134538) RS485_LOOPBACK: 📥 接收到 16 字节，总计 16/16[0m
[0;32mI (134538) RS485_LOOPBACK: 📥 接收完成: "0123456789ABCDEF" (16字节)[0m
[0;32mI (134538) RS485_LOOPBACK: ⏱️  总耗时: 29 ms[0m
[0;32mI (134538) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (134548) RS485_LOOPBACK: 📊 统计: 45/45 成功 (100.0%)[0m
[0;32mI (134548) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (137558) RS485_LOOPBACK: === 第46次回环测试 ===[0m
[0;32mI (137558) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (137558) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (137558) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (137578) RS485_LOOPBACK: 📥 接收到 5 字节，总计 5/5[0m
[0;32mI (137578) RS485_LOOPBACK: 📥 接收完成: "HELLO" (5字节)[0m
[0;32mI (137578) RS485_LOOPBACK: ⏱️  总耗时: 19 ms[0m
[0;32mI (137578) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (137588) RS485_LOOPBACK: 📊 统计: 46/46 成功 (100.0%)[0m
[0;32mI (137588) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (140598) RS485_LOOPBACK: === 第47次回环测试 ===[0m
[0;32mI (140598) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (140598) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (140608) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (140628) RS485_LOOPBACK: 📥 接收到 10 字节，总计 10/10[0m
[0;32mI (140628) RS485_LOOPBACK: 📥 接收完成: "RS485_TEST" (10字节)[0m
[0;32mI (140628) RS485_LOOPBACK: ⏱️  总耗时: 29 ms[0m
[0;32mI (140628) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (140638) RS485_LOOPBACK: 📊 统计: 47/47 成功 (100.0%)[0m
[0;32mI (140638) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (143648) RS485_LOOPBACK: === 第48次回环测试 ===[0m
[0;32mI (143648) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (143648) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (143658) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (143678) RS485_LOOPBACK: 📥 接收到 11 字节，总计 11/11[0m
[0;32mI (143678) RS485_LOOPBACK: 📥 接收完成: "LOOPBACK_OK" (11字节)[0m
[0;32mI (143678) RS485_LOOPBACK: ⏱️  总耗时: 29 ms[0m
[0;32mI (143678) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (143688) RS485_LOOPBACK: 📊 统计: 48/48 成功 (100.0%)[0m
[0;32mI (143688) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (146698) RS485_LOOPBACK: === 第49次回环测试 ===[0m
[0;32mI (146698) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (146698) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (146708) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (146728) RS485_LOOPBACK: 📥 接收到 13 字节，总计 13/13[0m
[0;32mI (146728) RS485_LOOPBACK: 📥 接收完成: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (146728) RS485_LOOPBACK: ⏱️  总耗时: 29 ms[0m
[0;32mI (146728) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (146738) RS485_LOOPBACK: 📊 统计: 49/49 成功 (100.0%)[0m
[0;32mI (146738) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (149748) RS485_LOOPBACK: === 第50次回环测试 ===[0m
[0;32mI (149748) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (149748) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (149758) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (149778) RS485_LOOPBACK: 📥 接收到 16 字节，总计 16/16[0m
[0;32mI (149778) RS485_LOOPBACK: 📥 接收完成: "0123456789ABCDEF" (16字节)[0m
[0;32mI (149778) RS485_LOOPBACK: ⏱️  总耗时: 29 ms[0m
[0;32mI (149778) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (149788) RS485_LOOPBACK: 📊 统计: 50/50 成功 (100.0%)[0m
[0;32mI (149788) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (152798) RS485_LOOPBACK: === 第51次回环测试 ===[0m
[0;32mI (152798) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (152798) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (152798) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (152818) RS485_LOOPBACK: 📥 接收到 5 字节，总计 5/5[0m
[0;32mI (152818) RS485_LOOPBACK: 📥 接收完成: "HELLO" (5字节)[0m
[0;32mI (152818) RS485_LOOPBACK: ⏱️  总耗时: 19 ms[0m
[0;32mI (152818) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (152828) RS485_LOOPBACK: 📊 统计: 51/51 成功 (100.0%)[0m
[0;32mI (152828) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (155838) RS485_LOOPBACK: === 第52次回环测试 ===[0m
[0;32mI (155838) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (155838) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (155848) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (155868) RS485_LOOPBACK: 📥 接收到 10 字节，总计 10/10[0m
[0;32mI (155868) RS485_LOOPBACK: 📥 接收完成: "RS485_TEST" (10字节)[0m
[0;32mI (155868) RS485_LOOPBACK: ⏱️  总耗时: 29 ms[0m
[0;32mI (155868) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (155878) RS485_LOOPBACK: 📊 统计: 52/52 成功 (100.0%)[0m
[0;32mI (155878) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (158888) RS485_LOOPBACK: === 第53次回环测试 ===[0m
[0;32mI (158888) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (158888) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (158898) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (158918) RS485_LOOPBACK: 📥 接收到 11 字节，总计 11/11[0m
[0;32mI (158918) RS485_LOOPBACK: 📥 接收完成: "LOOPBACK_OK" (11字节)[0m
[0;32mI (158918) RS485_LOOPBACK: ⏱️  总耗时: 29 ms[0m
[0;32mI (158918) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (158928) RS485_LOOPBACK: 📊 统计: 53/53 成功 (100.0%)[0m
[0;32mI (158928) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (161938) RS485_LOOPBACK: === 第54次回环测试 ===[0m
[0;32mI (161938) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (161938) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (161948) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (161968) RS485_LOOPBACK: 📥 接收到 13 字节，总计 13/13[0m
[0;32mI (161968) RS485_LOOPBACK: 📥 接收完成: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (161968) RS485_LOOPBACK: ⏱️  总耗时: 29 ms[0m
[0;32mI (161968) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (161978) RS485_LOOPBACK: 📊 统计: 54/54 成功 (100.0%)[0m
[0;32mI (161978) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (164988) RS485_LOOPBACK: === 第55次回环测试 ===[0m
[0;32mI (164988) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (164988) RS485_LOOPBACK: 📤 发送: 16/16 字节[0m
[0;32mI (164998) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (165018) RS485_LOOPBACK: 📥 接收到 16 字节，总计 16/16[0m
[0;32mI (165018) RS485_LOOPBACK: 📥 接收完成: "0123456789ABCDEF" (16字节)[0m
[0;32mI (165018) RS485_LOOPBACK: ⏱️  总耗时: 29 ms[0m
[0;32mI (165018) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (165028) RS485_LOOPBACK: 📊 统计: 55/55 成功 (100.0%)[0m
[0;32mI (165028) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (168038) RS485_LOOPBACK: === 第56次回环测试 ===[0m
[0;32mI (168038) RS485_LOOPBACK: 测试消息: "HELLO" (5字节)[0m
[0;32mI (168038) RS485_LOOPBACK: 📤 发送: 5/5 字节[0m
[0;32mI (168038) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (168058) RS485_LOOPBACK: 📥 接收到 5 字节，总计 5/5[0m
[0;32mI (168058) RS485_LOOPBACK: 📥 接收完成: "HELLO" (5字节)[0m
[0;32mI (168058) RS485_LOOPBACK: ⏱️  总耗时: 19 ms[0m
[0;32mI (168058) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (168068) RS485_LOOPBACK: 📊 统计: 56/56 成功 (100.0%)[0m
[0;32mI (168068) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (171078) RS485_LOOPBACK: === 第57次回环测试 ===[0m
[0;32mI (171078) RS485_LOOPBACK: 测试消息: "RS485_TEST" (10字节)[0m
[0;32mI (171078) RS485_LOOPBACK: 📤 发送: 10/10 字节[0m
[0;32mI (171088) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (171108) RS485_LOOPBACK: 📥 接收到 10 字节，总计 10/10[0m
[0;32mI (171108) RS485_LOOPBACK: 📥 接收完成: "RS485_TEST" (10字节)[0m
[0;32mI (171108) RS485_LOOPBACK: ⏱️  总耗时: 29 ms[0m
[0;32mI (171108) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (171118) RS485_LOOPBACK: 📊 统计: 57/57 成功 (100.0%)[0m
[0;32mI (171118) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (174128) RS485_LOOPBACK: === 第58次回环测试 ===[0m
[0;32mI (174128) RS485_LOOPBACK: 测试消息: "LOOPBACK_OK" (11字节)[0m
[0;32mI (174128) RS485_LOOPBACK: 📤 发送: 11/11 字节[0m
[0;32mI (174138) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (174158) RS485_LOOPBACK: 📥 接收到 11 字节，总计 11/11[0m
[0;32mI (174158) RS485_LOOPBACK: 📥 接收完成: "LOOPBACK_OK" (11字节)[0m
[0;32mI (174158) RS485_LOOPBACK: ⏱️  总耗时: 29 ms[0m
[0;32mI (174158) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (174168) RS485_LOOPBACK: 📊 统计: 58/58 成功 (100.0%)[0m
[0;32mI (174168) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (177178) RS485_LOOPBACK: === 第59次回环测试 ===[0m
[0;32mI (177178) RS485_LOOPBACK: 测试消息: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (177178) RS485_LOOPBACK: 📤 发送: 13/13 字节[0m
[0;32mI (177188) RS485_LOOPBACK: ✅ 发送完成，开始接收...[0m
[0;32mI (177208) RS485_LOOPBACK: 📥 接收到 13 字节，总计 13/13[0m
[0;32mI (177208) RS485_LOOPBACK: 📥 接收完成: "ESP32P4_HVD75" (13字节)[0m
[0;32mI (177208) RS485_LOOPBACK: ⏱️  总耗时: 29 ms[0m
[0;32mI (177208) RS485_LOOPBACK: ✅ 回环测试成功！数据完全匹配[0m
[0;32mI (177218) RS485_LOOPBACK: 📊 统计: 59/59 成功 (100.0%)[0m
[0;32mI (177218) RS485_LOOPBACK: ⏳ 等待3秒后进行下一次测试...[0m

[0;32mI (180228) RS485_LOOPBACK: === 第60次回环测试 ===[0m
[0;32mI (180228) RS485_LOOPBACK: 测试消息: "0123456789ABCDEF" (16字节)[0m
[0;32mI (1