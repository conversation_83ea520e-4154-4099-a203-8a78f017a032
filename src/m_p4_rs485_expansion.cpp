/**
 * ESP32-P4 水质监测系统 - TTL转RS485扩展板实现
 */

#include "m_p4_rs485_expansion.h"
#include "m_p4_rs485.h"  // 包含RS485_BUFFER_SIZE定义
#include "esp_log.h"
#include "esp_timer.h"
#include "driver/gpio.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include <string.h>
#include <sys/time.h>

// 统一日志输出函数（带时间戳）
static void log_with_timestamp(const char* level, const char* format, ...) {
    struct timeval tv;
    gettimeofday(&tv, NULL);
    struct tm* timeinfo = localtime(&tv.tv_sec);

    printf("[%02d:%02d:%02d.%03ld] %s: ",
           timeinfo->tm_hour, timeinfo->tm_min, timeinfo->tm_sec,
           tv.tv_usec / 1000, level);

    va_list args;
    va_start(args, format);
    vprintf(format, args);
    va_end(args);
    printf("\n");
}

static const char* TAG = "RS485_EXP";

// ==================== 全局变量定义 ====================
rs485_expansion_status_t g_rs485_expansion_status;
static rs485_expansion_config_t current_config;

// ==================== 私有函数声明 ====================
static esp_err_t configure_uart_for_expansion(const rs485_expansion_config_t* config);
static void update_expansion_stats(bool tx, bool success);

// ==================== 公共函数实现 ====================

esp_err_t rs485_expansion_init(const rs485_expansion_config_t* config) {
    log_with_timestamp("RS485-EXP", "初始化TTL转RS485扩展板: %s", RS485_EXPANSION_MODEL);

    if (g_rs485_expansion_status.initialized) {
        log_with_timestamp("RS485-EXP", "⚠️ 扩展板已经初始化，检查UART驱动状态...");
        // 检查UART驱动是否真的安装了
        if (uart_is_driver_installed(config->uart_num)) {
            log_with_timestamp("RS485-EXP", "✅ UART%d驱动正常，跳过重新初始化", config->uart_num);
            return ESP_OK;
        } else {
            log_with_timestamp("RS485-EXP", "❌ UART%d驱动未安装，需要重新初始化", config->uart_num);
            // 重置初始化标志，强制重新初始化
            g_rs485_expansion_status.initialized = false;
        }
    }

    // 验证配置参数
    if (!config) {
        log_with_timestamp("RS485-EXP", "❌ 配置参数为空");
        return ESP_ERR_INVALID_ARG;
    }

    if (!RS485_EXPANSION_IS_BAUDRATE_VALID(config->baudrate)) {
        log_with_timestamp("RS485-EXP", "❌ 波特率 %lu 超出支持范围 [%d, %d]",
                config->baudrate, RS485_EXP_BAUDRATE_MIN, RS485_EXP_BAUDRATE_MAX);
        return ESP_ERR_INVALID_ARG;
    }
    
    // 保存配置
    memcpy(&current_config, config, sizeof(rs485_expansion_config_t));
    
    // 配置UART
    esp_err_t ret = configure_uart_for_expansion(config);
    if (ret != ESP_OK) {
        log_with_timestamp("RS485-EXP", "❌ UART配置失败: %s", esp_err_to_name(ret));
        log_with_timestamp("RS485-EXP", "   检查GPIO%d(TX)和GPIO%d(RX)引脚配置", config->tx_pin, config->rx_pin);
        log_with_timestamp("RS485-EXP", "   确认3.3V供电正常 (40PIN右侧第1针)");
        return ret;
    }
    
    // 初始化状态
    memset(&g_rs485_expansion_status, 0, sizeof(rs485_expansion_status_t));
    g_rs485_expansion_status.initialized = true;
    g_rs485_expansion_status.power_on = true;
    g_rs485_expansion_status.baudrate = config->baudrate;
    g_rs485_expansion_status.last_activity = esp_timer_get_time();
    
    log_with_timestamp("RS485-EXP", "✅ 扩展板初始化成功");
    log_with_timestamp("RS485-EXP", "   UART端口: %d", config->uart_num);
    log_with_timestamp("RS485-EXP", "   TX引脚: GPIO%d", config->tx_pin);
    log_with_timestamp("RS485-EXP", "   RX引脚: GPIO%d", config->rx_pin);
    log_with_timestamp("RS485-EXP", "   波特率: %lu bps", config->baudrate);
    log_with_timestamp("RS485-EXP", "   自动换向: 启用 (无需RTS控制)");
    log_with_timestamp("RS485-EXP", "   电气隔离: 启用 (2500V隔离)");
    
    return ESP_OK;
}

esp_err_t rs485_expansion_deinit(void) {
    if (!g_rs485_expansion_status.initialized) {
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "反初始化TTL转RS485扩展板");
    
    // 删除UART驱动
    esp_err_t ret = uart_driver_delete(current_config.uart_num);
    if (ret != ESP_OK) {
        ESP_LOGW(TAG, "删除UART驱动失败: %s", esp_err_to_name(ret));
    }
    
    // 重置状态
    memset(&g_rs485_expansion_status, 0, sizeof(rs485_expansion_status_t));
    memset(&current_config, 0, sizeof(rs485_expansion_config_t));
    
    ESP_LOGI(TAG, "扩展板反初始化完成");
    return ESP_OK;
}

void rs485_expansion_get_default_config(rs485_expansion_config_t* config) {
    if (!config) return;

    config->uart_num = UART_NUM_2;
    config->tx_pin = 20;  // ESP32-P4 TX -> 扩展板 RX (GPIO20) - 最佳GPIO，纯通用IO
    config->rx_pin = 21;  // ESP32-P4 RX <- 扩展板 TX (GPIO21) - 最佳GPIO，纯通用IO
    config->baudrate = RS485_EXP_BAUDRATE_DEFAULT;
    config->data_bits = UART_DATA_8_BITS;
    config->stop_bits = UART_STOP_BITS_1;
    config->parity = UART_PARITY_DISABLE;
    config->flow_control = false; // 扩展板自动换向，无需流控
}

bool rs485_expansion_is_healthy(void) {
    if (!g_rs485_expansion_status.initialized) {
        return false;
    }
    
    // 检查通信状态
    if (!g_rs485_expansion_status.communication_ok) {
        return false;
    }
    
    // 检查错误率
    uint32_t total_ops = g_rs485_expansion_status.tx_count + g_rs485_expansion_status.rx_count;
    if (total_ops > 100) {
        float error_rate = (float)g_rs485_expansion_status.error_count / total_ops;
        if (error_rate > 0.1) { // 错误率超过10%
            return false;
        }
    }
    
    return true;
}

rs485_expansion_status_t rs485_expansion_get_status(void) {
    return g_rs485_expansion_status;
}

esp_err_t rs485_expansion_set_baudrate(uint32_t baudrate) {
    if (!g_rs485_expansion_status.initialized) {
        ESP_LOGE(TAG, "扩展板未初始化");
        return ESP_ERR_INVALID_STATE;
    }
    
    if (!RS485_EXPANSION_IS_BAUDRATE_VALID(baudrate)) {
        ESP_LOGE(TAG, "波特率 %lu 超出支持范围", baudrate);
        return ESP_ERR_INVALID_ARG;
    }
    
    ESP_LOGI(TAG, "设置波特率: %lu -> %lu", current_config.baudrate, baudrate);
    
    esp_err_t ret = uart_set_baudrate(current_config.uart_num, baudrate);
    if (ret == ESP_OK) {
        current_config.baudrate = baudrate;
        g_rs485_expansion_status.baudrate = baudrate;
        ESP_LOGI(TAG, "波特率设置成功");
    } else {
        ESP_LOGE(TAG, "波特率设置失败: %s", esp_err_to_name(ret));
    }
    
    return ret;
}

esp_err_t rs485_expansion_test_communication(void) {
    if (!g_rs485_expansion_status.initialized) {
        return ESP_ERR_INVALID_STATE;
    }
    
    log_with_timestamp("RS485-EXP", "测试扩展板通信...");

    // 发送测试数据
    const char test_data[] = "RS485_TEST";
    int written = uart_write_bytes(current_config.uart_num, test_data, strlen(test_data));

    if (written > 0) {
        update_expansion_stats(true, true);
        g_rs485_expansion_status.communication_ok = true;
        log_with_timestamp("RS485-EXP", "✅ 通信测试成功，发送 %d 字节", written);
        return ESP_OK;
    } else {
        update_expansion_stats(true, false);
        g_rs485_expansion_status.communication_ok = false;
        log_with_timestamp("RS485-EXP", "❌ 通信测试失败");
        log_with_timestamp("RS485-EXP", "   检查扩展板供电和引脚连接");
        log_with_timestamp("RS485-EXP", "   GPIO20(TX) -> 扩展板RX, GPIO21(RX) -> 扩展板TX");
        return ESP_FAIL;
    }
}

void rs485_expansion_print_info(void) {
    ESP_LOGI(TAG, "==================== 扩展板信息 ====================");
    ESP_LOGI(TAG, "型号: %s", RS485_EXPANSION_MODEL);
    ESP_LOGI(TAG, "尺寸: %s mm", RS485_EXPANSION_SIZE_MM);
    ESP_LOGI(TAG, "特性: 隔离型, 自动换向, 高速传输");
    ESP_LOGI(TAG, "电压: 3.3V/5V兼容");
    ESP_LOGI(TAG, "最大波特率: %d bps", RS485_EXP_BAUDRATE_MAX);
    
    if (g_rs485_expansion_status.initialized) {
        ESP_LOGI(TAG, "==================== 运行状态 ====================");
        ESP_LOGI(TAG, "初始化: %s", g_rs485_expansion_status.initialized ? "是" : "否");
        ESP_LOGI(TAG, "电源: %s", g_rs485_expansion_status.power_on ? "正常" : "异常");
        ESP_LOGI(TAG, "通信: %s", g_rs485_expansion_status.communication_ok ? "正常" : "异常");
        ESP_LOGI(TAG, "当前波特率: %lu bps", g_rs485_expansion_status.baudrate);
        ESP_LOGI(TAG, "发送计数: %lu", g_rs485_expansion_status.tx_count);
        ESP_LOGI(TAG, "接收计数: %lu", g_rs485_expansion_status.rx_count);
        ESP_LOGI(TAG, "错误计数: %lu", g_rs485_expansion_status.error_count);
        
        uint32_t total_ops = g_rs485_expansion_status.tx_count + g_rs485_expansion_status.rx_count;
        if (total_ops > 0) {
            float success_rate = (float)(total_ops - g_rs485_expansion_status.error_count) / total_ops * 100;
            ESP_LOGI(TAG, "成功率: %.1f%%", success_rate);
        }
    }
    ESP_LOGI(TAG, "================================================");
}

void rs485_expansion_reset_stats(void) {
    g_rs485_expansion_status.tx_count = 0;
    g_rs485_expansion_status.rx_count = 0;
    g_rs485_expansion_status.error_count = 0;
    ESP_LOGI(TAG, "扩展板统计信息已重置");
}

// ==================== 私有函数实现 ====================

static esp_err_t configure_uart_for_expansion(const rs485_expansion_config_t* config) {
    log_with_timestamp("RS485-EXP", "🔧 开始配置UART用于扩展板...");
    log_with_timestamp("RS485-EXP", "   UART端口: %d", config->uart_num);
    log_with_timestamp("RS485-EXP", "   TX引脚: GPIO%d", config->tx_pin);
    log_with_timestamp("RS485-EXP", "   RX引脚: GPIO%d", config->rx_pin);
    log_with_timestamp("RS485-EXP", "   波特率: %lu", config->baudrate);

    // 等待扩展板稳定
    log_with_timestamp("RS485-EXP", "⏳ 等待扩展板稳定...");
    vTaskDelay(pdMS_TO_TICKS(1000));  // 等待1秒

    // GPIO20/21无需特殊处理，它们是纯通用IO引脚

    // 🎯 ESP32-P4最佳GPIO配置：GPIO20/21
    // ================================================================================
    // 根据ESP32-P4数据手册详细分析，GPIO20/21是最优选择：
    // - GPIO20: 纯通用IO，无ADC/SPI/USB/UART等功能冲突
    // - GPIO21: 纯通用IO，无ADC/SPI/USB/UART等功能冲突
    // - 通过GPIO交换矩阵可以完美配置为任意UART功能
    // ================================================================================

    log_with_timestamp("RS485-EXP", "🎯 ESP32-P4最佳GPIO配置：GPIO%d(TX)/GPIO%d(RX) -> UART%d",
                      config->tx_pin, config->rx_pin, config->uart_num);

    // 验证是否使用推荐的GPIO20/21配置
    if (config->tx_pin == 20 && config->rx_pin == 21) {
        log_with_timestamp("RS485-EXP", "✅ 使用最佳GPIO配置：GPIO20/21 (纯通用IO，无功能冲突)");
    } else {
        log_with_timestamp("RS485-EXP", "⚠️  非最佳GPIO配置，推荐使用GPIO20/21");
    }

    // 重置GPIO为普通IO模式，确保完全清除任何之前的配置
    log_with_timestamp("RS485-EXP", "🔧 重置GPIO%d/GPIO%d为纯净状态...",
                      config->tx_pin, config->rx_pin);

    gpio_reset_pin((gpio_num_t)config->tx_pin);
    gpio_reset_pin((gpio_num_t)config->rx_pin);

    // 确保GPIO完全重置，为UART配置做准备
    vTaskDelay(pdMS_TO_TICKS(100));

    log_with_timestamp("RS485-EXP", "✅ GPIO%d/GPIO%d已重置完成，准备通过GPIO交换矩阵配置为UART功能",
                      config->tx_pin, config->rx_pin);

    // 先配置GPIO为安全状态
    log_with_timestamp("RS485-EXP", "🔧 预配置GPIO引脚...");
    gpio_config_t io_conf = {};

    // 配置TX引脚为输出，初始为高电平（空闲状态）
    io_conf.intr_type = GPIO_INTR_DISABLE;
    io_conf.mode = GPIO_MODE_OUTPUT;
    io_conf.pin_bit_mask = (1ULL << config->tx_pin);
    io_conf.pull_down_en = GPIO_PULLDOWN_DISABLE;
    io_conf.pull_up_en = GPIO_PULLUP_ENABLE;
    esp_err_t ret_gpio = gpio_config(&io_conf);
    if (ret_gpio != ESP_OK) {
        log_with_timestamp("RS485-EXP", "❌ TX引脚配置失败: %s", esp_err_to_name(ret_gpio));
        return ret_gpio;
    }
    gpio_set_level((gpio_num_t)config->tx_pin, 1);  // 设置为高电平（空闲状态）

    // 配置RX引脚为输入，启用上拉
    io_conf.mode = GPIO_MODE_INPUT;
    io_conf.pin_bit_mask = (1ULL << config->rx_pin);
    io_conf.pull_up_en = GPIO_PULLUP_ENABLE;
    ret_gpio = gpio_config(&io_conf);
    if (ret_gpio != ESP_OK) {
        log_with_timestamp("RS485-EXP", "❌ RX引脚配置失败: %s", esp_err_to_name(ret_gpio));
        return ret_gpio;
    }

    log_with_timestamp("RS485-EXP", "✅ GPIO预配置完成");
    vTaskDelay(pdMS_TO_TICKS(500));  // 再等待500ms

    // UART配置结构
    uart_config_t uart_config;
    memset(&uart_config, 0, sizeof(uart_config_t));
    uart_config.baud_rate = (int)config->baudrate;
    uart_config.data_bits = config->data_bits;
    uart_config.parity = config->parity;
    uart_config.stop_bits = config->stop_bits;
    uart_config.flow_ctrl = UART_HW_FLOWCTRL_DISABLE; // 扩展板自动换向，禁用硬件流控
    uart_config.rx_flow_ctrl_thresh = 122;
    uart_config.source_clk = UART_SCLK_DEFAULT;

    log_with_timestamp("RS485-EXP", "📋 配置UART参数...");
    // 配置UART参数
    esp_err_t ret = uart_param_config(config->uart_num, &uart_config);
    if (ret != ESP_OK) {
        log_with_timestamp("RS485-EXP", "❌ UART参数配置失败: %s", esp_err_to_name(ret));
        return ret;
    }
    log_with_timestamp("RS485-EXP", "✅ UART参数配置成功");

    log_with_timestamp("RS485-EXP", "📌 设置UART引脚...");
    log_with_timestamp("RS485-EXP", "   UART%d: TX=GPIO%d, RX=GPIO%d",
                      config->uart_num, config->tx_pin, config->rx_pin);

    // ESP32-P4关键：确保避开UART0(GPIO37/38)和UART1(GPIO10/11)
    if (config->uart_num == UART_NUM_0) {
        log_with_timestamp("RS485-EXP", "⚠️  警告：UART0是烧录串口，可能导致冲突！");
    } else if (config->uart_num == UART_NUM_1) {
        log_with_timestamp("RS485-EXP", "⚠️  警告：UART1可能与系统功能冲突！");
    } else {
        log_with_timestamp("RS485-EXP", "✅ 使用UART%d，避开了UART0/UART1冲突", config->uart_num);
    }

    // 设置UART引脚（注意：扩展板无需RTS/CTS）
    ret = uart_set_pin(config->uart_num, config->tx_pin, config->rx_pin,
                      UART_PIN_NO_CHANGE, UART_PIN_NO_CHANGE);
    if (ret != ESP_OK) {
        log_with_timestamp("RS485-EXP", "❌ UART引脚设置失败: %s", esp_err_to_name(ret));
        log_with_timestamp("RS485-EXP", "   检查GPIO%d和GPIO%d是否可用", config->tx_pin, config->rx_pin);
        log_with_timestamp("RS485-EXP", "   ESP32-P4 UART0默认: GPIO37/38, UART1默认: GPIO10/11");
        return ret;
    }
    log_with_timestamp("RS485-EXP", "✅ UART引脚设置成功：UART%d通过GPIO Matrix映射到GPIO%d/GPIO%d",
                      config->uart_num, config->tx_pin, config->rx_pin);

    log_with_timestamp("RS485-EXP", "🚀 安装UART驱动...");
    // 安装UART驱动
    ret = uart_driver_install(config->uart_num, RS485_BUFFER_SIZE,
                             RS485_BUFFER_SIZE, 0, NULL, 0);
    if (ret != ESP_OK) {
        log_with_timestamp("RS485-EXP", "❌ UART驱动安装失败: %s", esp_err_to_name(ret));
        return ret;
    }
    log_with_timestamp("RS485-EXP", "✅ UART驱动安装成功");

    log_with_timestamp("RS485-EXP", "🧹 清空UART缓冲区...");
    // 清空缓冲区
    uart_flush(config->uart_num);
    log_with_timestamp("RS485-EXP", "✅ UART配置完成");

    return ESP_OK;
}

static void update_expansion_stats(bool tx, bool success) {
    g_rs485_expansion_status.last_activity = esp_timer_get_time();
    
    if (tx) {
        g_rs485_expansion_status.tx_count++;
    } else {
        g_rs485_expansion_status.rx_count++;
    }
    
    if (!success) {
        g_rs485_expansion_status.error_count++;
    }
}
