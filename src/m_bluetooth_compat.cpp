#include "m_bluetooth_compat.h"

// ESP32-P4暂时不支持蓝牙经典模式，提供空实现
#include <cstring>

static const char* TAG = "BTCompat";

// 静态成员初始化
BluetoothSerial* BluetoothSerial::instance = nullptr;

// 全局蓝牙对象
BluetoothSerial SerialBT;

BluetoothSerial::BluetoothSerial()
    : initialized(false)
    , connected(false)
{
    instance = this;
    ESP_LOGI(TAG, "BluetoothSerial created (empty implementation for ESP32-P4)");
}

BluetoothSerial::~BluetoothSerial() {
    end();
}

BluetoothSerial& BluetoothSerial::getInstance() {
    if (!instance) {
        instance = new BluetoothSerial();
    }
    return *instance;
}

bool BluetoothSerial::begin(String localName, bool isMaster) {
    ESP_LOGI(TAG, "Bluetooth begin called (not supported on ESP32-P4)");
    (void)localName;
    (void)isMaster;
    return false;  // 不支持
}

void BluetoothSerial::end() {
    ESP_LOGI(TAG, "Bluetooth end called");
    initialized = false;
    connected = false;
}

size_t BluetoothSerial::write(uint8_t c) {
    (void)c;
    return 0;  // 不支持写入
}

size_t BluetoothSerial::write(const uint8_t *buffer, size_t size) {
    (void)buffer;
    (void)size;
    return 0;  // 不支持写入
}

int BluetoothSerial::available() {
    return 0;  // 没有可用数据
}

int BluetoothSerial::read() {
    return -1;  // 没有数据
}

int BluetoothSerial::peek() {
    return -1;  // 没有数据
}

void BluetoothSerial::flush() {
    // 空实现
}

bool BluetoothSerial::hasClient() {
    return false;  // 没有客户端连接
}

bool BluetoothSerial::isReady(bool checkMaster, int timeout) {
    (void)checkMaster;
    (void)timeout;
    return false;  // 不就绪
}

String BluetoothSerial::readString() {
    return String("");  // 返回空字符串
}

bool BluetoothSerial::isConnected() {
    return false;  // 未连接
}

// 删除未声明的函数

String BluetoothSerial::readStringUntil(char terminator) {
    (void)terminator;
    return String("");  // 返回空字符串
}

// 删除未声明的函数

// 删除重复定义

bool BluetoothSerial::connect(String remoteName) {
    (void)remoteName;
    ESP_LOGI(TAG, "connect called (not supported on ESP32-P4)");
    return false;
}

bool BluetoothSerial::connect(uint8_t remoteAddress[]) {
    (void)remoteAddress;
    ESP_LOGI(TAG, "connect called (not supported on ESP32-P4)");
    return false;
}

// 删除未声明的connect()函数

// 删除未声明的函数

bool BluetoothSerial::disconnect() {
    ESP_LOGI(TAG, "disconnect called");
    return true;  // 总是成功（因为本来就没连接）
}

// 删除未在头文件中声明的函数

// 简化的Print接口实现，只实现头文件中声明的函数
size_t BluetoothSerial::write(const char* str) {
    (void)str;
    return 0;
}

size_t BluetoothSerial::print(const String& s) {
    (void)s;
    return 0;
}

size_t BluetoothSerial::print(const char* str) {
    (void)str;
    return 0;
}

size_t BluetoothSerial::println(const String& s) {
    (void)s;
    return 0;
}

size_t BluetoothSerial::println(const char* str) {
    (void)str;
    return 0;
}

// 头文件中声明的其他函数
size_t BluetoothSerial::read(uint8_t *buffer, size_t size) {
    (void)buffer;
    (void)size;
    return 0;
}

bool BluetoothSerial::setPin(const char* pin, int len) {
    (void)pin;
    (void)len;
    ESP_LOGI(TAG, "setPin called (not supported on ESP32-P4)");
    return false;
}

bool BluetoothSerial::setPin(const String& pin) {
    (void)pin;
    ESP_LOGI(TAG, "setPin called (not supported on ESP32-P4)");
    return false;
}

bool BluetoothSerial::unpairDevice(uint8_t* address) {
    (void)address;
    ESP_LOGI(TAG, "unpairDevice called (not supported on ESP32-P4)");
    return false;
}

String BluetoothSerial::readStringUntil(char terminator, unsigned long timeout) {
    (void)terminator;
    (void)timeout;
    return String("");
}

esp_err_t BluetoothSerial::register_callback(void * callback) {
    (void)callback;
    ESP_LOGI(TAG, "register_callback called (not supported on ESP32-P4)");
    return ESP_ERR_NOT_SUPPORTED;
}